/*
 * @Description: 服务样式接口
 * @Autor: xyq
 * @Date: 2023-02-16 13:50:50
 * @LastEditors: silei
 * @LastEditTime: 2024-02-22 15:12:44
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
import { BaseSetting } from "geoserver-manager";
// 查询所有网络分析
export const getNetworks = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/network",
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 查询所有网络分析
export const getSpatials = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/serviceManage/vectors",
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 查询所有三维分析
export const getScene3d = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/scene3d",
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};

// 网络分析添加
export const addNetwork = async (data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `/network`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
/**
 * 网络分析修改
 * @returns all
 */
export const modifyNetwork = async (data: any) => {
  const result = await hRequest.put<DataType>({
    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
    url: `/network/${data.pipeInfo.name}`,
    data,
    maxRedirects: 0,
    timeout: 1000
  });
  return result;
};
// 网络分析详情
export const detialNetwork = async (name: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/network/${name}`,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 网络分析删除
export const delNetwork = async (name: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/network/${name}`,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 新增时服务种类
export const getServices = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/serviceManage/vectors",
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 新增时下拉属性
export const getVectorInfo = async (serverName: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `serviceManage/vectorInfo?serviceName=${serverName}`,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 横断面分析
export const networkAnalysis = async (name: string, method: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/network/${method}`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 横断面分析
export const crossAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/network/crossSectional`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 最短路径分析
export const shrotAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/network/shortest`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 上游分析
export const traceUpAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/network/traceup`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 下游分析
export const traceDownAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/network/tracedown`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 缓冲区分析
export const bufferAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/spatial/buffer`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
// 空间关系分析
export const geoRelationAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/spatial/georelation`,
    data,
    maxRedirects: 0,
    timeout: 50000
  });
  return result;
};
export const overlayAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/spatial/overlay`,
    data,
    maxRedirects: 0,
    timeout: 60000
  });
  return result;
};

export const contourAnalysis = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `${BaseSetting.getBaseUrl()}/${name}/rest/spatial/contour`,
    data,
    maxRedirects: 0,
    timeout: 60000
  });
  return result;
};
