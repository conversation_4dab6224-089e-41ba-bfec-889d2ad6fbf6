<!--
 * @Description: 图层列表
 * @Autor: silei
 * @Date: 2023-02-20 16:57:21
 * @LastEditors: silei
 * @LastEditTime: 2023-04-20 09:27:29
-->
<template>
  <el-table
    :data="value"
    class="custom-table lyaer-table border"
    header-row-class-name="custom-header-row"
    header-cell-class-name="custom-header-cell"
    style="width: 97%"
  >
    <el-table-column width="80">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span>{{ scope.$index + 1 }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="图层" width="380">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span>{{ scope.row.name }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="类型" width="200">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span>{{ scope.row.type }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column v-if="styleEnabled" label="风格" width="380">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <el-link class="fontstyle" @click="handleSelectStyle(scope.$index)">{{
            scope.row.style ?? "默认"
          }}</el-link>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="位置">
      <template #default="scope">
        <div class="positonBox">
          <div
            v-if="scope.$index > 0"
            @click="moveUp(scope.$index, scope.row)"
            class="fontstyle item"
          >
            上移
          </div>
          <div
            v-if="scope.$index < value.length - 1"
            @click="moveDown(scope.$index, scope.row)"
            class="fontstyle item"
          >
            下移
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    :before-close="handleClose"
    :close-on-click-modal="false"
    v-model="dialogVisible"
    width="24%"
    title="选择样式"
  >
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择样式</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <StyleSelect v-if="dialogVisible" @selected="handleStyleSelected" />
    <template #footer>
      <span class="custom-dialog-footer">
        <div class="dialog-cancle" @click="resetForm()">取消</div>
        <div class="dialog-submit" @click="submitForm()">确定</div>
      </span>
    </template>
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, PropType, ref } from "vue";
import StyleSelect from "./StyleSelect.vue";
class LayerData {
  public name: string = "";
  public style: string = "";
}
const props = defineProps({
  modelValue: {
    type: Object as PropType<LayerData[]>,
    default: [] as LayerData[]
  },
  styleEnabled: {
    type: Boolean,
    default: true
  }
});
const resetForm = () => {
  dialogVisible.value = false;
};
const submitForm = () => {
  dialogVisible.value = false;
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue[currentLayer].style = selectValue.value;
  emits("update:modelValue", props.modelValue);
};
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {}
});
const moveUp = (index: number, data: LayerData) => {
  if (index > 0) {
    const preLayer = props.modelValue[index - 1];
    value.value.splice(index - 1, 2, ...[data, preLayer]);
    emits("update:modelValue", props.modelValue);
  }
};
const moveDown = (index: number, data: LayerData) => {
  if (index < value.value.length - 1) {
    const next = props.modelValue[index + 1];
    value.value.splice(index, 2, ...[next, data]);
    emits("update:modelValue", props.modelValue);
  }
};
const handleClose = () => {
  dialogVisible.value = false;
};
const dialogVisible = ref(false);
let currentLayer = -1;
const handleSelectStyle = (index: number) => {
  dialogVisible.value = true;
  currentLayer = index;
};
const selectValue = ref("");
const handleStyleSelected = (style: any) => {
  selectValue.value = style.name;
};
</script>
<style lang="scss" scoped>
.fontstyle {
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  color: #4076f3;
}
.positonBox {
  width: 100%;
  display: flex;
  .item {
    width: 70px;
    height: 36px;
    line-height: 36px;
    cursor: pointer;
  }
}
.test {
  min-height: 300px;
}
.lyaer-table {
  margin-left: 20px;
  height: 280px;
}
.border {
  border: 1px solid #e6eaef;
  border-radius: 4px;
}
</style>
