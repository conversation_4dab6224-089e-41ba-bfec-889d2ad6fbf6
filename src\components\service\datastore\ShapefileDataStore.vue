<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-14 11:00:11
 * @LastEditors: silei
 * @LastEditTime: 2023-04-23 10:10:16
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="path" label="shapefile文件目录">
          <el-input v-model="value.path" class="file-input" placeholder="请选择文件目录">
          </el-input>
          <el-button @click="handleSelect">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="charset" label="shapefile文件编码">
          <el-select v-model="value.charset" placeholder="选择文件编码">
            <el-option v-for="(item, index) in Charset" :key="index" :label="item" :value="item" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect choose-type="dir" v-if="dialogVisible" @file-selected="handleFileSelected" />
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, PropType, reactive, ref } from "vue";
import { Charset } from "geoserver-manager";
import { FormRules } from "element-plus";
import FileSelect from "@/components/FileSelect.vue";
const rules = reactive<FormRules>({
  path: [{ required: true, message: "请选择文件目录", trigger: "blur" }],
  charset: [{ required: true, message: "请选择文件编码", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        path: "",
        charset: ""
      };
    }
  }
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const dialogVisible = ref(false);
const handleSelect = () => {
  dialogVisible.value = true;
};
const handleFileSelected = (fileName: string) => {
  dialogVisible.value = false;
  value.value.path = `${fileName}`;
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
<style lang="scss" scoped></style>
