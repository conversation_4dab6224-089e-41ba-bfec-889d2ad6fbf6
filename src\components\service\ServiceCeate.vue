<!--
 * @Description: 服务创建
 * @Autor: silei
 * @Date: 2023-02-02 11:06:08
 * @LastEditors: silei
 * @LastEditTime: 2023-11-21 14:21:19
-->
<template>
  <div>
    <div>
      <component ref="componentRef" :is="currStep.component" />
    </div>
    <el-row class="custom-dialog-footer create-footer">
      <el-button :loading="btnLoading" class="dialog-cancle" @click="cancel">取消</el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== 'datasource'"
        class="dialog-submit"
        @click="getPreStep"
      >
        上一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== 'details'"
        class="dialog-submit"
        @click="getNextStep"
      >
        下一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name === 'details'"
        class="dialog-submit"
        @click="createService"
      >
        完成
      </el-button>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import { ServiceUtil, DataSourceFactory } from "geoserver-manager";
import { PropType, Ref, ref } from "vue";

import { setScoped } from "@/utils/basic";
import ServiceInfo from "./serviceCreate/ServiceInfo.vue";
import DataSource from "./serviceCreate/DataSource.vue";
import { ElMessage } from "element-plus";
import LayerSelect from "./serviceCreate/LayerSelect.vue";
import { OperatorStep } from "./type";
const props = defineProps<{
  step: OperatorStep;
}>();
// 当前步骤
const currStep: Ref<OperatorStep> = ref(props.step);
const data = ref<any>();
const emits = defineEmits(["canceled", "completed"]);
const layers = ref<string[] | null>(null);
const componentRef = ref();
/**
 * 获取下一步
 */
const getNextStep = async () => {
  // 提交信息
  const info = await componentRef.value.submitForm();
  if (info) {
    data.value = {
      ...data.value,
      ...info
    };
    // 选择数据源
    const nextStep = currStep.value.getNextStep();
    if (nextStep) {
      currStep.value = nextStep;
    }
  }
};
/**
 * 获取上一步
 */
const getPreStep = () => {
  const preStep = currStep.value.getPreStep();
  if (preStep) {
    currStep.value = preStep;
  }
};
const btnLoading = ref(false);
/**
 * 创建服务
 */
const createService = async () => {
  btnLoading.value = true;
  try {
    await ServiceUtil.createService(
      {
        ...data.value
      },
      DataSourceFactory.getDataSource(data.value.datasource).getServiceType() as any
    );
    emits("completed");
    ElMessage({
      message: "创建成功",
      type: "success"
    });
    setScoped(`服务新增-${data.value.name}`);
  } finally {
    btnLoading.value = false;
  }
};
const cancel = () => {
  emits("canceled");
};
</script>
<style scoped lang="scss">
.create-footer {
  margin: 20px 0 0 0;
}
</style>
