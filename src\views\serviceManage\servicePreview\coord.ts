/*
 * @Description: 坐标转换
 * @Autor: silei
 * @Date: 2023-05-26 16:33:29
 * @LastEditors: silei
 * @LastEditTime: 2023-06-19 18:01:21
 */

import { Cartesian3, Matrix4, Transforms } from "cesium";

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class Coord {
  /**
   * 根据本地坐标获取笛卡尔坐标
   * @param position 本地坐标
   * @returns
   */
  static getCartesian(position: any) {
    // 获取本地坐标
    const matrix = Transforms.eastNorthUpToFixedFrame(
      Cartesian3.fromDegrees(119.38196790804783, 35.215246834277956)
    );
    const oldP = new Cartesian3(position.x, position.y, position.z);
    const newP = Matrix4.multiplyByPoint(matrix, oldP, oldP);
    return newP;
  }

  /**
   * 根据笛卡尔坐标获取本地坐标
   * @param position 笛卡尔坐标
   * @returns
   */
  // static getLocationPosition(position: any) {
  //   const matrix = BC.Namespace.Cesium.Transforms.eastNorthUpToFixedFrame(
  //     BC.Cartesian3.fromDegrees(119.38196790804783, 35.215246834277956)
  //   );
  //   BC.Matrix4.inverse(matrix, matrix);
  //   const oldP = new BC.Cartesian3(position.x, position.y, position.z);
  //   const newP = BC.Matrix4.multiplyByPoint(matrix, oldP, oldP);
  //   return newP;
  // }
}
