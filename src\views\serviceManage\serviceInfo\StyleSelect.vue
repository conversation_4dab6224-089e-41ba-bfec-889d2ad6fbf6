<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-21 16:31:16
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-27 10:31:36
-->
<template>
  <div>
    <div class="top">
      <el-input v-model="filterName" placeholder="请输入" @change="handleFilter" />
      <img @click="handleFilter" src="../../../assets/img/search.png" alt="" />
    </div>
    <el-row>
      <div class="title"><span>样式名称</span></div>
    </el-row>
    <div class="select-box">
      <el-row v-for="(style, index) in pageStyles" :key="index" @click="handleSelect(style)">
        <div class="styleItem" :class="[active === style.name ? 'activeStyle' : '']">
          <span>{{ style.name }}</span>
        </div>
      </el-row>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        layout="prev, pager, next"
        :total="pageInfo.total"
        background
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { getStyles } from "@/api/style";
import { onMounted, Ref, ref } from "vue";
class StyleInfo {
  name: string = "";
  href?: string = "";
}
const pageInfo = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});
const filterName = ref("");
const styles: Ref<StyleInfo[]> = ref([]);
const pageStyles: Ref<StyleInfo[]> = ref([]);
const handleCurrentChange = (val: number) => {
  const pageIndex = val - 1;
  // 查询页码
  pageStyles.value = styles.value.filter((style) => style.name.includes(filterName.value));
  pageInfo.value.total = pageStyles.value.length;
  pageStyles.value = pageStyles.value.filter((service, index) => {
    if (
      index >= pageIndex * pageInfo.value.pageSize &&
      index < (pageIndex + 1) * pageInfo.value.pageSize
    ) {
      return true;
    }
    return false;
  });
};
const handleFilter = () => {
  handleCurrentChange(1);
};
const active = ref("");
const emits = defineEmits(["selected"]);
const handleSelect = (style: StyleInfo) => {
  active.value = style.name;
  emits("selected", style);
};
onMounted(async () => {
  // 获取样式列表
  const data = await getStyles();
  styles.value = data;
  pageInfo.value.total = styles.value.length;
  handleCurrentChange(1);
});
</script>
<style lang="scss" scoped>
.title {
  width: 100%;
  height: 36px;
  background: #fafafa;
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  text-align: left;
  color: #232a3a;
  line-height: 36px;
  border-bottom: 1px solid #e6eaef;
  margin: 15px 0 0 0;
  span {
    margin-left: 20px;
  }
}
.styleItem {
  cursor: pointer;
  width: 100%;
  height: 36px;
  background: #ffffff;
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: left;
  color: #232a3a;
  line-height: 36px;
  border-bottom: 1px solid #e6eaef;
  span {
    margin-left: 20px;
  }
}
.styleItem:hover {
  background: #f4f7fa;
  color: #4076f3;
}
.activeStyle {
  background: #f4f7fa;
  color: #4076f3;
}
.pagination {
  width: 100%;
  margin: 15px 0 10px 0;
}
.top {
  position: relative;
  width: 100%;
  img {
    cursor: pointer;
    width: 16px;
    height: 16px;
    position: absolute;
    right: 10px;
    top: 7px;
  }
}
.select-box {
  height: 300px;
  overflow: auto;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
