// html,
// body,
// #app {
//   font-size: 16px;
//   box-sizing: border-box;
// }
.custom-table-header-row {
  margin: 25px 0 23px 0;
  height: 50px;
  display: flex;
  justify-content: space-between;
}
// 右侧容器
.custom-content {
  width: 96%;
  margin-left: 2%;
}
// 表格tile
.custom-table-title {
  height: 41px;
  font-size: 26px;
  font-family: Source <PERSON>, Source Han Sans CN-Bold;
  font-weight: 700;
  text-align: left;
  color: #232a3a;
  line-height: 22px;
}
.custom-button-group{
  width: fit-content;
  display: flex;
}
// 表格 头部按钮
.custom-table-add {
  cursor: pointer;
  width: 140px;
  height: 44px;
  background: linear-gradient(227deg, #5eafff 2%, #4076f3 98%);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 24px;
    height: 24px;
    margin: 0 6px 0 0;
  }
  span {
    height: 27px;
    font-size: 16px;
    font-family: Source <PERSON>, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #ffffff;
  }
}
.custom-table-wihte {
  cursor: pointer;
  width: 177px;
  height: 44px;
  background: #ffffff;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px 0 0;
  img {
    width: 18px;
    height: 18px;
    margin: 0 6px 0 0;
  }

  span {
    height: 27px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #35465b;
    line-height: 25px;
  }
}
.custom-table-addBlue{
  cursor: pointer;
  width: 160px;
  height: 44px;
  background: linear-gradient(227deg, #5eafff 2%, #4076f3 98%);
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 24px;
    height: 24px;
    margin: 0 6px 0 0px;
  }

  span {
    height: 27px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #ffffff;
  }
}
// 表格盒子
.custom-table-box {
  width: 100%;
  height: 761px;
  background: #ffffff;
  border-radius: 4px;
}
.custom-table-search-box {
  display: flex;
  align-items: center;
  width: 100%;
  height: 120px;
  border-bottom: 1px solid #e6eaef;
}
.custom-search-item {
  width: 320px;
  margin-left: 30px;
  .el-form-item .el-form-item__label {
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: #000000;
  }
}
.custom-query-button {
  cursor: pointer;
  width: 80px;
  height: 34px;
  background: #4076f3;
  border-radius: 2px;
  font-size: 14px;
  font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: #ffffff;
  line-height: 34px;
  margin: 25px 10px 0 0;
}
.custom-reset-button {
  cursor: pointer;
  width: 80px;
  height: 34px;
  margin: 25px 10px 0 0;
  line-height: 34px;
  background: #ffffff;
  border: 1px solid #d7d7d7;
  border-radius: 2px;
  font-size: 14px;
  font-family: Microsoft YaHei, Microsoft YaHei-Regular;
  font-weight: 400;
  text-align: center;
  color: rgba(0, 0, 0, 0.75);
}
.custom-table {
  --el-table-border-color: #ebebeb;
  --el-table-row-hover-bg-color: #f6fbff;
  --el-table-header-text-color: #000000;
  margin: 10px 2% 0 2%;
  height: 539px;
  .custom-header-row {
    height: 48px;
    --el-table-header-bg-color: #fafafa;

    // --el-table-border-color: #fafafa;
    .is-leaf.custom-header-cell {
      border-bottom-color: #fafafa;
    }

    .cell {
      font-size: 14px;
      font-weight: 700;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
    }
  }

  .el-table__row {
    .cell {
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: rgba(0, 0, 0, 0.75);
    }
  }
}
// 分页
.custom-pagination-box {
  margin-top: 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
}

// 提交表单
.custom-sub-form {
  .el-form-item__label {
    color: #000000;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
  }
}

// 弹窗头
.custom-dialog-header {
  margin: 0 15px;
  width: 100%;
  display: flex;
  img {
    width: 24px;
    height: 24px;
    margin: 0 10px 0 0;
  }
}

.custom-split-line {
  width: 110%;
  margin-left: -30px;
  height: 1px;
  background: #ebebeb;
  margin-bottom: 20px;
}

// 弹窗footer
.custom-dialog-footer {
  width: 100%;
  display: flex;
  height: 50px;
  justify-content: flex-end;
  .dialog-cancle {
    cursor: pointer;
    width: 86px;
    height: 34px;
    background: #ffffff;
    border: 1px solid #cdd3dc;
    border-radius: 2px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: center;
    color: #4b5970;
    line-height: 34px;
    margin: 0 5px;
  }
  .dialog-submit {
    cursor: pointer;
    width: 86px;
    height: 34px;
    background: #4076f3;
    border-radius: 2px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    line-height: 34px;
    margin: 0 5px;
  }
}
// select
.custom-dialog-select {
  width: 100%;
  overflow: hidden;
  .el-select__tags-text {
    display: inline-block;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .el-select .el-tag__close.el-icon-close {
    top: -7px;
    right: -4px;
  }
}
.file-input {
  width: 80%;
  margin-right: 20px;
}
