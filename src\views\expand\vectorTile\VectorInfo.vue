<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    :disabled="editable"
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="名称" prop="name">
          <el-input class="" v-model.trim="formData.name" placeholder="请输入名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="样式设置" prop="config.style">
          <el-select
            class="custom-dialog-select"
            v-model="formData.config.style"
            placeholder="请选择样式设置"
            clearable
          >
            <el-option
              v-for="item in styleData"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="最大缩放级别" prop="config.max">
          <el-input
            type="number"
            v-model.trim="formData.config.max"
            placeholder="请输入最大缩放级别"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="最小缩放级别" prop="config.min">
          <el-input
            type="number"
            v-model.trim="formData.config.min"
            placeholder="请输入最小缩放级别"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="东北角" prop="config.east">
          <el-input class="" v-model.trim="formData.config.east" placeholder="请输入东北角经纬度" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="西南角" prop="config.west">
          <el-input class="" v-model.trim="formData.config.west" placeholder="请输入西南角经纬度" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="描述" prop="remark">
      <el-input
        class="custom-textarea"
        placeholder="请输入描述"
        v-model.trim="formData.remark"
        type="textarea"
        resize="none"
        :rows="3"
        maxlength="500"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch } from "vue";
import { getStyles } from "@/api/style";
const props = defineProps({
  formData: Object,
  editable: Boolean
});
const formData: any = ref(props.formData);
const editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);

const validateCoordinates = (rule: any, value: any, callback: any) => {
  const COORDINATE_REGEX =
    /^(-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?)\s*,\s*(-?\d+(?:\.\d+)?(?:[eE][+-]?\d+)?)$/;
  if (COORDINATE_REGEX.test(value)) {
    return callback();
  }
  // 返回一个错误提示
  callback(new Error("坐标格式不正确，请使用 '经度,纬度' 格式，如：116.404,39.915"));
};
const rules = {
  name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  config: {
    max: [
      { required: true, message: "请输入最大缩放级别", trigger: "blur" },
      {
        validator: (_, value, callback) => {
          if (isNaN(Number(value))) {
            callback(new Error("必须输入数字"));
          } else if (Number(value) <= 0) {
            callback(new Error("必须大于0"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    min: [
      { required: true, message: "请输入最小缩放级别", trigger: "blur" },
      {
        validator: (_, value, callback) => {
          if (isNaN(Number(value))) {
            callback(new Error("必须输入数字"));
          } else if (Number(value) <= 0) {
            callback(new Error("必须大于0"));
          } else if (Number(value) >= Number(formData.value.config.max)) {
            callback(new Error("必须小于最大值"));
          } else {
            callback();
          }
        },
        trigger: "blur"
      }
    ],
    east: [
      { required: true, message: "请输入东北角经纬度", trigger: "blur" },
      { validator: validateCoordinates, trigger: "blur" }
    ],
    west: [
      { required: true, message: "请输入西南角经纬度", trigger: "blur" },
      { validator: validateCoordinates, trigger: "blur" }
    ],
    style: [{ required: true, message: "请选择样式设置", trigger: "change" }]
  }
};
const form: any = ref(null);
const submitForm = async (): Promise<any> => {
  formData.value.configValue = JSON.stringify(formData.value.config);
  return (await form.value?.validate()) ? formData.value : null;
};
const resetForm = () => {
  form.value?.resetFields();
};
const styleData = ref<any>([]);
const getSts = async () => {
  const res = await getStyles();
  styleData.value = res;
};
onMounted(async () => {
  resetForm();
  getSts();
});
defineExpose({
  submitForm,
  resetForm
});
</script>
<style scoped lang="scss"></style>
