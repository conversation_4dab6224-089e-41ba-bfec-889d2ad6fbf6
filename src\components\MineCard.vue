<!--
 * @Description: 
 * @Date: 2022-09-21 14:52:55
 * @Author: GISerZ
 * @LastEditors: silei
 * @LastEditTime: 2023-03-13 14:29:34
-->
<template>
  <div class="mine-card">
    <div class="card-top">
      <img src="../assets/img/user.png" alt="" />
      <div class="user-q">{{ greetings }}！{{ realName }}</div>
      <div class="login-time">上次登录 {{ time }}</div>
    </div>
    <div class="line"></div>
    <div class="mid">
      <div class="title">常用功能</div>
      <div class="operty">
        <div
          @click="linkTo(item)"
          v-for="(item, index) in opertyList"
          :key="index"
          class="operty-item"
        >
          <div class="item-top" :style="{ background: item.color }">
            <img :src="item.icon" alt="" />
          </div>
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <div class="line"></div>
    <div class="bottom">
      <div class="title">最近操作记录</div>
      <div class="operty-history">
        <div
          class="item"
          v-for="(item, index) in opertyHistory"
          :key="index"
          :class="[index % 2 == 0 ? 'active-bg' : '']"
        >
          <div class="cicle"></div>
          <div class="operty-name">{{ item.name }}</div>
          <div class="operty-time">{{ item.timeStr }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getLoginRecord } from "@/api/homePage";
import router from "@/router";
import store from "@/store";
import localCatch from "@/utils/auth";
import moment from "moment";
import { onMounted, ref } from "vue";
const greetings = ref("");
const realName = localCatch.getCache("realName") || "";
const getTimeState = () => {
  // 获取当前时间
  const timeNow = new Date();
  // 获取当前小时
  const hours = timeNow.getHours();
  // 设置默认文字
  let text = ``;
  // 判断当前时间段
  if (hours >= 0 && hours <= 10) {
    text = `早上好`;
  } else if (hours > 10 && hours <= 14) {
    text = `中午好`;
  } else if (hours > 14 && hours <= 18) {
    text = `下午好`;
  } else if (hours > 18 && hours <= 24) {
    text = `晚上好`;
  }
  // 返回当前时间段对应的状态
  greetings.value = text;
};

const opertyList = [
  {
    name: "服务管理",
    color: "#e8523f",
    icon: require("../assets/img/serverManage.png"),
    path: "serviceManage",
    value: 1
  },
  {
    name: "用户管理",
    color: "#F8C925",
    icon: require("../assets/img/userManage.png"),
    path: "userManage",
    value: 2
  },
  {
    name: "角色",
    color: "#649CFE",
    icon: require("../assets/img/roleManage.png"),
    path: "roleManage",
    value: 3
  }
];
const opertyHistory: any = ref([]);
const time = ref("------");
const getUserLoginInfo = async () => {
  const records = await getLoginRecord();
  console.log(records);
  for (const record of records) {
    try {
      const body = JSON.parse(record.BodyAsString);
      if (body.loginUser) {
        if (body.loginUser.userName === localCatch.getCache("realName")) {
          time.value = moment(record.EndTime).format("YYYY-MM-DD HH:mm:ss");
          return;
        }
      }
    } catch {}
  }
};
onMounted(() => {
  getTimeState();
  getUserLoginInfo();
  // time.value = localCatch.getCache("historyLogin");
  opertyHistory.value = (store.state as any).basic.historyOperate;
  if (opertyHistory.value.length === 0 && localCatch.getCache("opertyList") !== undefined) {
    opertyHistory.value = localCatch.getCache("opertyList");
  }
});
const linkTo = (row: any) => {
  router.push({ name: row.path });
  store.commit("basic/changeAvtiveMenu", row.path);
};
</script>

<style lang="scss" scoped>
.mine-card {
  margin-top: 1.8vh;
  width: 360px;
  height: 88vh;
  background: #ffffff;
  border-radius: 4px;
}
.card-top {
  width: 100%;
  height: 35%;
  display: flex;
  flex-direction: column;
  align-items: center;
  img {
    width: 140px;
    height: 140px;
    margin: 30px 0 15px 0;
  }
  .user-q {
    width: 100%;
    height: 24px;
    font-size: 24px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #232a3a;
    line-height: 35px;
    letter-spacing: 0.36px;
    text-align: center;
    margin-bottom: 15px;
  }
  .login-time {
    width: 100%;
    height: 15px;
    font-size: 14px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #7d8da1;
    line-height: 35px;
    letter-spacing: 0.21px;
    text-align: center;
  }
}
.line {
  margin-left: 25px;
  width: 312px;
  height: 1px;
  background: #ecf2fc;
}
.mid {
  width: 100%;
  height: 21%;
  .title {
    margin: 20px 0 28px 30px;
    width: 80px;
    height: 29px;
    font-size: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN-Bold;
    font-weight: 700;
    text-align: left;
    color: #232a3a;
    line-height: 35px;
  }
  .operty {
    display: flex;
    width: 100%;
    height: 100px;
    .operty-item {
      cursor: pointer;
      width: 33.3%;
      display: flex;
      flex-direction: column;
      align-items: center;
      .item-top {
        width: 56px;
        height: 56px;
        background: #e8523f;
        border-radius: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      span {
        width: 100%;
        height: 16px;
        font-size: 16px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        text-align: center;
        color: #232a3a;
        line-height: 35px;
        letter-spacing: 0.24px;
      }
    }
  }
}
.bottom {
  width: 100%;
  .title {
    margin: 20px 0 16px 30px;
    width: 200px;
    height: 29px;
    font-size: 20px;
    font-family: Source Han Sans CN, Source Han Sans CN-Bold;
    font-weight: 700;
    text-align: left;
    color: #232a3a;
    line-height: 35px;
  }
  .operty-history {
    margin-left: 27px;
    width: 310px;
    height: 270px;
    .item {
      width: 100%;
      height: 40px;
      border-radius: 2px;
      display: flex;
      align-items: center;
      .cicle {
        margin: 0 5px 0 5px;
        width: 4px;
        height: 4px;
        background: #7d8da1;
        border-radius: 50%;
      }
      .operty-name {
        width: 44%;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        text-align: left;
        color: #232a3a;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .operty-time {
        width: 46%;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        text-align: right;
        color: #7d8da1;
      }
    }
  }
}
.active-bg {
  height: 40px;
  background: #f4f7fa;
}
</style>
