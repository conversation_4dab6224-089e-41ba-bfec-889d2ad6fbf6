/*
 * @Author: xiao
 * @Date: 2022-07-20 17:28:38
 * @LastEditors: xiao
 * @LastEditTime: 2022-11-10 11:56:29
 * @Description: 浏览器本地存储
 */
class LocalCache {
  setCache(key: string, value: any) {
    window.localStorage.setItem(key, JSON.stringify(value));
  }

  getCache(key: string) {
    // obj => string => obj
    const value = window.localStorage.getItem(key);
    if (value && value !== "undefined") {
      return JSON.parse(value);
    }
  }

  deleteCache(key: string) {
    window.localStorage.removeItem(key);
  }

  clearCache() {
    window.localStorage.clear();
  }
}

export default new LocalCache();
