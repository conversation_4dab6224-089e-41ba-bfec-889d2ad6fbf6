/*
 * @Author: xiao
 * @Date: 2022-09-16 15:03:28
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 14:43:20
 * @Description:
 */

import { onMounted, onUnmounted } from "vue";

export const useRem = () => {
  // 设置 rem 函数
  const setRem = () => {
    //  PC端
    // 基准大小
    const baseSize = 100;
    const basePc = baseSize / 3840; // 表示3280的设计图,使用100PX的默认值
    // const basePc = baseSize / 1920;
    let vW = window.innerWidth; // 当前窗口的宽度
    const vH = window.innerHeight; // 当前窗口的高度

    // 非正常屏幕下的尺寸换算
    const dueH = (vW * 1080) / 1920;
    // console.log("due" + dueH);
    // console.log("vh" + vH);

    if (vH < dueH) {
      // 当前屏幕高度小于应有的屏幕高度，就需要根据当前屏幕高度重新计算屏幕宽度
      // vW = (vH * 1920) / 1080;
      // vW = (vH * 1920) / 929;
      vW = (vH * 1920) / vH;
    }

    const rem = vW * basePc; // 以默认比例值乘以当前窗口宽度,得到该宽度下的相应font-size值

    document.documentElement.style.fontSize = `${rem}px`;
  };

  const remFc = () => {
    // 初始化
    setRem();
  };
  onMounted(() => {
    /*     remFc();
    // // 改变窗口大小时重新设置 rem
    window.addEventListener("resize", remFc); */
  });

  onUnmounted(() => {
    window.removeEventListener("resize", remFc);
  });
};
