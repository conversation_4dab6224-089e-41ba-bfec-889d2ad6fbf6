/*
 * @Description: 地理编码服务接口
 * @Autor: silei
 * @Date: 2023-11-28 15:01:10
 * @LastEditors: silei
 * @LastEditTime: 2023-12-01 13:41:17
 */
import { GeocodingInfo } from "@/interface/business/geocodingInfo";
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
// 查询所有网络分析
export const getServices = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/geocoding",
    timeout: 10000
  });
  return result;
};
export const getServiceByName = async (serverName: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/geocoding/${serverName}`,
    timeout: 10000
  });
  return result;
};
export const createService = async (info: GeocodingInfo) => {
  const result: any = await hRequest.post<DataType>({
    url: `/geocoding`,
    data: {
      geocoding: info
    },
    timeout: 10000
  });
  return result;
};

export const editService = async (info: GeocodingInfo) => {
  const result: any = await hRequest.put<DataType>({
    url: `/geocoding/${info.name}`,
    data: {
      geocoding: info
    },
    timeout: 10000
  });
  return result;
};

export const deleteService = async (serviceName: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/geocoding/${serviceName}`,
    timeout: 10000
  });
  return result;
};
export const updateIndex = async (serviceName: string) => {
  const result: any = await hRequest.post<DataType>({
    url: `/geocoding/index/${serviceName}`,
    timeout: 10000
  });
  return result;
};

// 请求地理编码服务
export const queryGeocoding = async (serviceName: string, parameters: any) => {
  const result: any = await hRequest.get<DataType>({
    url: `../services/${serviceName}/rest/address/geocoding`,
    params: parameters,
    timeout: 60000
  });
  return result;
};

export const queryGeodecoding = async (serviceName: string, parameters: any) => {
  const result: any = await hRequest.get<DataType>({
    url: `../services/${serviceName}/rest/address/geodecoding`,
    params: parameters,
    timeout: 60000
  });
  return result;
};
