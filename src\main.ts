/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-01-05 10:30:13
 * @LastEditors: silei
 * @LastEditTime: 2023-03-17 10:04:48
 */
import { createApp } from "vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import locale from "element-plus/lib/locale/lang/zh-cn"; // 中文
import App from "./App.vue";
import router from "./router";
import store from "./store";
import { BaseSetting, HttpManager } from "geoserver-manager";
import hRequest from "./utils/http";
import "@/assets/css/layout.scss";
createApp(App).use(ElementPlus, { locale }).use(store).use(router).mount("#app");
// 设置Http请求
HttpManager.setHttpClient(hRequest);
BaseSetting.serviceName = process.env.VUE_APP_SERVER!;
