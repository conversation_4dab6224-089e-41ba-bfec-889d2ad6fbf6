{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "useDefineForClassFields": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "postcss.config.js"], "exclude": ["node_modules"]}