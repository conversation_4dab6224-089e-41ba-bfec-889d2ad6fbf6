<!--
 * @Description: 坐标转换参数设置
 * @Date: 2023-02-06 11:42:26
 * @Author: G<PERSON>er<PERSON>
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 15:20:48
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">参数设置</div>
    </div>
    <div class="custom-table-box" style="height: 600px">
      <div class="coordinate-transform-container">
        <!-- 输入区域 -->
        <div class="input-section">
          <div class="section-title">输入坐标系统</div>
          <div class="coordinate-selector">
            <label>选择输入坐标系</label>
            <select v-model="inputCoordSystem" class="coord-select">
              <option value="">请选择坐标系</option>
              <option v-for="system in coordinateSystems" :key="system.value" :value="system.value">
                {{ system.label }}
              </option>
            </select>
          </div>
          <div class="coordinate-input">
            <label>输入坐标 (限100个/次，以半角分号隔开)</label>
            <textarea
              v-model="inputCoordinates"
              class="coord-textarea"
              placeholder="示例: 104.055604,30.615314; 104.055603,30.615318; 104.055605,30.615313"
              rows="8"
            />
            <div class="input-info">
              <span class="count-info">{{ coordinateCount }} / 100</span>
              <span v-if="parseError" class="error-text">{{ parseError }}</span>
            </div>
          </div>
        </div>

        <!-- 转换按钮 -->
        <div class="convert-section">
          <button
            @click="handleConvert"
            :disabled="!canConvert"
            class="convert-btn"
            :class="{ converting: isConverting }"
          >
            <span v-if="isConverting">转换中...</span>
            <span v-else>转 换</span>
          </button>
        </div>

        <!-- 输出区域 -->
        <div class="output-section">
          <div class="section-title">输出坐标系统</div>
          <div class="coordinate-selector">
            <label>选择输出坐标系</label>
            <select v-model="outputCoordSystem" class="coord-select">
              <option value="">请选择坐标系</option>
              <option v-for="system in coordinateSystems" :key="system.value" :value="system.value">
                {{ system.label }}
              </option>
            </select>
          </div>
          <div class="coordinate-output">
            <label>输出坐标</label>
            <textarea
              v-model="outputCoordinates"
              class="coord-textarea output-textarea"
              readonly
              rows="8"
              placeholder="转换结果将显示在这里..."
            />
            <div class="output-actions" v-if="outputCoordinates">
              <button @click="copyToClipboard" class="copy-btn">复制结果</button>
              <span v-if="copySuccess" class="success-text">已复制到剪贴板</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick } from "vue";
import { CoordinateTransform, type CoordinatePoint } from "./CoordinateTransform";

/**
 * 坐标系统选项
 */
const coordinateSystems = [
  { label: "WGS84 (GPS坐标系)", value: "WGS84" },
  { label: "GCJ02 (火星坐标系)", value: "GCJ02" },
  { label: "BD09 (百度坐标系)", value: "BD09" }
];

/**
 * 响应式数据
 */
const inputCoordSystem = ref<string>("");
const outputCoordSystem = ref<string>("");
const inputCoordinates = ref<string>("");
const outputCoordinates = ref<string>("");
const isConverting = ref<boolean>(false);
const parseError = ref<string>("");
const copySuccess = ref<boolean>(false);

/**
 * 计算属性 - 坐标数量统计
 */
const coordinateCount = computed(() => {
  if (!inputCoordinates.value.trim()) return 0;
  const coords = parseCoordinates(inputCoordinates.value);
  return coords.length;
});

/**
 * 计算属性 - 是否可以转换
 */
const canConvert = computed(() => {
  return (
    inputCoordSystem.value &&
    outputCoordSystem.value &&
    inputCoordinates.value.trim() &&
    coordinateCount.value > 0 &&
    coordinateCount.value <= 100 &&
    !parseError.value &&
    !isConverting.value
  );
});

/**
 * 解析坐标字符串
 * @param coordText - 坐标文本
 * @returns 解析后的坐标点数组
 */
function parseCoordinates(coordText: string): CoordinatePoint[] {
  if (!coordText.trim()) return [];

  try {
    // 支持分号、换行符、逗号分隔的坐标对
    const coordPairs = coordText
      .split(/[;\n]/)
      .map((pair) => pair.trim())
      .filter((pair) => pair.length > 0);

    if (coordPairs.length > 100) {
      parseError.value = "坐标数量超过100个限制";
      return [];
    }

    const coordinates: CoordinatePoint[] = [];

    for (let i = 0; i < coordPairs.length; i++) {
      const pair = coordPairs[i];
      const parts = pair.split(",").map((p) => p.trim());

      if (parts.length < 2) {
        parseError.value = `第${i + 1}个坐标格式错误，应为：经度,纬度`;
        return [];
      }

      const lng = parseFloat(parts[0]);
      const lat = parseFloat(parts[1]);
      const alt = parts[2] ? parseFloat(parts[2]) : undefined;

      if (isNaN(lng) || isNaN(lat)) {
        parseError.value = `第${i + 1}个坐标数值无效`;
        return [];
      }

      // 基本范围检查
      if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        parseError.value = `第${i + 1}个坐标超出有效范围`;
        return [];
      }

      coordinates.push({ lng, lat, alt });
    }

    parseError.value = "";
    return coordinates;
  } catch (error) {
    parseError.value = "坐标格式解析失败";
    return [];
  }
}

/**
 * 格式化坐标输出
 * @param coordinates - 坐标点数组
 * @returns 格式化的坐标字符串
 */
function formatCoordinates(coordinates: CoordinatePoint[]): string {
  return coordinates
    .map((coord) => {
      const formatted = CoordinateTransform.formatCoordinate(coord, 6);
      if (formatted.alt !== undefined && formatted.alt !== 0) {
        return `${formatted.lng},${formatted.lat},${formatted.alt}`;
      }
      return `${formatted.lng},${formatted.lat}`;
    })
    .join(";\n");
}

/**
 * 执行坐标转换
 */
async function handleConvert(): Promise<void> {
  if (!canConvert.value) return;

  isConverting.value = true;

  try {
    await nextTick();

    const inputCoords = parseCoordinates(inputCoordinates.value);
    if (inputCoords.length === 0) {
      return;
    }

    const convertedCoords: CoordinatePoint[] = [];

    for (const coord of inputCoords) {
      const converted = CoordinateTransform.transform(
        coord,
        inputCoordSystem.value as "WGS84" | "GCJ02" | "BD09",
        outputCoordSystem.value as "WGS84" | "GCJ02" | "BD09"
      );
      convertedCoords.push(converted);
    }

    outputCoordinates.value = formatCoordinates(convertedCoords);
  } catch (error) {
    console.error("坐标转换失败:", error);
    parseError.value = "坐标转换过程中发生错误";
  } finally {
    isConverting.value = false;
  }
}

/**
 * 复制结果到剪贴板
 */
async function copyToClipboard(): Promise<void> {
  try {
    await navigator.clipboard.writeText(outputCoordinates.value);
    copySuccess.value = true;
    setTimeout(() => {
      copySuccess.value = false;
    }, 2000);
  } catch (error) {
    console.error("复制失败:", error);
  }
}

/**
 * 监听输入变化，实时验证
 */
watch(inputCoordinates, () => {
  if (inputCoordinates.value.trim()) {
    parseCoordinates(inputCoordinates.value);
  } else {
    parseError.value = "";
  }
});

/**
 * 清空输出当坐标系选择改变
 */
watch([inputCoordSystem, outputCoordSystem], () => {
  outputCoordinates.value = "";
  copySuccess.value = false;
});
</script>

<style lang="scss" scoped>
.coordinate-transform-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  border-radius: 8px;
  // min-height: 500px;

  .input-section,
  .output-section {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .convert-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
  }

  .coordinate-selector {
    margin-bottom: 20px;

    label {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .coord-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      background: white;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }
    }
  }

  .coordinate-input,
  .coordinate-output {
    label {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .coord-textarea {
      width: 100%;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: "Courier New", monospace;
      resize: vertical;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }

      &.output-textarea {
        background: #f8f9fa;
        color: #333;
      }
    }

    .input-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;

      .count-info {
        color: #666;
      }

      .error-text {
        color: #f56565;
        font-weight: 500;
      }
    }

    .output-actions {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 12px;

      .copy-btn {
        padding: 6px 12px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #66b1ff;
        }
      }

      .success-text {
        color: #67c23a;
        font-size: 12px;
      }
    }
  }

  .convert-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    min-width: 120px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
    }

    &:disabled {
      background: #c0c4cc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    &.converting {
      background: #e6a23c;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
  }
  50% {
    box-shadow: 0 6px 16px rgba(230, 162, 60, 0.6);
  }
  100% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
  }
}

@media (max-width: 768px) {
  .coordinate-transform-container {
    flex-direction: column;
    gap: 16px;
    padding: 16px;

    .convert-section {
      padding: 16px 0;
    }
  }
}
</style>
