{"name": "bcht-server-web", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "build:stage": "vue-cli-service build --mode stage", "build:prod": "vue-cli-service build --mode prod", "prepare": "husky install", "prettier": "prettier --write ."}, "dependencies": {"@turf/centroid": "^7.1.0", "@turf/distance": "^7.1.0", "axios": "^1.1.3", "cesium": "^1.99.0", "core-js": "^3.8.3", "csvtojson": "^2.0.10", "echarts": "^5.4.1", "element-plus": "^2.2.28", "element-resize-detector": "^1.2.4", "geoserver-manager": "git+http://***********:9001/gisserver/geoserver-manager-release.git#1.0.18", "js-base64": "^3.7.4", "moment": "^2.29.4", "ol": "^7.2.2", "proj4": "^2.9.0", "vjmap": "^1.0.105", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0", "x2js": "^3.4.4"}, "devDependencies": {"@commitlint/cli": "^17.2.0", "@commitlint/config-conventional": "^17.2.0", "@types/element-resize-detector": "^1.1.3", "@types/proj4": "^2.5.2", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.28.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "copy-webpack-plugin": "^11.0.0", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.0.0", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.5.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.7.0", "husky": "^8.0.2", "lint-staged": "^13.0.3", "node-polyfill-webpack-plugin": "^2.0.1", "postcss": "^8.4.19", "postcss-pxtorem": "^6.0.0", "prettier": "2.7.1", "sass": "^1.32.7", "sass-loader": "^12.0.0", "typescript": "^4.8.4", "unplugin-auto-import": "^0.11.4", "unplugin-vue-components": "^0.22.9"}, "lint-staged": {"*.{js,jsx,ts,tsx,vue}": ["prettier --write", "eslint --fix"]}, "config": {"commitizen": {"path": "./node_modules/cz-customizable"}}}