<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-14 09:33:07
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-01-02 10:50:50
-->
<template>
  <div>
    <div
      class="service-container"
      :class="analysisList[0]?.serverType !== 'serverManage' ? 'box-height' : ''"
    >
      <AnalysisCard
        v-for="(service, index) in services"
        :key="index"
        v-model="services[index]"
        @deleted="onServiceDeleted(service)"
        @setEnabled="onServiceSetEnabled(service)"
      />
    </div>
    <div class="custom-pagination-box">
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        layout="prev, pager, next, jumper"
        :total="pageInfo.total"
        background
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import AnalysisCard from "./AnalysisCard.vue";
import { onMounted, ref, watch } from "vue";

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    analysisList.value = props.modelValue;
    analysisList.value.sort((a: { dateModified: number }, b: { dateModified: number }) => {
      return a.dateModified < b.dateModified ? 1 : -1;
    });
    handleCurrentChange(1);
  }
);
const pageInfo = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});
// eslint-disable-next-line no-undef
const services: any = ref([]);
const analysisList: any = ref([]);
// 处理当前页
const handleCurrentChange = (val: number) => {
  pageInfo.value.total = analysisList.value.length;
  const pageIndex = val - 1;
  // 查询页码
  services.value = analysisList.value.filter((service: any, index: number) => {
    if (
      index >= pageIndex * pageInfo.value.pageSize &&
      index < (pageIndex + 1) * pageInfo.value.pageSize
    ) {
      return true;
    }
    return false;
  });
};
const emits = defineEmits(["serviceDeleted", "serviceSetEnabled"]);
const onServiceDeleted = (service: any) => {
  emits("serviceDeleted", service);
};
const onServiceSetEnabled = (service: any) => {
  emits("serviceSetEnabled", service);
};
onMounted(() => {
  analysisList.value = props.modelValue;
  analysisList.value.sort((a: { dateModified: number }, b: { dateModified: number }) => {
    return a.dateModified < b.dateModified ? 1 : -1;
  });
  handleCurrentChange(1);
});
</script>
<style lang="scss" scoped>
.service-container {
  width: 100%;
  height: 610px;
  display: flex;
  flex-wrap: wrap;
}
.box-height {
  height: 670px;
}
</style>
