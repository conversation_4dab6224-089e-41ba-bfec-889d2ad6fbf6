<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:54:49
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 10:52:27
-->
<template>
  <div>
    <component ref="datastoreRef" v-model="value.parameters" :is="dataSourceTypeMap[value.type]" />
  </div>
</template>
<script lang="ts" setup>
import { DatasourceType } from "geoserver-manager";
import { computed, PropType, ref } from "vue";
import ShapefileDataStore from "./datastore/ShapefileDataStore.vue";
import Cesium3DTilesDataStore from "./datastore/Cesium3DTilesDataStore.vue";
import MbtileDataStore from "./datastore/MbtileDataStore.vue";
import ArcGISCacheDataStore from "./datastore/ArcGISCacheDataStore.vue";
import GeotiffDataStore from "./datastore/GeotiffDataStore.vue";
import PostGISDataStore from "./datastore/PostGISDataStore.vue";
import MapPolymerizestore from "./datastore/MapPolymerizestore.vue";
import TerrainDataStore from "./datastore/TerrainDataStore.vue";
import MBTilesCacheDataStore from "./datastore/MBTilesCacheDataStore.vue";
import MBTilesCacheDirDataStore from "./datastore/MBTilesCacheDirDataStore.vue";
import XYZMapTilesDataStore from "./datastore/XYZMapTilesDataStore.vue";
const props = defineProps<{
  modelValue: any;
}>();
const dataSourceTypeMap = {
  [DatasourceType.SHAPEFILES]: ShapefileDataStore,
  [DatasourceType.CESIUM3DTILES]: Cesium3DTilesDataStore,
  [DatasourceType.MBTILESVECTOR]: MbtileDataStore,
  [DatasourceType.MBTILESGRID]: MbtileDataStore,
  [DatasourceType.MBTILESCACHE]: MBTilesCacheDataStore,
  [DatasourceType.MBTILESCACHEDIR]: MBTilesCacheDirDataStore,
  [DatasourceType.ARCGISCACHE]: ArcGISCacheDataStore,
  [DatasourceType.GEOTIFF]: GeotiffDataStore,
  [DatasourceType.POSTGIS]: PostGISDataStore,
  [DatasourceType.MAPPOLYMERIZE]: MapPolymerizestore,
  [DatasourceType.TERRAIN]: TerrainDataStore,
  [DatasourceType.XYZMAPTILES]: XYZMapTilesDataStore
} as any;
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const datastoreRef = ref();
const submitForm = () => {
  return datastoreRef.value.submitForm();
};
defineExpose({
  submitForm
});
</script>
