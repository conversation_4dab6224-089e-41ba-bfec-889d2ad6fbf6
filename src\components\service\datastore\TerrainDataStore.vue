<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-04-06 16:58:53
 * @LastEditors: silei
 * @LastEditTime: 2023-04-23 10:33:40
-->
<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-14 11:23:28
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-20 17:45:54
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="url" label="地形切片路径">
          <el-input v-model="value.url" class="file-input" placeholder="地形切片所在文件夹">
          </el-input>
          <el-button @click="handleSelect">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect
      :choose-type="chooseType"
      v-if="dialogVisible"
      @file-selected="handleFileSelected"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import { FormRules } from "element-plus";
import { computed, PropType, reactive, ref } from "vue";
import FileSelect from "@/components/FileSelect.vue";
const rules = reactive<FormRules>({
  url: [{ required: true, message: "请选择地形切片路径", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        url: ""
      };
    }
  }
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const chooseType = ref("dir");
const dialogVisible = ref(false);
const handleSelect = () => {
  dialogVisible.value = true;
};
const handleFileSelected = (fileName: string) => {
  dialogVisible.value = false;
  value.value.url = fileName;
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
