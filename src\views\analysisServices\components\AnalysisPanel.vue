<!--
 * @Description: 上游分析
 * @Autor: silei
 * @Date: 2023-11-15 15:12:36
 * @LastEditors: silei
 * @LastEditTime: 2023-11-15 15:36:46
-->
<template>
  <div class="custom-content">
    <div class="info-bg">
      <div class="custom-table-header-row">
        <div class="custom-table-title">{{ title }}</div>
      </div>
      <div class="box">
        <slot></slot>
        <el-row>
          <el-col :span="24">
            <div class="custom-dialog-footer">
              <div class="dialog-submit" style="width: 140px" @click="showRequest">
                查看请求参数
              </div>
              <div class="dialog-submit" @click="startAnalysis">提交</div>
            </div>
          </el-col>
        </el-row>
        <div class="result-box" v-if="Object.keys(analysisResult).length > 0">
          <div class="result-title">分析结果</div>
          <div class="result-value">
            <el-input :readonly="true" type="textarea" v-model="analysisResult" />
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="请求参数"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="@/assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">请求参数详情</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <div style="white-space: pre-wrap">{{ showText }}</div>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-submit" @click="dialogVisible = false">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
defineProps<{
  title: string;
}>();
const dialogVisible = ref<boolean>(false);

const analysisResult: any = ref("");
const emits = defineEmits(["analysis", "showRequest"]);
const startAnalysis = async () => {
  emits("analysis", (result: any) => {
    analysisResult.value = JSON.stringify(result);
  });
};
const showText = ref("");
const showRequest = () => {
  emits("showRequest", (request: any) => {
    showText.value = JSON.stringify(request);
    dialogVisible.value = true;
  });
};
</script>
<style lang="scss" scoped>
.box {
  width: 60%;
  border: 1px solid #e6eaef;
  padding: 20px 20px 30px;
}
.info-bg {
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
  padding-left: 2%;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
.result-value {
  margin-top: 20px;
  width: 98%;
  :deep(.el-textarea__inner) {
    min-height: 280px !important;
  }
}
</style>
