<!--
 * @Author: xiao
 * @Date: 2022-11-14 16:08:18
 * @LastEditors: xiao
 * @LastEditTime: 2022-11-14 17:22:43
 * @Description:
-->

# vue3-admin

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### git 约束

1. 使用 git cz 代替 git commit，会进入自动生成提交信息步骤，你只需要选择对应选项
2. 在自动生成提交信息过程中，使用键盘 Enter 键，确认选择

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
