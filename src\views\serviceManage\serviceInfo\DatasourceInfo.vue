<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-11-21 10:40:04
 * @LastEditors: silei
 * @LastEditTime: 2023-12-01 14:31:38
-->
<template>
  <el-form class="content-container" label-position="right">
    <div class="content-flex">
      <div
        class="content-item"
        :class="(i as any) === 'namespace' || (i as any) === 'dbtype'? 'content-empty':''"
        v-for="(v, i) in value"
        :key="i"
        :span="12"
      >
        <el-form-item
          v-if="(i as any) !== 'namespace' && (i as any) !== 'dbtype'"
          :label="labels[i] ?? i"
        >
          <el-input
            :type="(i as any) === 'passwd' ? 'password' : 'string'"
            v-model="value[i]"
            @change="change"
          />
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>
<script setup lang="ts">
import { computed } from "vue";

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const emits = defineEmits(["update:modelValue", "change"]);
const value = computed<any>({
  get: () => {
    return props.modelValue;
  },

  set: (v) => {
    emits("update:modelValue", v);
  }
});
const change = () => {
  emits("change", value.value);
};
const labels: any = {
  database: "数据库",
  port: "端口号",
  passwd: "密码",
  host: "地址",
  dbtype: "数据库类型",
  namespace: "命名空间",
  user: "用户",
  charset: "编码",
  url: "路径",
  scheme: "配置文件",
  indexUrl: "索引目录",
  datasetNames: "数据集",
  searchFields: "查询字段",
  filterFields: "过滤字段",
  geoDecodingRadius: "查询半径"
};
</script>
<style scoped lang="scss">
.content-flex {
  display: grid;
  width: 100%;
  flex-wrap: wrap;
  grid-template-columns: repeat(2, minmax(0, 1fr));
  column-gap: 20px;
  // .content-item {
  //   width: 50%;
  // }
  .content-empty {
    width: 0;
  }
}
</style>
