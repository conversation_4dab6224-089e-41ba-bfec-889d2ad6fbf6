export const getPonitXml = (
  lableType: string,
  lableColor: string,
  lableSize: string,
  tagProperties: string,
  textColor: string
) => {
  return `<?xml version="1.0" encoding="ISO-8859-1"?>
      <StyledLayerDescriptor version="1.0.0"
          xsi:schemaLocation="http://www.opengis.net/sld http://schemas.opengis.net/sld/1.0.0/StyledLayerDescriptor.xsd"
          xmlns="http://www.opengis.net/sld" xmlns:ogc="http://www.opengis.net/ogc"
          xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
          <NamedLayer>
            <Name></Name>
            <UserStyle>
              <Title>gold square point style</Title>
              <FeatureTypeStyle>
                <Rule>
                  <Title>gold point</Title>
                  <PointSymbolizer>
                    <Graphic>
                      <Mark>    
                        <WellKnownName>${lableType}</WellKnownName>
                        <Fill>
                          <CssParameter name="fill">${lableColor}</CssParameter>
                        </Fill>
                      </Mark>
                      <Size>${lableSize}</Size>
                    </Graphic>
                  </PointSymbolizer>
                  <TextSymbolizer>
                    <Label>
                      <ogc:PropertyName>${tagProperties}</ogc:PropertyName>
                    </Label>
                    <Fill>
                      <CssParameter name="fill">${textColor}</CssParameter>
                    </Fill>
                  </TextSymbolizer>
                </Rule>
              </FeatureTypeStyle>
            </UserStyle>
          </NamedLayer>
    </StyledLayerDescriptor>`;
};

export const getLineXml = (lineColor: string, linWidth: string) => {
  return `<?xml version="1.0" encoding="ISO-8859-1"?>
    <StyledLayerDescriptor version="1.0.0"
      xsi:schemaLocation="http://www.opengis.net/sld http://schemas.opengis.net/sld/1.0.0/StyledLayerDescriptor.xsd"
      xmlns="http://www.opengis.net/sld" xmlns:ogc="http://www.opengis.net/ogc"
      xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

      <NamedLayer>
        <Name></Name>
        <UserStyle>
          <Title>A dark yellow line style</Title>
          <FeatureTypeStyle>
            <Rule>
              <Title>dark yellow line</Title>
              <LineSymbolizer>
                <Stroke>
                  <CssParameter name="stroke">${lineColor}</CssParameter>
                  <CssParameter name="stroke-width">${linWidth}</CssParameter>
                </Stroke>
              </LineSymbolizer>
            </Rule>

          </FeatureTypeStyle>
        </UserStyle>
      </NamedLayer>
    </StyledLayerDescriptor>`;
};

export const getRectangleXml = (fillColor: string, outlineColor: string, outlineWidth: string) => {
  return `<?xml version="1.0" encoding="ISO-8859-1"?>
    <StyledLayerDescriptor version="1.0.0"
      xsi:schemaLocation="http://www.opengis.net/sld http://schemas.opengis.net/sld/1.0.0/StyledLayerDescriptor.xsd"
      xmlns="http://www.opengis.net/sld" xmlns:ogc="http://www.opengis.net/ogc"
      xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

      <NamedLayer>
        <Name></Name>
        <UserStyle>
          <Title>A dark yellow polygon style</Title>
          <FeatureTypeStyle>
            <Rule>
              <Title>dark yellow polygon</Title>
              <PolygonSymbolizer>
                <Fill>
                  <CssParameter name="fill">${fillColor}
                  </CssParameter>
                </Fill>
                <Stroke>
                  <CssParameter name="stroke">${outlineColor}</CssParameter>
                  <CssParameter name="stroke-width">${outlineWidth}</CssParameter>
                </Stroke>
              </PolygonSymbolizer>
            </Rule>
          </FeatureTypeStyle>
        </UserStyle>
      </NamedLayer>
    </StyledLayerDescriptor>`;
};
