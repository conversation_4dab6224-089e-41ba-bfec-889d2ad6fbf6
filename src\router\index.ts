/*
 * @Author: xiao
 * @Date: 2022-11-09 16:59:43
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 15:52:19
 * @Description:
 */
import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";
import Login from "../views/login/index.vue";
import ServiceInfo from "../views/serviceManage/ServiceInfo.vue";
import navigationMenu from "@/views/navigationMenu/index.vue";
import localCatch from "@/utils/auth";
import RoutePage from "@/views/navigationMenu/RoutePage.vue";
import AuthRoutePage from "@/views/navigationMenu/AuthRoutePage.vue";
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/home",
    component: navigationMenu,
    children: [
      {
        path: "/home",
        name: "home",
        meta: {
          isShow: true,
          title: "首页"
        },
        component: () => import("../views/homePage/index.vue")
      },
      {
        path: "/services",
        name: "services",
        component: RoutePage,
        redirect: "/services/service/serviceOverview",
        children: [
          {
            path: "serviceManage",
            name: "serviceManage",
            component: () => import("../views/serviceManage/ServiceManage.vue"),
            meta: {
              title: "服务管理"
            }
          },
          {
            path: "sceneServiceManage",
            name: "SceneServiceManage",
            component: () => import("../views/serviceManage/SceneServiceManage.vue")
            // meta: {
            //   title: "风格样式"
            // }
          },
          {
            path: "serviceStyleManage",
            name: "serviceStyleManage",
            component: () => import("../views/serviceManage/style/StyleManage.vue"),
            meta: {
              title: "风格样式"
            }
          },
          {
            path: "workspace",
            name: "serviceWorkSpace",
            component: () => import("../views/serviceManage/workSpace/index.vue")
          },
          {
            path: "running",
            name: "taskRunning",
            component: () => import("../views/serviceManage/mapcache/RunningTask.vue"),
            meta: {
              title: "切片缓存"
            }
          },
          {
            path: ":serviceType/:serviceName",
            component: ServiceInfo,
            meta: {
              title: "服务详情"
            }
          }
        ]
      },
      {
        path: "/expand",
        name: "expand",
        component: RoutePage,
        redirect: "/expand/parameterSetting",
        children: [
          {
            path: "vectorTileManage",
            name: "vectorTileManage",
            meta: {
              isShow: true,
              title: "矢量瓦片管理"
            },
            component: () => import("../views/expand/vectorTile/index.vue")
          },
          {
            path: "mapSchemeManage",
            name: "mapSchemeManage",
            meta: {
              isShow: true,
              title: "地图方案管理"
            },
            component: () => import("../views/expand/scheme/index.vue")
          },
          {
            path: "mapSchemeAdd",
            name: "mapSchemeAdd",
            meta: {
              isShow: true,
              title: "地图方案新增"
            },
            component: () => import("../views/expand/scheme/add.vue")
          },
          {
            path: "parameterSetting",
            name: "parameterSetting",
            meta: {
              isShow: true,
              title: "坐标转换服务"
            },
            component: () => import("../views/expand/parameterSetting/index.vue")
          },
          {
            path: "coordinateSystem",
            name: "coordinateSystem",
            meta: {
              isShow: true,
              title: "坐标系统查询"
            },
            component: () => import("../views/expand/coordinateSystem/index.vue")
          },
          {
            path: "coordinateTransform",
            name: "coordinateTransform",
            meta: {
              isShow: true,
              title: "参数设置"
            },
            component: () => import("../views/expand/coordinateTransform/index.vue")
          },
          {
            path: "accessService",
            name: "accessService",
            meta: {
              isShow: true,
              title: "接入服务"
            },
            component: () => import("../views/expand/accessService/index.vue")
          }
        ]
      },
      {
        path: "/security",
        name: "security",
        component: RoutePage,
        redirect: "/security/userManage",
        children: [
          {
            path: "userManage",
            name: "userManage",
            meta: {
              isShow: true,
              title: "用户"
            },
            component: () => import("../views/security/user/index.vue")
          },
          {
            path: "userGroupManage",
            name: "userGroupManage",
            meta: {
              isShow: true,
              title: "用户组"
            },
            component: () => import("../views/security/userGroup/index.vue")
          },
          {
            path: "roleManage",
            name: "roleManage",
            meta: {
              isShow: true,
              title: "角色"
            },
            component: () => import("../views/security/role/index.vue")
          }
        ]
      },
      {
        path: "/analysis",
        name: "analysis",
        component: RoutePage,
        children: [
          {
            path: "spatialAnalysis",
            name: "SpatialAnalysis",
            redirect: "/analysis/spatialAnalysis/spatialAnalysisIndex",
            component: () => import("../views/analysisServices/components/Router.vue"),
            children: [
              {
                path: "spatialAnalysisIndex",
                name: "SpatialAnalysisIndex",
                meta: {
                  isShow: true,
                  title: "空间分析"
                },
                component: () => import("../views/analysisServices/SpatialAnalysis.vue")
              },
              {
                path: "buffer",
                name: "Buffer",
                meta: {
                  isShow: true,
                  title: "缓冲区分析"
                },
                component: () => import("../views/analysisServices/components/Buffer.vue")
              },
              {
                path: "georelation",
                name: "GeoRelation",
                meta: {
                  isShow: true,
                  title: "空间关系分析"
                },
                component: () => import("../views/analysisServices/components/GeoRelation.vue")
              },
              {
                path: "overlay",
                name: "Overlay",
                meta: {
                  isShow: true,
                  title: "叠加分析"
                },
                component: () => import("../views/analysisServices/components/Overlay.vue")
              },
              {
                path: "contour",
                name: "Contour",
                meta: {
                  isShow: true,
                  title: "等值线分析"
                },
                component: () => import("../views/analysisServices/components/Contour.vue")
              }
            ]
          },
          {
            path: "threeDAnalysis",
            name: "ThreeDAnalysis",
            redirect: "/analysis/threeDAnalysis/threeDAnalysisIndex",
            component: () => import("../views/analysisServices/components/Router.vue"),
            children: [
              {
                path: "threeDAnalysisIndex",
                name: "ThreeDAnalysisIndex",
                meta: {
                  isShow: true,
                  title: "三维分析"
                },
                component: () => import("../views/analysisServices/ThreeDAnalysis.vue")
              }
            ]
          },
          {
            path: "networkAnalysis",
            name: "NetworkAnalysis",
            redirect: "/analysis/networkAnalysis/networkAnalysisIndex",
            component: () => import("../views/analysisServices/components/Router.vue"),
            children: [
              {
                path: "networkAnalysisIndex",
                name: "NetworkAnalysisIndex",
                meta: {
                  isShow: true,
                  title: "网络分析"
                },
                component: () => import("../views/analysisServices/NetworkAnalysis.vue")
              },
              {
                path: "crosssectionAnalysis",
                name: "CrosssectionAnalysis",
                meta: {
                  isShow: true,
                  title: "横断面分析"
                },
                component: () => import("../views/analysisServices/components/Crosssection.vue")
              },
              {
                path: "shortAnalysis",
                name: "ShortAnalysis",
                meta: {
                  isShow: true,
                  title: "最短路径分析"
                },
                component: () => import("../views/analysisServices/components/ShrotAnalysis.vue")
              },
              {
                path: "traceUp",
                name: "TraceUp",
                meta: {
                  isShow: true,
                  title: "上游分析"
                },
                component: () => import("../views/analysisServices/components/TraceUp.vue")
              },
              {
                path: "traceDown",
                name: "TraceDown",
                meta: {
                  isShow: true,
                  title: "下游分析"
                },
                component: () => import("../views/analysisServices/components/TraceDown.vue")
              },
              {
                path: "horizontal",
                name: "Horizontal",
                meta: {
                  isShow: true,
                  title: "水平净距分析"
                },
                component: () => import("../views/analysisServices/components/Horizontal.vue")
              },
              {
                path: "vertical",
                name: "Vertical",
                meta: {
                  isShow: true,
                  title: "垂直净距分析"
                },
                component: () => import("../views/analysisServices/components/Vertical.vue")
              },
              {
                path: "depth",
                name: "Depth",
                meta: {
                  isShow: true,
                  title: "埋深分析"
                },
                component: () => import("../views/analysisServices/components/Depth.vue")
              },
              {
                path: "explosion",
                name: "Explosion",
                meta: {
                  isShow: true,
                  title: "爆管分析"
                },
                component: () => import("../views/analysisServices/components/Explosion.vue")
              },
              {
                path: "connectivity",
                name: "Connectivity",
                meta: {
                  isShow: true,
                  title: "连通性分析"
                },
                component: () => import("../views/analysisServices/components/Connectivity.vue")
              },
              {
                path: "crossSectionalV",
                name: "CrossSectionalV",
                meta: {
                  isShow: true,
                  title: "纵断面分析"
                },
                component: () => import("../views/analysisServices/components/CrossSectionalV.vue")
              }
            ]
          },
          {
            path: ":serviceType/:serviceName",
            component: () => import("../views/analysisServices/AnalysisInfo.vue")
          }
        ]
      },
      {
        path: "/geocoding",
        name: "GeoCoding",
        component: RoutePage,
        redirect: "/geocoding/index",
        children: [
          {
            path: "index",
            name: "GeoCodingIndex",
            component: () => import("../views/geocodingService/GeocodingManage.vue"),
            meta: {
              title: "地理编码"
            }
          },
          {
            path: ":serviceType/:serviceName",
            component: ServiceInfo
          },
          {
            path: "rest/:serviceName/geocoding",
            component: () => import("../views/geocodingService/geocoding/GeocodingQuery.vue")
          },
          {
            path: "rest/:serviceName/geodecoding",
            component: () => import("../views/geocodingService/geocoding/GeodecodingQuery.vue")
          }
        ]
      },
      {
        path: "/cad",
        name: "cad",
        component: RoutePage,
        redirect: "/cad/cadManage",
        children: [
          {
            path: "cadManage",
            name: "CadManage",
            component: () => import("../views/cadService/CadServiceManage.vue"),
            meta: {
              title: "CAD图纸"
            }
          },
          {
            path: ":serviceType/:serviceName",
            component: ServiceInfo
          }
        ]
      }
    ]
  },
  {
    path: "/login",
    name: "login",
    component: Login
  },
  {
    path: "/preview",
    component: AuthRoutePage,
    children: [
      {
        path: "3dtiles",
        name: "tilesPreview",
        component: () => import("../views/serviceManage/servicePreview/TilesPreview.vue")
      },
      {
        path: "terrain",
        name: "terrainPreview",
        component: () => import("../views/serviceManage/servicePreview/TerrainPreview.vue")
      },
      {
        path: "wms",
        name: "wmsPreview",
        component: () => import("../views/serviceManage/servicePreview/WmsPreview.vue")
      },
      {
        path: "wmts",
        name: "wmtsPreview",
        component: () => import("../views/serviceManage/servicePreview/WmtsPreview.vue")
      },
      {
        path: "xyz",
        name: "xyzPreview",
        component: () => import("../views/serviceManage/servicePreview/XYZPreview.vue")
      }
    ]
  }
];

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
});
router.afterEach((to) => {
  if (to.meta.title) {
    localCatch.deleteCache("route_title");
    localCatch.setCache("route_title", to.meta.title);
  } else {
    localCatch.setCache("route_title", "");
  }
});
export default router;
