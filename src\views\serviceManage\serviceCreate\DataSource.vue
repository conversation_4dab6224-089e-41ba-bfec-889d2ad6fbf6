<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:54:49
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 10:52:27
-->
<template>
  <div v-if="datasourceType === DatasourceType.SHAPEFILES">
    <ShapefileDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div
    v-if="
      datasourceType === DatasourceType.MBTILESVECTOR ||
      datasourceType === DatasourceType.MBTILESGRID
    "
  >
    <MbtileDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.MBTILESCACHE">
    <MBTilesCacheDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.MBTILESCACHEDIR">
    <MBTilesCacheDirDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.CESIUM3DTILES">
    <Cesium3DTilesDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.ARCGISCACHE">
    <ArcGISCacheDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.GEOTIFF">
    <GeotiffDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.POSTGIS">
    <PostGISDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.MAPPOLYMERIZE">
    <MapPolymerizestore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.TERRAIN">
    <TerrainDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.PIPEANALYSIS">
    <TerrainDataStore ref="datastoreRef" v-model="value" />
  </div>
  <div v-if="datasourceType === DatasourceType.XYZMAPTILES">
    <XYZMapTilesDataStore ref="datastoreRef" v-model="value" />
  </div>
</template>
<script lang="ts" setup>
import { DatasourceType } from "geoserver-manager";
import { computed, PropType, ref } from "vue";
import ShapefileDataStore from "../datastore/ShapefileDataStore.vue";
import Cesium3DTilesDataStore from "../datastore/Cesium3DTilesDataStore.vue";
import MbtileDataStore from "../datastore/MbtileDataStore.vue";
import ArcGISCacheDataStore from "../datastore/ArcGISCacheDataStore.vue";
import GeotiffDataStore from "../datastore/GeotiffDataStore.vue";
import PostGISDataStore from "../datastore/PostGISDataStore.vue";
import MapPolymerizestore from "../datastore/MapPolymerizestore.vue";
import TerrainDataStore from "../datastore/TerrainDataStore.vue";
import MBTilesCacheDataStore from "../datastore/MBTilesCacheDataStore.vue";
import MBTilesCacheDirDataStore from "../datastore/MBTilesCacheDirDataStore.vue";
import XYZMapTilesDataStore from "../datastore/XYZMapTilesDataStore.vue";
const props = defineProps({
  modelValue: Object as PropType<any>,
  datasourceType: Object as PropType<DatasourceType>
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const datasourceType = computed({
  get() {
    return props.datasourceType;
  },
  set(value) {}
});
const datastoreRef = ref();
const submitForm = () => {
  return datastoreRef.value.submitForm();
};
defineExpose({
  submitForm
});
</script>
