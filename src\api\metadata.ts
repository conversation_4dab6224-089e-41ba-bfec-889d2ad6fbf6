/*
 * @Description: 获取元数据
 * @Autor: silei
 * @Date: 2023-02-09 16:00:22
 * @LastEditors: silei
 * @LastEditTime: 2023-02-10 10:59:30
 */

import hRequest from "@/utils/http";
/**
 * 获取元数据
 * @param workspace 工作空间名称
 * @returns
 */
export const getWmsMetaData = async (workspace: string) => {
  const result = await hRequest.get<any>({
    url: `../${workspace}/wms?REQUEST=GetCapabilities`
  });
  return result;
};

export const getWmtsMetaData = async (workspace: string) => {
  const result = await hRequest.get<any>({
    url: `../${workspace}/gwc/service/wmts?REQUEST=GetCapabilities`
  });
  return result;
};
