<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2024-09-06 10:55:26
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 17:05:03
-->
<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-04-03 10:50:43
 * @LastEditors: silei
 * @LastEditTime: 2024-05-09 17:01:51
-->
<template>
  <div id="map-container" v-loading="loading" element-loading-text="Loading...">
    <!-- <button @click="mapDownload">地图下载</button> -->
  </div>
</template>
<script lang="ts" setup>
import { View, Map } from "ol";
import { Tile as TileLayer } from "ol/layer";
import { WMTS } from "ol/source";
import WMTSGrid from "ol/tilegrid/WMTS";
import { Draw } from "ol/interaction";
import { Circle, Fill, Stroke, Style } from "ol/style";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { getWmtsInfo, getXYZMetaInfo } from "../../../api/resource";
import "ol/ol.css";
import { BaseSetting, ServiceType, ServiceUtil } from "geoserver-manager";
import { boundingExtent, getTopLeft } from "ol/extent";
import { Projection } from "ol/proj";
import proj4 from "proj4";
import localCache from "@/utils/auth";
import { createTemToken } from "@/api/security/token";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import XYZ from "ol/source/XYZ";
import { createBox } from "ol/interaction/Draw";
import TileGrid from "ol/tilegrid/TileGrid";
import { mapDownloadPost } from "@/api/map/map";
import { ElMessage } from "element-plus";
import { XmlUtil } from "@/utils/xmlUtil";
const route = useRoute();
const serviceName = route.query.serviceName as string;
const baseUrl = BaseSetting.getBaseUrl();
const getExtentByBbox = (bound: number[]) => {
  try {
    const minx = bound[0];
    const miny = bound[1];
    const maxx = bound[2];
    const maxy = bound[3];
    const mapExtent = boundingExtent([
      [minx, miny],
      [maxx, maxy]
    ]);
    return mapExtent;
  } catch {
    return undefined;
  }
};

let map: any = null;
const style = new Style({
  fill: new Fill({
    color: "rgba(96,96,96, 0.1)"
  }),
  // 划线的时候的图样
  stroke: new Stroke({
    color: "red",
    width: 2
  }),
  image: new Circle({
    radius: 5,
    stroke: new Stroke({
      color: "rgba(96,96,96, 0.1)"
    }),
    fill: new Fill({
      color: "rgba(96,96,96, 0.1)"
    })
  })
});
const loading = ref(false);
const mapDownload = () => {
  const layer = new VectorLayer({
    source: new VectorSource(),
    style
  });
  const draw = new Draw({
    source: layer.getSource() as any,
    type: "Circle",
    style,
    geometryFunction: createBox()
  });
  map.addInteraction(draw);

  draw.on("drawend", (evt) => {
    const extent = evt.feature.getGeometry()?.getExtent();
    if (extent) {
      // Layer(layer);
      map.removeInteraction(draw);
      loading.value = true;
      mapDownloadPost({
        baseMapDto: {
          levelList: [18],
          baseMapUrl: `${baseUrl}/${serviceName}/${serviceName}/gwc/service/wmts/rest/${serviceName}/default/EPSG:4326/EPSG:4326:{level}/{row}/{col}?format=image/png`,
          startLg: extent[0],
          startLa: extent[3],
          endLg: extent[2],
          endLa: extent[1]
        }
      })
        .then(() => {
          ElMessage({
            message: "下载成功",
            type: "success"
          });
        })
        .catch(() => {
          ElMessage({
            message: "下载失败",
            type: "success"
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};
const getMetaData = async () => {
  const info = await getXYZMetaInfo(serviceName as string);
  return new Promise<any>((resolve) => {
    const reader = new FileReader();
    reader.readAsText(info);
    reader.onload = (e) => {
      const data = reader.result as string;
      resolve(XmlUtil.ConvertToJson(data));
    };
  });
};
onMounted(async () => {
  const data = await getMetaData();
  console.log(data);
  const projection = data.TileMap.SRS;
  map = new Map({
    target: "map-container",
    view: new View({
      projection
    })
  });
  const userName = localCache.getCache("realName");
  const token = await createTemToken(userName, "");
  const resolutions = [];
  for (let i = 0; i < 19; i++) {
    resolutions[i] = Math.pow(2, 18 - i);
  }
  // ol.tilegrid.TileGrid是定义瓦片格网的类，其实就是瓦片的坐标系
  // 构造时设置瓦片的原点和每一层瓦片的分辨率
  const tilegrid = new TileGrid({
    origin: [0, 0],
    resolutions
  });
  const geoserverLayer = new TileLayer({
    source: new XYZ({
      url: `${baseUrl}/services/${serviceName}/rest/xyz/{z}/{x}/{y}.png?token=${token}`,
      projection
    })
  });
  map.addLayer(geoserverLayer);
  const bound = data.TileMap.BoundingBox;
  map.getView().fit(
    boundingExtent([
      [bound._minx, bound._miny],
      [bound._maxx, bound._maxy]
    ]),
    {
      size: map.getSize()
    }
  );
  // map.getView().fit(
  //   boundingExtent([
  //     [11688546.533293726, 2273030.9269876895],
  //     [12045143.987260092, 2875744.624352243]
  //   ]),
  //   {
  //     size: map.getSize()
  //   }
  // );
});
</script>
<style lang="scss" scoped>
#map-container {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
}
</style>
