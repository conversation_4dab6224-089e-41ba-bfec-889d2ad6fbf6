<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:08:22
 * @LastEditors: silei
 * @LastEditTime: 2023-11-30 17:08:51
-->
<template>
  <el-form
    :rules="serviceRules"
    :model="serviceInfo"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="name" label="服务名称">
          <el-input v-model="serviceInfo.name" placeholder="服务名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="title" label="服务标题">
          <el-input v-model="serviceInfo.title" placeholder="服务标题" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item label="服务描述">
          <el-input type="textarea" v-model="serviceInfo.description" placeholder="服务描述" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { reactive, ref } from "vue";
import { FormRules } from "element-plus";
import { GeocodingInfo } from "@/interface/business/geocodingInfo";
const serviceInfo = ref<GeocodingInfo>({} as any);
const nameChecked = (rule: any, value: any, callback: any) => {
  if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
    callback(new Error("不能出现中文字符，请重新输入"));
  } else if (!value) {
    callback(new Error("服务名称不能为空"));
  } else {
    callback();
  }
};
const serviceRules = reactive<FormRules>({
  name: [{ required: true, validator: nameChecked, trigger: "blur" }],
  title: [{ required: true, message: "请输入服务标题", trigger: "blur" }],
  services: [{ required: true, message: "请选择服务接口", trigger: "blur" }]
});
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      if (vaild) {
        resolve(serviceInfo.value);
      } else {
        resolve(null);
      }
    });
  });
};
defineExpose({
  submitForm
});
</script>
