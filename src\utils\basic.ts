import localCatch from "@/utils/auth";
import moment from "moment";
import store from "@/store";
export const setScoped = (title: string) => {
  const nowTime = moment().format("YYYY/MM/DD HH:mm:ss");
  let opertyList = [];
  if (localCatch.getCache("opertyList") !== undefined) {
    opertyList = localCatch.getCache("opertyList");
  }
  if (opertyList.length < 6) {
    opertyList.push({
      name: title,
      timeStr: nowTime
    });
  } else {
    opertyList.shift();
    opertyList.push({
      name: title,
      timeStr: nowTime
    });
  }
  localCatch.setCache("opertyList", opertyList);
  store.commit("basic/changeHistoryOperate", opertyList);
  console.log(store.state);
};
// 模糊搜索
export const inputSearch = (list: any, keyWord: string, name: string) => {
  const searchList = [];
  for (const i in list) {
    if (list[i][name].indexOf(keyWord) >= 0) {
      searchList.push(list[i]);
    }
  }
  return searchList;
};
