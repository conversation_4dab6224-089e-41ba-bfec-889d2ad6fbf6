<!--
 * @Author: xiao
 * @Date: 2022-11-09 16:59:43
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-07 16:22:05
 * @Description:
-->
<template>
  <router-view />
</template>

<script lang="ts" setup>
import localCatch from "@/utils/auth";
/* import { useRem } from "./utils/rem";

useRem(); */
</script>

<style lang="scss">
* {
  margin: 0;
  padding: 0;
}
body,
html {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
}
.cesium-performanceDisplay-defaultContainer {
  position: absolute;
  top: 10% !important;
  right: 1% !important;
  transform: scale(0.8);
}
</style>
