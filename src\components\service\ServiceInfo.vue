<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:08:22
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 11:12:43
-->
<template>
  <el-form
    :rules="serviceRules"
    :model="serviceInfo"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="name" label="服务名称">
          <el-input v-model="serviceInfo.name" placeholder="服务名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="title" label="服务标题">
          <el-input v-model="serviceInfo.title" placeholder="服务标题" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="数据来源">
          <el-select
            v-model="serviceInfo.type"
            class="custom-dialog-select"
            placeholder="选择数据源"
            @change="handleDatasourceChanged"
          >
            <el-option-group v-for="group in datasources" :key="group.name" :label="group.name">
              <el-option
                v-for="(item, index) in group.datasources"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="hiddenTypes" :span="12">
        <el-form-item prop="services" label="服务接口">
          <el-select
            v-model="serviceInfo.enableServices"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            class="custom-dialog-select"
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择服务接口"
          >
            <el-option v-for="item in services" :key="item" :label="item" :value="item" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item label="服务描述">
          <el-input type="textarea" v-model="serviceInfo.description" placeholder="服务描述" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { computed, reactive, ref } from "vue";
import { FormRules } from "element-plus";
const props = defineProps<{
  modelValue: any;
  datasources: any[];
  defaultType: string;
  hiddenTypes?: boolean;
  serviceFunc?: () => string[];
}>();
const emits = defineEmits(["update:modelValue"]);
const serviceInfo = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const services = ref(["WMS", "WMTS", "WFS"]);
const nameChecked = (rule: any, value: any, callback: any) => {
  if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
    callback(new Error("不能出现中文字符，请重新输入"));
  } else if (!value) {
    callback(new Error("服务名称不能为空"));
  } else {
    callback();
  }
};
const serviceRules = reactive<FormRules>({
  name: [{ required: true, validator: nameChecked, trigger: "blur" }],
  title: [{ required: true, message: "请输入服务标题", trigger: "blur" }],
  services: [{ required: true, message: "请选择服务接口", trigger: "blur" }]
});
const handleDatasourceChanged = () => {
  services.value = props.serviceFunc ? props.serviceFunc() : ["REST"];
  serviceInfo.value.services = [];
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      if (vaild) {
        resolve(serviceInfo.value);
      } else {
        resolve(null);
      }
    });
  });
};
defineExpose({
  submitForm
});
</script>
