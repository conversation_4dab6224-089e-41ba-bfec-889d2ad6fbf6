import hRequest from "@/utils/http/request_server/hRequest";
import { DataType } from "@/utils/http/types";

/**
 * @description 新增操作日志
 * @param {any} data 日志数据
 * @returns {Promise<any>} 新增操作结果
 */
export const logAdd = async (data: any) => {
  const result: any = await hRequest.post<DataType>({
    url: "/sys/oper-log",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 坐标转换
 * @param {any} data 坐标转换参数
 * @returns {Promise<any>} 坐标转换结果
 */
export const coordinateTransform = async (data: any) => {
  const result: any = await hRequest.post<DataType>({
    url: "/analyse/gs/pt/convert/coordinate",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 获取坐标系列表
 * @returns {Promise<any>} 坐标系列表
 */
export const coordinateSystemList = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/analyse/gs/pt/coordinate/list",
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
