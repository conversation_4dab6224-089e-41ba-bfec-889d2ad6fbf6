import hRequest from "@/utils/http/request_server/hRequest";
import { DataType } from "@/utils/http/types";
import type { SysUserDto, UserListResponse } from "@/interface/security/user";

/**
 * @description 获取用户列表
 * @returns {Promise<UserListResponse>} 用户列表响应
 */
export const getUserList = async (): Promise<UserListResponse> => {
  const result: any = await hRequest.get<DataType<UserListResponse>>({
    url: "/sys/user/list",
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 获取当前用户信息
 * @param {string} token 用户令牌
 * @returns {Promise<SysUserDto>} 当前用户信息
 */
export const getCurrentUser = async (token: string): Promise<any> => {
  const result: any = await hRequest.get<DataType<SysUserDto>>({
    url: "/sys/user/current",
    params: { token },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
