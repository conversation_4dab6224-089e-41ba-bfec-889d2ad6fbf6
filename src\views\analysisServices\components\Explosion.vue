<!--
 * @Description: 上游分析
 * @Autor: silei
 * @Date: 2023-11-15 15:12:36
 * @LastEditors: silei
 * @LastEditTime: 2023-11-15 15:36:46
-->
<template>
  <AnalysisPanel title="爆管分析" @analysis="startAnalysis" @showRequest="showRequest">
    <el-form
      :model="info"
      ref="formRef"
      :rules="rules"
      class="demo-ruleForm custom-sub-form"
      :label-position="'left'"
      status-icon
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="边编号" prop="minDistance">
            <el-input placeholder="请输入边编号" v-model="info.edgeId" />
          </el-form-item>
          <el-form-item label="关键设施字段" prop="minDistance">
            <el-input placeholder="请输入关键设施字段名称" v-model="info.fieldName" />
          </el-form-item>
          <el-form-item label="关键设施名称" prop="minDistance">
            <el-input placeholder="请输入关键设施名称（多个用‘,’隔开）" v-model="info.facilities" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </AnalysisPanel>
</template>
<script lang="ts" setup>
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref } from "vue";
import { networkAnalysis } from "@/api/analysis/network";
import { useRoute } from "vue-router";
import AnalysisPanel from "./AnalysisPanel.vue";
const info = ref({
  edgeId: "",
  fieldName: "",
  facilities: ""
});
const formRef = ref();
const route = useRoute(); // 活跃状态的路由
const rules = reactive<FormRules>({
  edgeId: [{ required: true, message: "请输入边编码", trigger: "blur" }]
});
const startAnalysis = async (callback: any) => {
  const data = await formRef.value.validate();
  if (data) {
    const res = await networkAnalysis(route.query.name as string, "explosion", info.value);
    ElMessage({
      message: "请求成功",
      type: "success"
    });
    callback(res);
  }
};
const showRequest = (callback: any) => {
  callback(info.value);
};
</script>
<style lang="scss" scoped>
.box {
  width: 60%;
  border: 1px solid #e6eaef;
  padding: 20px 20px 30px;
}
.info-bg {
  width: 98%;
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
  padding-left: 2%;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
.result-value {
  margin-top: 20px;
  width: 98%;
  :deep(.el-textarea__inner) {
    min-height: 280px !important;
  }
}
</style>
