<!--
 * @Description: 地形预览
 * @Autor: silei
 * @Date: 2023-02-20 10:26:40
 * @LastEditors: silei
 * @LastEditTime: 2023-11-15 09:54:42
-->
<template>
  <div id="cesiumContainer">
    <el-button @click="pipeAnalysis"> 管网状态分析 </el-button>
    <el-upload
      ref="uploadRef"
      class="upload-demo"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :show-file-list="false"
      accept=".dwg"
      :on-change="loadCad"
      :auto-upload="false"
    >
      <template #trigger>
        <el-button>上传</el-button>
      </template>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import {
  Viewer,
  buildModuleUrl,
  CesiumTerrainProvider,
  BoundingSphere,
  Cartesian3,
  UrlTemplateImageryProvider,
  WebMapServiceImageryProvider,
  ImageryLayer
} from "cesium";
import { onMounted } from "vue";
import "cesium/Source/Widgets/widgets.css";
import { useRoute } from "vue-router";
import { BaseSetting } from "geoserver-manager";
import { PipeLayer } from "./pipeLayer";
import { ElMessage, UploadFile } from "element-plus";
// import CadHelper from "@/utils/CadHelper";
const route = useRoute();
const serviceName = route.query.serviceName as string;
const baseUrl = BaseSetting.getBaseUrl();
const url = `${baseUrl}/services/${serviceName}/rest/terrain`;
let viewer: any = null;
onMounted(async () => {
  (buildModuleUrl as any).setBaseUrl("/static/Cesium/");
  viewer = new Viewer("cesiumContainer", {
    // contextOptions: {
    //   requestWebgl1: true
    // },
    geocoder: false,
    showRenderLoopErrors: true,
    selectionIndicator: false,
    // targetFrameRate: 100,//设置最大频率数
    requestRenderMode: false, // 减少Cesium渲染新帧总时间并减少Cesium在应用程序中总体CPU使用率
    // 如场景中的元素没有随仿真时间变化，请考虑将设置maximumRenderTimeChange为较高的值，例如Infinity
    // maximumRenderTimeChange:Infinity,
    maximumRenderTimeChange: 60,
    homeButton: false,
    vrButton: false,
    projectionPicker: false,
    sceneModePicker: false,
    infoBox: true,
    shadows: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    shouldAnimate: false,
    timeline: false,
    imageryProvider: new UrlTemplateImageryProvider({
      url: "http://map.geoq.cn/arcgis/rest/services/ChinaOnlineStreetPurplishBlue/MapServer/tile/{z}/{x}/{y}"
    }),
    fullscreenButton: false
  });
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = "none";
  viewer.scene.globe.show = true;
  viewer.scene.sun.show = false;
  viewer.scene.moon.show = false;
  viewer.scene.skyBox.show = false;
  // viewer.scene.backgroundColor = Color.fromCssColorString("#a1b1c4");
  const terrain = new CesiumTerrainProvider({
    requestVertexNormals: true,
    url
  });
  viewer.terrainProvider = terrain;
  // 获取地形元数据
  const meta = await fetch(`${baseUrl}/services/${serviceName}/rest/terrain/meta.json`);
  const data = await meta.json();
  const bounds = BoundingSphere.fromCornerPoints(
    Cartesian3.fromDegrees(data.bounds.east, data.bounds.north),
    Cartesian3.fromDegrees(data.bounds.west, data.bounds.south)
  );
  viewer.camera.flyToBoundingSphere(bounds);
  console.log("...");
});
const pipeAnalysis = async () => {
  const topo = await fetch("/serviceConfig/topo.json");
  const pipejson = await topo.json();
  const pipeInfo = pipejson.data.pipeInfo;
  const pipeLayer = new PipeLayer(viewer);
  pipeLayer.load(pipeInfo);
  viewer.scene.camera.setView({
    destination: Cartesian3.fromDegrees(119.3758, 35.165, 1800),
    orientation: {
      heading: 6.270697772321509,
      pitch: -0.5
    }
  });
};

/**
 * 加载cad
 * @param uploadFile
 */
const loadCad = async (uploadFile: UploadFile) => {
  if (!uploadFile.name.endsWith("dwg")) {
    ElMessage({
      message: "请选择dwg文件",
      type: "warning"
    });
    return;
  }
  // const cadwms = await CadHelper.initCadWms_Vj(uploadFile.raw!, "EPSG:4544");
  const provider = new WebMapServiceImageryProvider({
    url: "https://vjmap.com/server/api/v1/map/cmd/wms/c7688ddc95d5/v1?SERVICE=WMS&VERSION=1.1.1&REQUEST=GetMap&FORMAT=image%2Fpng&TRANSPARENT=true&mapbounds=&format=image%2Fpng&service=WMS&request=GetMap&srs=EPSG%3A4326&transparent=true&width=256&height=256&layers=sd7de02106&crs=EPSG%3A4544&fourParameter=&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************.29OAA4Cnjtw780VxIxWqduLtszp1EtpSNHAmWjiL_OM",
    // layers: cadwms.params.layers
    layers: "c7688ddc95d5"
  });

  const layer = new ImageryLayer(provider);
  viewer.imageryLayers.add(layer);
};
</script>
<style lang="scss" scoped>
#cesiumContainer {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
}
</style>
