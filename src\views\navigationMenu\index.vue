<!--
 * @Description: 
 * @Date: 2022-09-21 11:55:17
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-29 13:56:24
-->
<template>
  <div class="common-layout">
    <el-container>
      <el-header><TopNavBar /></el-header>
      <el-container>
        <el-aside class="left">
          <div class="seize-seat"></div>
          <el-menu :default-active="active" :default-openeds="openeds">
            <template v-for="(subMenu, index) in menuList">
              <el-menu-item
                @click="chiledHandleSelect(subMenu)"
                v-if="subMenu.children.length === 0"
                :key="index"
                :class="active == subMenu.path ? 'active-item' : ''"
                :index="subMenu.key"
              >
                <img class="item-img" :src="subMenu.icon" alt="" />
                <span class="item-span">{{ subMenu.name }}</span>
              </el-menu-item>
              <el-sub-menu
                v-if="subMenu.children.length > 0"
                :index="subMenu.key"
                :key="index"
                popper-class="submenu"
              >
                <template #title>
                  <span class="group-title">{{ subMenu.name }}</span>
                </template>
                <el-menu-item-group v-if="subMenu.children.length > 0">
                  <el-menu-item
                    v-for="(menu, idx) in subMenu.children"
                    :key="idx"
                    @click="chiledHandleSelect(menu)"
                    :class="active == menu.path ? 'active-item' : ''"
                    :index="`${menu.key}`"
                  >
                    <img class="item-img" :src="menu.icon" alt="" />
                    <span class="item-span">{{ menu.name }}</span>
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
            </template>
          </el-menu>
        </el-aside>
        <el-main class="custom-admin-main"><router-view></router-view></el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import TopNavBar from "@/components/TopNavBar.vue";
import router from "@/router";
import store from "@/store";
import { onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import localCatch from "@/utils/auth";
import { getCurrentUser } from "@/api/user";
import moment from "moment";
import { getRoleUser } from "@/api/security/role";
const route = useRoute(); // 活跃状态的路由
const openeds = ref(["1", "2", "3", "4", "5"]);
const chiledHandleSelect = (row: any) => {
  // 改变服务类型
  store.commit("basic/changeServerType", "地图服务");
  router.push({ name: row.path });
  active.value = row.path;
};
const initUserInfo = async () => {
  const token = route.query.token || localCatch.getCache("Latias");
  if (token) {
    localCatch.setCache("Latias", token);
    // 获取当前用户信息
    const data = await getCurrentUser(token as string);
    console.log(data);
    if (data.data) {
      await store.commit("login/changeUserName", data.data.nickname);
      const temData = moment().format("YYYY/MM/DD HH:mm:ss");
      localCatch.setCache("loginTime", temData);
      localCatch.setCache("realName", "admin");
      // 记录用户角色
      setUserRole("admin");
    }
  }
};
const setUserRole = async (username: string) => {
  const temPromise: any = await getRoleUser(username);
  const roles: string[] = temPromise.roles;
  localCatch.setCache("userRoles", roles);
};
initUserInfo();
const active = ref("1");
watch(
  () => (store.state as any).basic.avtiveMenu,
  (newVal, oldVal) => {
    active.value = newVal;
  }
);
onMounted(() => {
  const routeList = route.path.split("/");
  active.value = routeList[routeList.length - 1];
});
const menuList = [
  {
    name: "首页",
    key: "1",
    icon: require("../../assets/img/menuList/deskTop.png"),
    path: "home",
    children: []
  },
  {
    name: "基础服务",
    key: "2",
    icon: "",
    path: "",
    children: [
      // {
      //   name: "场景管理",
      //   icon: require("../../assets/img/menuList/server.png"),
      //   key: "2-1",
      //   path: "SceneServiceManage"
      // },
      {
        name: "服务管理",
        icon: require("../../assets/img/menuList/server.png"),
        key: "2-2",
        path: "serviceManage"
      },
      {
        name: "风格样式",
        icon: require("../../assets/img/menuList/style.png"),
        key: "2-3",
        path: "serviceStyleManage"
      },
      {
        name: "切片进度",
        icon: require("../../assets/img/menuList/style.png"),
        key: "2-3",
        path: "taskRunning"
      }
    ]
  },
  {
    name: "高级服务",
    key: "3",
    icon: "",
    path: "",
    children: [
      {
        name: "空间分析",
        icon: require("../../assets/img/menuList/space.png"),
        key: "3-1",
        path: "SpatialAnalysis"
      },
      // {
      //   name: "三维分析",
      //   icon: require("../../assets/img/menuList/userGroup.png"),
      //   key: "3-2",
      //   path: "ThreeDAnalysis"
      // },
      {
        name: "网络分析",
        icon: require("../../assets/img/menuList/net.png"),
        key: "3-3",
        path: "NetworkAnalysis"
      },
      {
        name: "地理编码",
        icon: require("../../assets/img/menuList/geography.png"),
        key: "3-4",
        path: "GeoCoding"
      }
      // {
      //   name: "CAD图纸",
      //   icon: require("../../assets/img/menuList/cad.png"),
      //   key: "3-5",
      //   path: "CadManage"
      // }
    ]
  },
  {
    name: "扩展能力",
    key: "5",
    icon: "",
    path: "",
    children: [
      {
        name: "矢量瓦片管理",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-1",
        path: "vectorTileManage"
      },
      {
        name: "地图方案管理",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-2",
        path: "mapSchemeManage"
      },
      {
        name: "坐标转换服务",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-3",
        path: "parameterSetting"
      },
      {
        name: "坐标系统查询",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-4",
        path: "coordinateSystem"
      },
      {
        name: "参数设置",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-5",
        path: "coordinateTransform"
      },
      {
        name: "接入服务",
        icon: require("../../assets/img/menuList/space.png"),
        key: "5-6",
        path: "accessService"
      }
    ]
  }
  // {
  //   name: "安全管理",
  //   key: "4",
  //   icon: "",
  //   path: "",
  //   children: [
  //     {
  //       name: "用户",
  //       icon: require("../../assets/img/menuList/user.png"),
  //       key: "4-1",
  //       path: "userManage"
  //     },
  //     {
  //       name: "用户组",
  //       icon: require("../../assets/img/menuList/userGroup.png"),
  //       key: "4-2",
  //       path: "userGroupManage"
  //     },
  //     {
  //       name: "角色",
  //       icon: require("../../assets/img/menuList/role.png"),
  //       key: "4-3",
  //       path: "roleManage"
  //     }
  //   ]
  // }
];
</script>

<style lang="scss" scoped>
.main-container {
  display: flex;
}
.el-header {
  --el-header-height: 64px;
  padding: 0 0;
}
.el-container {
  height: 100vh;
}
.el-main {
  --el-main-padding: 0 0;
  background: #f4f7fa;
}
.left {
  width: 256px;
  background: #ffffff;
}
.el-menu {
  border-right: none;
  height: 98%;
}
.group-title {
  width: 56px;
  height: 15px;
  font-size: 14px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: left;
  color: #7d8da1;
  line-height: 22px;
  margin-left: 15px;
}
.item-img {
  width: 24px;
  height: 24px;
  margin: 0 13px 0 10px;
}
.item-span {
  width: 32px;
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: left;
  color: #232a3a;
}
.active-item {
  background: #f1f6ff;
  .item-span {
    color: #4076f3;
  }
  &::before {
    content: "";
    left: 0;
    width: 4px;
    height: 47px;
    background: #4076f3;
    border-radius: 2px;
    display: block;
    text-align: center;
    position: absolute;
  }
}
.seize-seat {
  height: 2%;
}
.el-menu-item {
  height: 48px;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
