<!--
 * @Description: 坐标转换参数设置
 * @Date: 2023-02-06 11:42:26
 * @Author: GISer<PERSON>
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 15:20:48
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">参数设置</div>
    </div>
    <div class="custom-table-box" style="height: 600px">
      <div class="coordinate-transform-container">
        <!-- 输入区域 -->
        <div class="input-section">
          <div class="section-title">输入坐标系统</div>
          <div class="coordinate-selector">
            <label>选择输入坐标系</label>
            <select
              v-model="inputCoordSystem"
              class="coord-select"
              :disabled="isLoadingCoordSystems"
            >
              <option value="">请选择坐标系</option>
              <option v-for="system in coordinateSystems" :key="system.value" :value="system.value">
                {{ system.label }}
              </option>
            </select>
          </div>
          <div class="coordinate-input">
            <label>输入坐标 (以半角分号隔开)</label>
            <textarea
              v-model="inputCoordinates"
              class="coord-textarea"
              placeholder="示例: 104.055604,30.615314"
              rows="8"
            />
            <div class="input-info">
              <span class="count-info">{{
                coordinateCount > 0 ? "已输入坐标" : "未输入坐标"
              }}</span>
              <span v-if="parseError" class="error-text">{{ parseError }}</span>
            </div>
          </div>
        </div>

        <!-- 转换按钮 -->
        <div class="convert-section">
          <button
            @click="handleConvert"
            :disabled="!canConvert"
            class="convert-btn"
            :class="{ converting: isConverting }"
          >
            <span v-if="isConverting">转换中...</span>
            <span v-else>转 换</span>
          </button>
        </div>

        <!-- 输出区域 -->
        <div class="output-section">
          <div class="section-title">输出坐标系统</div>
          <div class="coordinate-selector">
            <label>选择输出坐标系</label>
            <select
              v-model="outputCoordSystem"
              class="coord-select"
              :disabled="isLoadingCoordSystems"
            >
              <option value="">请选择坐标系</option>
              <option v-for="system in coordinateSystems" :key="system.value" :value="system.value">
                {{ system.label }}
              </option>
            </select>
          </div>
          <div class="coordinate-output">
            <label>输出坐标</label>
            <textarea
              v-model="outputCoordinates"
              class="coord-textarea output-textarea"
              readonly
              rows="8"
              placeholder="转换结果将显示在这里..."
            />
            <div class="output-actions" v-if="outputCoordinates">
              <button @click="copyToClipboard" class="copy-btn">复制结果</button>
              <span v-if="copySuccess" class="success-text">已复制到剪贴板</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, nextTick, onMounted } from "vue";
import { coordinateTransform, coordinateSystemList } from "@/api/log";

/**
 * @interface CoordinatePoint
 * @description 坐标点接口
 */
interface CoordinatePoint {
  lng: number;
  lat: number;
  alt?: number;
}

/**
 * @interface CoordinateSystem
 * @description 坐标系统接口
 */
interface CoordinateSystem {
  label: string;
  value: number;
  epsg?: number;
  srid?: number;
}

/**
 * @interface TransformRequest
 * @description 坐标转换请求接口
 */
interface TransformRequest {
  /** WKT格式的几何对象字符串，如 "POINT(116.4 39.9)" */
  geometry: string;
  /** 源坐标系EPSG代码 */
  sourceSRS: number;
  /** 目标坐标系EPSG代码 */
  targetSRS: number;
}

/**
 * @interface TransformResponse
 * @description 坐标转换响应接口
 */
interface TransformResponse {
  data: {
    longitude: number;
    latitude: number;
  };
  success?: boolean;
  message?: string;
}

/**
 * 响应式数据
 */
const inputCoordSystem = ref<number>(0);
const outputCoordSystem = ref<number>(0);
const inputCoordinates = ref<string>("");
const outputCoordinates = ref<string>("");
const isConverting = ref<boolean>(false);
const parseError = ref<string>("");
const copySuccess = ref<boolean>(false);
const coordinateSystems = ref<CoordinateSystem[]>([]);
const isLoadingCoordSystems = ref<boolean>(false);

/**
 * 计算属性 - 坐标数量统计（限制为单个坐标）
 */
const coordinateCount = computed(() => {
  if (!inputCoordinates.value.trim()) return 0;
  const coords = parseCoordinates(inputCoordinates.value);
  return coords.length;
});

/**
 * 计算属性 - 是否可以转换
 */
const canConvert = computed(() => {
  return (
    inputCoordSystem.value &&
    outputCoordSystem.value &&
    inputCoordinates.value.trim() &&
    coordinateCount.value === 1 &&
    !parseError.value &&
    !isConverting.value &&
    !isLoadingCoordSystems.value
  );
});

/**
 * 组件挂载时加载坐标系列表
 */
onMounted(async () => {
  await loadCoordinateSystems();
});

/**
 * 加载坐标系列表
 */
async function loadCoordinateSystems(): Promise<void> {
  // 使用默认坐标系
  coordinateSystems.value = [
    { label: "WGS84 (EPSG:4326)", value: 4326, epsg: 4326 },
    { label: "大地坐标 (EPSG:3857)", value: 3857, epsg: 3857 },
    { label: "CGCS2000 (EPSG:4490)", value: 4490, epsg: 4490 }
  ];
  // isLoadingCoordSystems.value = true;

  // try {
  //   const { status, data } = await coordinateSystemList();
  //   if (status === 200) {
  //     const result = data.data;
  //     coordinateSystems.value = result.map((item: any) => ({
  //       label: item,
  //       value: item,
  //       epsg: item,
  //       srid: item
  //     }));
  //   }
  // } catch (error) {
  //   console.error("加载坐标系列表失败:", error);
  //   // 使用默认坐标系
  //   coordinateSystems.value = [
  //     { label: "WGS84 (EPSG:4326)", value: "4326", epsg: "4326" },
  //     { label: "Web墨卡托 (EPSG:3857)", value: "3857", epsg: "3857" },
  //     { label: "CGCS2000 (EPSG:4490)", value: "4490", epsg: "4490" }
  //   ];
  // } finally {
  //   isLoadingCoordSystems.value = false;
  // }
}

/**
 * 解析坐标字符串
 * @param coordText - 坐标文本
 * @returns 解析后的坐标点数组
 */
function parseCoordinates(coordText: string): CoordinatePoint[] {
  if (!coordText.trim()) return [];

  try {
    // 支持分号、换行符分隔的坐标对
    const coordPairs = coordText
      .split(/[;\n]/)
      .map((pair) => pair.trim())
      .filter((pair) => pair.length > 0);

    if (coordPairs.length > 1) {
      parseError.value = "坐标数量超过限制";
      return [];
    }

    const coordinates: CoordinatePoint[] = [];

    for (let i = 0; i < coordPairs.length; i++) {
      const pair = coordPairs[i];
      const parts = pair.split(",").map((p) => p.trim());

      if (parts.length < 2) {
        parseError.value = `坐标格式错误，应为：经度,纬度`;
        return [];
      }

      const lng = parseFloat(parts[0]);
      const lat = parseFloat(parts[1]);
      const alt = parts[2] ? parseFloat(parts[2]) : undefined;

      if (isNaN(lng) || isNaN(lat)) {
        parseError.value = `坐标数值无效`;
        return [];
      }

      // 基本范围检查（宽松检查，因为投影坐标系可能超出常规范围）
      if (Math.abs(lng) > 20037508 || Math.abs(lat) > 20037508) {
        parseError.value = `坐标数值超出合理范围`;
        return [];
      }

      coordinates.push({ lng, lat, alt });
    }

    parseError.value = "";
    return coordinates;
  } catch (error) {
    parseError.value = "坐标格式解析失败";
    return [];
  }
}

/**
 * 将坐标点转换为WKT格式字符串
 * @param coordinate - 坐标点
 * @returns WKT格式字符串
 */
function coordinateToWKT(coordinate: CoordinatePoint): string {
  if (coordinate.alt !== undefined) {
    // 3D点
    return `POINT Z(${coordinate.lng} ${coordinate.lat} ${coordinate.alt})`;
  } else {
    // 2D点
    return `POINT(${coordinate.lng} ${coordinate.lat})`;
  }
}

/**
 * 执行坐标转换
 * @description 使用WKT格式进行单点坐标转换
 */
async function handleConvert(): Promise<void> {
  if (!canConvert.value) return;

  isConverting.value = true;
  parseError.value = "";

  try {
    await nextTick();

    const inputCoords = parseCoordinates(inputCoordinates.value);
    if (inputCoords.length === 0) {
      return;
    }

    // 只处理第一个坐标点（已限制为单点转换）
    const coordinate = inputCoords[0];

    // 生成WKT格式字符串
    const wktGeometry = coordinateToWKT(coordinate);

    // 构建转换请求
    const transformRequest: TransformRequest = {
      geometry: wktGeometry,
      sourceSRS: inputCoordSystem.value,
      targetSRS: outputCoordSystem.value
    };

    // 调用API进行转换
    const response = await coordinateTransform(transformRequest);
    if (response && response.msg) {
      const wktString = response.msg;
      outputCoordinates.value = wktToCoordinate(wktString);
    } else {
      throw new Error("API返回数据格式错误");
    }
  } catch (error: any) {
    console.error("坐标转换失败:", error);
    parseError.value =
      error.response?.data?.message || error.message || "坐标转换过程中发生错误，请稍后重试";
    outputCoordinates.value = "";
  } finally {
    isConverting.value = false;
  }
}

/**
 * 将WKT格式字符串解析为坐标字符串
 * @param wktString - WKT格式字符串，如 "POINT(116.4 39.9)" 或 "POINT Z(116.4 39.9 50.0)"
 * @returns 坐标字符串，如 "116.4,39.9" 或 "116.4,39.9,50.0"
 * @example
 * wktToCoordinate("POINT(116.4 39.9)") // "116.400000,39.900000"
 * wktToCoordinate("POINT Z(116.4 39.9 50.0)") // "116.400000,39.900000,50.000"
 */
function wktToCoordinate(wktString: string): string {
  try {
    // 移除多余的空白字符
    const cleanWkt = wktString.trim();

    // 匹配POINT或POINT Z格式的WKT
    const pointMatch = cleanWkt.match(
      /POINT\s*(?:Z\s*)?\(\s*([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s+([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*(?:([-+]?[0-9]*\.?[0-9]+(?:[eE][-+]?[0-9]+)?)\s*)?\)/i
    );

    if (!pointMatch) {
      throw new Error(`无效的WKT格式: ${wktString}`);
    }

    const longitude = parseFloat(pointMatch[1]);
    const latitude = parseFloat(pointMatch[2]);
    const altitude = pointMatch[3] ? parseFloat(pointMatch[3]) : undefined;

    // 验证坐标数值的有效性
    if (isNaN(longitude) || isNaN(latitude)) {
      throw new Error(`无效的坐标数值: ${wktString}`);
    }

    // 格式化输出
    if (altitude !== undefined && !isNaN(altitude)) {
      return `${longitude},${latitude},${altitude}`;
    } else {
      return `${longitude},${latitude}`;
    }
  } catch (error) {
    console.error("WKT解析失败:", error);
    return `WKT解析错误: ${error instanceof Error ? error.message : "未知错误"}`;
  }
}

/**
 * 复制结果到剪贴板
 */
async function copyToClipboard(): Promise<void> {
  try {
    await navigator.clipboard.writeText(outputCoordinates.value);
    copySuccess.value = true;
    setTimeout(() => {
      copySuccess.value = false;
    }, 2000);
  } catch (error) {
    console.error("复制失败:", error);
  }
}

/**
 * 监听输入变化，实时验证
 */
watch(inputCoordinates, () => {
  if (inputCoordinates.value.trim()) {
    parseCoordinates(inputCoordinates.value);
  } else {
    parseError.value = "";
  }
});

/**
 * 清空输出当坐标系选择改变
 */
watch([inputCoordSystem, outputCoordSystem], () => {
  outputCoordinates.value = "";
  copySuccess.value = false;
});
</script>

<style lang="scss" scoped>
.coordinate-transform-container {
  display: flex;
  gap: 20px;
  padding: 20px;
  border-radius: 8px;

  .input-section,
  .output-section {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .convert-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
  }

  .coordinate-selector {
    margin-bottom: 20px;

    label {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .coord-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      background: white;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }

      &:disabled {
        background: #f5f5f5;
        cursor: not-allowed;
        opacity: 0.6;
      }
    }
  }

  .coordinate-input,
  .coordinate-output {
    label {
      display: block;
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .coord-textarea {
      width: 100%;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      font-family: "Courier New", monospace;
      resize: vertical;
      transition: border-color 0.3s;

      &:focus {
        outline: none;
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
      }

      &.output-textarea {
        background: #f8f9fa;
        color: #333;
      }
    }

    .input-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      font-size: 12px;

      .count-info {
        color: #666;
      }

      .error-text {
        color: #f56565;
        font-weight: 500;
      }
    }

    .output-actions {
      margin-top: 12px;
      display: flex;
      align-items: center;
      gap: 12px;

      .copy-btn {
        padding: 6px 12px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.3s;

        &:hover {
          background: #66b1ff;
        }
      }

      .success-text {
        color: #67c23a;
        font-size: 12px;
      }
    }
  }

  .convert-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s;
    min-width: 120px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
    }

    &:disabled {
      background: #c0c4cc;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    &.converting {
      background: #e6a23c;
      animation: pulse 1.5s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
  }
  50% {
    box-shadow: 0 6px 16px rgba(230, 162, 60, 0.6);
  }
  100% {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.4);
  }
}

@media (max-width: 768px) {
  .coordinate-transform-container {
    flex-direction: column;
    gap: 16px;
    padding: 16px;

    .convert-section {
      padding: 16px 0;
    }
  }
}
</style>
