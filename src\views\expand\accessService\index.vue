<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">服务接入管理</div>
      <div class="custom-table-addBlue" @click="addScheme">
        <img src="../../../assets/img/add.png" alt="" />
        <span>新增服务</span>
      </div>
    </div>
    <div class="custom-table-box">
      <el-form :model="queryform" label-position="top" class="custom-table-search-box">
        <div class="custom-search-item">
          <el-form-item label="名称">
            <el-input class="custom-input" v-model="queryform.name" placeholder="请输入" />
          </el-form-item>
        </div>
        <div class="custom-search-item">
          <el-form-item class="online-form-btns">
            <div class="custom-query-button" @click="queryData">查询</div>
            <div class="custom-reset-button" @click="reset">重置</div>
          </el-form-item>
        </div>
      </el-form>
      <el-table
        :data="selectTable"
        class="custom-table"
        header-row-class-name="custom-header-row"
        header-cell-class-name="custom-header-cell"
        style="width: 96%"
        v-loading="loading"
      >
        <el-table-column align="center" width="50">
          <template v-slot="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="createTime" label="创建时间" show-overflow-tooltip />
        <el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
        <el-table-column width="240" align="center" label="操作">
          <template v-slot="scope">
            <el-button text type="primary" @click="previewHandler(scope.row)">预览</el-button>
            <el-button text type="primary" @click="editHandler(scope.row)">编辑</el-button>
            <el-button text type="warning" class="table-del-btn" @click="deleteHandler(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination-box">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 30]"
          :small="small"
          :disabled="disabled"
          :background="background"
          layout="prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :model-value="visible"
      width="30%"
      class="admin-dialog"
      :before-close="handleClose"
      :close-on-click-modal="false"
      ref="test"
    >
      <access-service-info
        ref="vectorRef"
        v-model:formData="formData"
        v-model:editable="editable"
      />
      <template v-slot:footer v-if="dialogTitle != '矢量瓦片查看'">
        <div>
          <el-button @click="handleClose" class="custom-close-button" :loading="subBtnLoading"
            >取 消</el-button
          >
          <el-button
            type="primary"
            :loading="subBtnLoading"
            class="custom-sub-button"
            @click="eventSubmit"
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import type {
  ServiceAccessPageQuery,
  ServiceAccessPageVo,
  ServiceAccessPageResponse
} from "@/interface/business/serviceAccess";
import { accessPage, addAccess, editAccess, detailAccess, delAccess } from "@/api/accessService";
import AccessServiceInfo from "./AccessServiceInfo.vue";

/**
 * @description 创建查询表单对象
 */
const createQueryForm = (): ServiceAccessPageQuery => ({
  name: "",
  pageNum: 1,
  pageSize: 10
});

/**
 * @description 响应式数据
 */
const queryform = ref(createQueryForm());
const initFormData = () => {
  return {
    id: "",
    name: "",
    config: {
      serviceType: "",
      cityName: "",
      adminArea: "",
      tileUrl: "",
      frontendKey: "FIXED_FRONTEND_KEY_2025",
      serviceKey: "",
      centerLng: 0,
      centerLat: 0,
      maxZoom: 18,
      minZoom: 1
    },
    configValue: "",
    remark: ""
  };
};
const formData = ref<any>(initFormData());
const selectTable = ref<ServiceAccessPageVo[]>([]);
const vectorRef = ref();
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const small = ref(false);
const background = ref("#ffffff");
const disabled = ref(false);
const dialogTitle = ref("");
const visible = ref(false);
const editable = ref(false);
const subBtnLoading = ref(false);
/**
 * @description 加载分页数据
 */
const loadPageData = async () => {
  try {
    loading.value = true;
    queryform.value.pageNum = currentPage.value;
    queryform.value.pageSize = pageSize.value;
    const result = await accessPage(queryform.value);
    if (result.code === 200) {
      selectTable.value = result.data.list;
      total.value = result.data.totalCount;
    }
  } finally {
    loading.value = false;
  }
};
const previewHandler = (row: ServiceAccessPageVo) => {
  // router.push({
  //   path: "/expand/accessServiceAdd",
  //   query: { id: row.id, mode: "preview" }
  // });
};
/**
 * @description 查询数据
 */
const queryData = async () => {
  currentPage.value = 1;
  await loadPageData();
};

/**
 * @description 重置查询条件
 */
const reset = async () => {
  queryform.value = createQueryForm();
  currentPage.value = 1;
  await loadPageData();
};

/**
 * @description 分页大小改变
 * @param {number} val 新的分页大小
 */
const handleSizeChange = async (val: number) => {
  pageSize.value = val;
  currentPage.value = 1;
  await loadPageData();
};

/**
 * @description 当前页改变
 * @param {number} val 新的页码
 */
const handleCurrentChange = async (val: number) => {
  currentPage.value = val;
  await loadPageData();
};

/**
 * @description 新增方案
 */
const addScheme = () => {
  formData.value = initFormData();
  editable.value = false;
  dialogTitle.value = "服务接入新增";
  visible.value = true;
};

/**
 * @description 编辑方案
 * @param {ServiceAccessPageVo} row 方案数据
 */
const editHandler = async (row: ServiceAccessPageVo) => {
  editable.value = false;
  dialogTitle.value = "服务接入修改";
  const result = await detailAccess(row.id.toString());
  formData.value = result.data;
  formData.value.config = JSON.parse(result.data.configValue || '{}');
  visible.value = true;
};

/**
 * @description 删除方案
 * @param {ServiceAccessPageVo} row 方案数据
 */
const deleteHandler = async (row: ServiceAccessPageVo) => {
  ElMessageBox.confirm("确定要删除当前服务接入吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  }).then(() => {
    delAccess(row.id.toString()).then((res) => {
      if (res.data.code === 200) {
        ElMessage({
          showClose: true,
          message: "删除成功",
          type: "success"
        });
        if (selectTable.value.length === 1 && currentPage.value > 1) {
          currentPage.value = currentPage.value - 1;
        }
        loadPageData();
      }
    });
  });
};

/**
 * @description 获取租户显示信息
 * @param {string} configValue 配置值JSON字符串
 * @returns {string} 租户显示文本
 */
const getTenantDisplay = (configValue?: string): string => {
  if (!configValue) {
    return "无";
  }

  try {
    const config = JSON.parse(configValue);

    // 优先使用新格式的用户信息
    if (
      config.userInfoList &&
      Array.isArray(config.userInfoList) &&
      config.userInfoList.length > 0
    ) {
      const tenantNames = config.userInfoList
        .map((user: any) => user.label || user.value || user.code)
        .filter(Boolean);
      return tenantNames.length > 0 ? tenantNames.join(", ") : "无";
    }

    return "无";
  } catch (error) {
    console.error("解析配置值失败:", error);
    return "解析失败";
  }
};

/**
 * @description 组件挂载时加载数据
 */
const eventSubmit = async () => {
  try {
    const info = await vectorRef.value!.submitForm();
    if (info) {
      subBtnLoading.value = true;
      if (dialogTitle.value === "服务接入新增") {
        const result = await addAccess(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success(result.msg);
          await loadPageData();
        }
      } else {
        const result = await editAccess(info);
        if (result.code === 200) {
          visible.value = false;
          ElMessage.success("修改成功");
          await loadPageData();
        }
      }
    }
  } finally {
    subBtnLoading.value = false;
  }
};
const handleClose = () => {
  visible.value = false;
};
onMounted(async () => {
  await loadPageData();
});
</script>

<style lang="scss" scoped>
.allData {
  width: 1300px;
  height: 100%;
}
.top-operate {
  display: flex;
  height: 50px;
  width: 100%;
  justify-content: flex-end;
}
.pagination-box {
  margin-top: 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
}
::v-deep(.el-transfer__buttons) {
  padding: 0 10px;
  width: 80px;
  span {
    font-size: 12px;
    color: #000000;
    font-weight: 400;
  }
  .el-transfer__button:nth-child(2) {
    margin: 10px 0 0 0px;
    font-size: 14px;
  }
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
::v-deep(.el-transfer-panel__body) {
  height: 130px;
}
::v-deep(.el-checkbox__label) {
  font-size: 12px;
}
::v-deep(.el-dialog__header) {
  display: flex;
}
::v-deep(.el-transfer-panel__list) {
  height: 130px;
}
.role-row-width {
  width: 90%;
}
.over-length {
  width: 100%;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap; // 默认不换行；
}
</style>
