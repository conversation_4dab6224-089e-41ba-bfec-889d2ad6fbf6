<!--
 * @Description: 3dtiles预览
 * @Autor: silei
 * @Date: 2023-02-20 10:26:40
 * @LastEditors: Tanqy
 * @LastEditTime: 2024-07-22 10:42:07
-->
<template>
  <div id="cesiumContainer">
    <div class="operator-container">
      <el-form>
        <el-form-item label="显示精度">
          <el-slider v-model="data" :min="0" :max="50" :step="0.1" @change="changeData" />
        </el-form-item>
        <el-form-item label="最大内存">
          <el-slider v-model="maxMemory" :min="512" :max="5120" :step="1" @change="changeMemory" />
        </el-form-item>
      </el-form>
    </div>

    <!-- <el-button @click="pipeAnalysis"> 管网状态分析 </el-button>
    <el-button @click="changeData"> 流量 </el-button>
    <el-button @click="changeData"> 压力 </el-button>
    <el-button @click="changeData"> 流速 </el-button> -->
  </div>
</template>
<script lang="ts" setup>
import { Viewer, Cesium3DTileset, buildModuleUrl, Color } from "cesium";
import { onMounted, ref } from "vue";
import "cesium/Source/Widgets/widgets.css";
import { useRoute } from "vue-router";
import { BaseSetting } from "geoserver-manager";
// import { PipeLayer } from "./pipeLayer";
const route = useRoute();
const serviceName = route.query.serviceName as string;
const baseUrl = BaseSetting.getBaseUrl();
const url = `${baseUrl}/services/${serviceName}/rest/3dtiles/tileset.json`;
let viewer: any = null;
let tileset: Cesium3DTileset | null = null;
const data = ref(2);
const maxMemory = ref(2048);
const changeData = (value: number) => {
  if (tileset) {
    tileset.maximumScreenSpaceError = value;
  }
};
const changeMemory = (value: number) => {
  if (tileset) {
    tileset.maximumMemoryUsage = value;
  }
};
onMounted(async () => {
  (buildModuleUrl as any).setBaseUrl("/static/Cesium/");
  viewer = new Viewer("cesiumContainer", {
    contextOptions: {
      requestWebgl2: true
    },
    msaaSamples: 4,
    geocoder: false,
    showRenderLoopErrors: true,
    selectionIndicator: false,
    // targetFrameRate: 100,//设置最大频率数
    requestRenderMode: false, // 减少Cesium渲染新帧总时间并减少Cesium在应用程序中总体CPU使用率
    // 如场景中的元素没有随仿真时间变化，请考虑将设置maximumRenderTimeChange为较高的值，例如Infinity
    // maximumRenderTimeChange:Infinity,
    maximumRenderTimeChange: 60,
    homeButton: false,
    vrButton: false,
    projectionPicker: false,
    sceneModePicker: false,
    infoBox: true,
    shadows: false,
    baseLayerPicker: false,
    navigationHelpButton: false,
    animation: false,
    shouldAnimate: false,
    timeline: false,

    fullscreenButton: false
  });
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = "none";
  viewer.scene.globe.show = false;
  viewer.scene.sun.show = false;
  viewer.scene.moon.show = false;
  viewer.scene.skyBox.show = false;
  viewer.scene.backgroundColor = Color.fromCssColorString("#a1b1c4");
  tileset = new Cesium3DTileset({
    url
  });
  tileset.maximumScreenSpaceError = data.value;
  tileset.maximumMemoryUsage = maxMemory.value;
  await tileset.readyPromise;
  viewer.scene.primitives.add(tileset);
  const bound = tileset.boundingSphere;
  console.log(bound);
  viewer.scene.screenSpaceCameraController._zoomFactor = 2;
  viewer.camera.flyToBoundingSphere(bound);
});
// let pipeLayer: PipeLayer = null!;
// const pipeAnalysis = async () => {
//   const topo = await fetch("/serviceConfig/topo.json");
//   const pipejson = await topo.json();
//   const pipeInfo = pipejson.data.pipeInfo;
//   pipeLayer = new PipeLayer(viewer);
//   pipeLayer.load(pipeInfo);
//   viewer.scene.camera.setView({
//     destination: Cartesian3.fromDegrees(119.3758, 35.165, 1800),
//     orientation: {
//       heading: 6.270697772321509,
//       pitch: -0.5
//     }
//   });
// };
// const changeData = () => {
//   pipeLayer.updateRender();
// };
</script>
<style lang="scss" scoped>
#cesiumContainer {
  height: 100%;
  width: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
  .operator-container {
    position: absolute;
    right: 0;
    width: 200px;
    padding: 20px;
    z-index: 1;
  }
}
</style>
