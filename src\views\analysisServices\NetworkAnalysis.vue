<!--
 * @Description: 网络分析界面
 * @Autor: silei
 * @Date: 2023-02-01 11:57:47
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-08-02 10:44:01
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title">网络分析</div>
      <div class="custom-table-add" @click="createNetworkA">
        <img src="../../assets/img/add.png" alt="" />
        <span>新增网络分析</span>
      </div>
    </div>
    <AnalysisList
      v-model="services"
      @service-deleted="serviceDel"
      @serviceSetEnabled="SetEnabled"
    />
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="创建服务"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">创建网络分析</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <NetWorkAnalysisFrom ref="networkFromRef"></NetWorkAnalysisFrom>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-cancle" @click="dialogVisible = false">取消</div>
          <div class="dialog-submit" @click="createCompleted">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { setScoped } from "@/utils/basic";
import { onMounted, Ref, ref } from "vue";
import AnalysisList from "@/views/analysisServices/components/AnalysisList.vue";
import NetWorkAnalysisFrom from "@/views/analysisServices/components/NetWorkAnalysisFrom.vue";
import { getNetworks, addNetwork, delNetwork, modifyNetwork } from "@/api/analysis/network";
import { BaseSetting } from "geoserver-manager";
import { ElMessage } from "element-plus";
const networkFromRef = ref();
const services: Ref<any[]> = ref([]);
/**
 * 获取图层组列表
 */
const loadLayerGroups = async () => {
  const serviceList = await getNetworks();
  if (serviceList.pipeInfos?.pipeInfo) {
    services.value =
      serviceList.pipeInfos.pipeInfo instanceof Array
        ? serviceList.pipeInfos.pipeInfo
        : [serviceList.pipeInfos.pipeInfo];
  }
  services.value.forEach((serviceItem: any) => {
    serviceItem.serverType = "network";
    serviceItem.thumbnail = getThumbnail(serviceItem.name);
  });
};
const getThumbnail = (serviceName: string) => {
  const basePath = BaseSetting.getBaseUrl();
  return `${basePath}/rest/resource/thumbnail/map/${serviceName}.png`;
};
const dialogVisible: Ref<boolean> = ref(false);
const createNetworkA = () => {
  dialogVisible.value = true;
};
// 新增
const createCompleted = async () => {
  const info = await networkFromRef.value.submitForm();
  if (!info) {
    return;
  }
  addNetwork({ pipeInfo: info }).then(() => {
    ElMessage({
      message: "新增成功",
      type: "success"
    });
    setScoped(`网络分析新增-${info.title ?? info.name}`);
    dialogVisible.value = false;
    loadLayerGroups();
  });
};
// 删除
const serviceDel = (value: any) => {
  delNetwork(value.name).then(() => {
    ElMessage({
      message: "删除成功",
      type: "success"
    });
    setScoped(`网络分析删除-${value.title ?? value.name}`);
    loadLayerGroups();
  });
};
const SetEnabled = (value: any) => {
  console.log(value);
  value.enabled = !value.enabled;
  const tip = value.enabled ? "启动成功" : "禁用成功";
  modifyNetwork({ pipeInfo: value }).then(() => {
    ElMessage({
      message: tip,
      type: "success"
    });
    setScoped(`网络分析${tip}-${value.title ?? value.name}`);
    loadLayerGroups();
  });
};
onMounted(() => {
  loadLayerGroups();
});
</script>
<style lang="scss" scoped>
.sever-box {
  width: 1600px;
  height: 80px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.server-item {
  cursor: pointer;
  width: 200px;
  height: 100%;
  display: flex;
  align-items: center;
  img {
    width: 35px;
    height: 35px;
    margin: 0 20px;
  }
  .right {
    width: 120px;
    display: flex;
    flex-direction: column;
    .titile {
      width: 72px;
      height: 27px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #232a3a;
    }
    span {
      width: 88px;
      height: 20px;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #7d8da1;
    }
  }
}
.active-title {
  color: #4076f3 !important;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
