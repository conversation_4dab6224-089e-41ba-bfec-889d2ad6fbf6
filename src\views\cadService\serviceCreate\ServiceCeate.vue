<!--
 * @Description: 服务创建
 * @Autor: silei
 * @Date: 2023-02-02 11:06:08
 * @LastEditors: silei
 * @LastEditTime: 2023-12-12 11:55:07
-->
<template>
  <div>
    <div v-show="currStep.name === 'datasource'">
      <ServiceInfo ref="serviceInfoRef" />
    </div>
    <div v-show="currStep.name === 'datasourcedetails'">
      <DataSource ref="datasourceRef" v-model="datastoreOptions" :datasource-type="currStep.type" />
    </div>
    <div v-show="currStep.name === 'details'">
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务名称">
            <span>{{ serviceInfo.name }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务标题">
            <span>{{ serviceInfo.title }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务描述">
            <span>{{ serviceInfo.description }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <el-row class="custom-dialog-footer create-footer">
      <el-button :loading="btnLoading" class="dialog-cancle" @click="cancel">取消</el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== 'datasource'"
        class="dialog-submit"
        @click="getPreStep"
      >
        上一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== 'details'"
        class="dialog-submit"
        @click="getNextStep"
      >
        下一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name === 'details'"
        class="dialog-submit"
        @click="createService"
      >
        完成
      </el-button>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import {
  DatasourceStep,
  DatasourceType,
  Charset,
  ServiceStep,
  ServiceUtil,
  DataSourceFactory,
  BaseService
} from "geoserver-manager";
import { Ref, ref } from "vue";

import { setScoped } from "@/utils/basic";
import ServiceInfo from "./ServiceInfo.vue";
import DataSource from "./DataSource.vue";
import { ElMessage } from "element-plus";
import { createService as createCadService } from "@/api/cad";
import { GeocodingInfo } from "@/interface/business/geocodingInfo";
// 当前步骤
const currStep: Ref<ServiceStep> = ref(new DatasourceStep(null));
// 数据源列表
const datastoreOptions = ref({});
const serviceInfo = ref<GeocodingInfo>({} as any);
const emits = defineEmits(["canceled", "completed"]);
const serviceInfoRef = ref();
const datasourceRef = ref();
const layerSelectRef = ref();
const layerData = ref([] as any[]);
const layers = ref<string[] | null>(null);
/**
 * 获取上一步
 */
const getNextStep = async () => {
  if (currStep.value.name === "datasource") {
    const service = await serviceInfoRef.value.submitForm();
    if (!service) {
      return;
    }
    serviceInfo.value = service;
  } else if (currStep.value.name === "datasourcedetails") {
    const serviceInfo = await datasourceRef.value.submitForm();
    if (!serviceInfo) {
      return;
    }
  } else if (currStep.value.name === "select") {
    const data = layerSelectRef.value.submitForm();
    if (!data) {
      ElMessage.error("至少选择一个一个图层");
      return;
    }
    layers.value = data.map((d: any) => d.name);
  }
  // 选择数据源
  const nextStep = currStep.value.getNextStep(DatasourceType.ARCGISCACHE);
  if (nextStep) {
    currStep.value = nextStep;
  }
};
/**
 * 获取下一步
 */
const getPreStep = () => {
  const preStep = currStep.value.getPreStep(DatasourceType.ARCGISCACHE)!;
  if (preStep) {
    currStep.value = preStep;
  }
};
const btnLoading = ref(false);
/**
 * 创建服务
 */
const createService = async () => {
  btnLoading.value = true;
  try {
    await createCadService({
      ...serviceInfo.value,
      ...datastoreOptions.value
    } as any);
    emits("completed");
    ElMessage({
      message: "创建成功",
      type: "success"
    });
    setScoped(`服务新增-${serviceInfo.value.name}`);
  } finally {
    btnLoading.value = false;
  }
};
const cancel = () => {
  emits("canceled");
};
</script>
<style scoped lang="scss">
.create-footer {
  margin: 20px 0 0 0;
}
</style>
