/*
 * @Author: xiao
 * @Date: 2022-11-09 16:59:43
 * @LastEditors: silei
 * @LastEditTime: 2024-10-21 10:17:35
 * @Description:
 */
const { defineConfig } = require("@vue/cli-service");
const path = require("path");
const CopyWebpackPlugin = require("copy-webpack-plugin");
//const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const cesiumBuild = "./node_modules/cesium/Build/Cesium";
const webpack = require("webpack");
function resolve(dir) {
  return path.join(__dirname, dir);
}

function resolve(dir) {
  return path.join(__dirname, dir);
}
// const px2rem = require("postcss-px2rem");
// const postcss = px2rem({
//   // 基准大小 baseSize，需要和rem.js中相同
//   remUnit: 50
// });
function getPlugins() {
  const plugins = [];
  plugins.push(
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.join(cesiumBuild, "Workers"),
          to: "static/Cesium/Workers"
        },
        {
          from: path.join(cesiumBuild, "Assets"),
          to: "static/Cesium/Assets"
        },
        {
          from: path.join(cesiumBuild, "Widgets"),
          to: "static/Cesium/Widgets"
        },
        {
          from: path.join(cesiumBuild, "ThirdParty"),
          to: "static/Cesium/ThirdParty"
        }
      ]
    })
  );
  plugins.push(
    new webpack.DefinePlugin({
      CESIUM_BASE_URL: JSON.stringify("")
    })
  );
  plugins.push(new NodePolyfillPlugin());
  // 生产环境
  if (process.env.ENV === "production") {
    // plugins.push(
    //   new UglifyJsPlugin({
    //     // 移除console及注释
    //     uglifyOptions: {
    //       output: {
    //         comments: false // 去除注释
    //       },
    //       compress: {
    //         // warnings: false,
    //         drop_debugger: true,
    //         drop_console: true, // console
    //         pure_funcs: ["console.log"] // 移除console
    //       }
    //     },
    //     sourceMap: false,
    //     parallel: true
    //   })
    // );
  } else {
    // 开发环境
  }
  return plugins;
}

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: "/",
  outputDir: "dist",
  devServer: {
    // overlay: {
    //   warnings: false,
    //   errors: true
    // }
    proxy: {
      bcserver: {
        // target: "http://************:8077/bcserver",
        target: "http://************:8089/geoserver",
        //target: process.env.VUE_APP_BASEURL,
        changeOrigin: true,
        pathRewrite: {
          // 路径重写
          bcserver: ""
        }
      },
      "/api": {
        target: "http://************:9111",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, "")
      }
      // "geoserver/rest": {
      //   target: "http://localhost:8070/geoserver/rest",
      //   //target: process.env.VUE_APP_BASEURL,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     // 路径重写
      //     "geoserver/rest": ""
      //   }
      // }
    }
  },
  configureWebpack: {
    resolve: {
      alias: {
        "@": resolve("src")
      }
    },
    plugins: getPlugins()
  },
  css: {
    loaderOptions: {
      scss: {
        // 注意：在 sass-loader v8 中，这个选项名是 "prependData"
        additionalData: ``
      },
      postcss: {
        postcssOptions: {
          // plugins: [postcss]
        }
      }
    }
  }
});
