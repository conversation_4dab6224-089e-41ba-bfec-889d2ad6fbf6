<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-15 15:34:50
 * @LastEditors: silei
 * @LastEditTime: 2023-04-23 10:34:55
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="tilingScheme" label="切片方案">
          <el-input class="file-input" v-model="value.tilingScheme" placeholder=".xml文件">
          </el-input>
          <el-button @click="handleSelect('config')">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="tileCachePath" label="切片缓存路径">
          <el-input class="file-input" v-model="value.tileCachePath" placeholder="切片缓存路径">
          </el-input>
          <el-button @click="handleSelect('path')">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect
      :choose-type="chooseType"
      :accept="accept"
      v-if="dialogVisible"
      @file-selected="handleFileSelected"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import { FormRules } from "element-plus";
import { computed, PropType, reactive, ref } from "vue";
import FileSelect from "@/components/FileSelect.vue";
const rules = reactive<FormRules>({
  tilingScheme: [{ required: true, message: "请选择切片方案路径", trigger: "blur" }],
  tileCachePath: [{ required: true, message: "请选择切片缓存路径", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        tilingScheme: "",
        tileCachePath: ""
      };
    }
  }
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const dialogVisible = ref(false);
const chooseType = ref("file");
const accept = ref(".xml");
const selectType = ref("config");
const handleSelect = (type: string) => {
  dialogVisible.value = true;
  selectType.value = type;
  if (type === "config") {
    chooseType.value = "file";
    accept.value = ".xml";
  } else {
    chooseType.value = "dir";
    accept.value = "";
  }
};
const handleFileSelected = (fileName: string) => {
  dialogVisible.value = false;
  if (selectType.value === "config") {
    value.value.tilingScheme = fileName;
  } else {
    value.value.tileCachePath = fileName;
  }
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
