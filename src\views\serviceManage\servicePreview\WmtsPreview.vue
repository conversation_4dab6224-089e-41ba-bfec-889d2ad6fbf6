<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-04-03 10:50:43
 * @LastEditors: silei
 * @LastEditTime: 2024-05-09 17:01:51
-->
<template>
  <div id="map-container" v-loading="loading" element-loading-text="Loading...">
    <!-- <button @click="mapDownload">地图下载</button> -->
  </div>
</template>
<script lang="ts" setup>
import { View, Map } from "ol";
import { Tile as TileLayer } from "ol/layer";
import { WMTS } from "ol/source";
import WMTSGrid from "ol/tilegrid/WMTS";
import { Draw } from "ol/interaction";
import { Circle, Fill, Stroke, Style } from "ol/style";
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { getWmtsInfo } from "../../../api/resource";
import "ol/ol.css";
import { BaseSetting } from "geoserver-manager";
import { boundingExtent, getTopLeft } from "ol/extent";
import { Projection } from "ol/proj";
import proj4 from "proj4";
import localCache from "@/utils/auth";
import { createTemToken } from "@/api/security/token";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import { createBox } from "ol/interaction/Draw";
import { mapDownloadPost } from "@/api/map/map";
import { ElMessage } from "element-plus";
const route = useRoute();
const serviceName = route.query.serviceName;
const baseUrl = BaseSetting.getBaseUrl();
const getExtentByBbox = (bound: number[]) => {
  try {
    const minx = bound[0];
    const miny = bound[1];
    const maxx = bound[2];
    const maxy = bound[3];
    const mapExtent = boundingExtent([
      [minx, miny],
      [maxx, maxy]
    ]);
    return mapExtent;
  } catch {
    return undefined;
  }
};

let map: any = null;
const style = new Style({
  fill: new Fill({
    color: "rgba(96,96,96, 0.1)"
  }),
  // 划线的时候的图样
  stroke: new Stroke({
    color: "red",
    width: 2
  }),
  image: new Circle({
    radius: 5,
    stroke: new Stroke({
      color: "rgba(96,96,96, 0.1)"
    }),
    fill: new Fill({
      color: "rgba(96,96,96, 0.1)"
    })
  })
});
const loading = ref(false);
const mapDownload = () => {
  const layer = new VectorLayer({
    source: new VectorSource(),
    style
  });
  const draw = new Draw({
    source: layer.getSource() as any,
    type: "Circle",
    style,
    geometryFunction: createBox()
  });
  map.addInteraction(draw);

  draw.on("drawend", (evt) => {
    const extent = evt.feature.getGeometry()?.getExtent();
    if (extent) {
      // Layer(layer);
      map.removeInteraction(draw);
      loading.value = true;
      mapDownloadPost({
        baseMapDto: {
          levelList: [18],
          baseMapUrl: `${baseUrl}/${serviceName}/${serviceName}/gwc/service/wmts/rest/${serviceName}/default/EPSG:4326/EPSG:4326:{level}/{row}/{col}?format=image/png`,
          startLg: extent[0],
          startLa: extent[3],
          endLg: extent[2],
          endLa: extent[1]
        }
      })
        .then(() => {
          ElMessage({
            message: "下载成功",
            type: "success"
          });
        })
        .catch(() => {
          ElMessage({
            message: "下载失败",
            type: "success"
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};
onMounted(async () => {
  const wmtsInfo = await getWmtsInfo(serviceName as string);
  proj4.defs(wmtsInfo.srs, wmtsInfo.srsWkt);
  const projection = new Projection({
    code: wmtsInfo.srs,
    units: wmtsInfo.units,
    axisOrientation: wmtsInfo.neu ? "neu" : undefined
  });
  map = new Map({
    target: "map-container",
    view: new View({
      projection
    })
  });
  const userName = localCache.getCache("realName");
  const token = await createTemToken(userName, "");
  console.log(getTopLeft(getExtentByBbox(wmtsInfo.gridBound)!));
  const geoserverLayer = new TileLayer({
    source: new WMTS({
      url: `${baseUrl}${wmtsInfo.url ?? "/gwc/service/wmts"}?token=${token}`,
      layer: wmtsInfo.name,
      matrixSet: wmtsInfo.tileMatrixSet,
      format: wmtsInfo.format,
      tileGrid: new WMTSGrid({
        origin: getTopLeft(getExtentByBbox(wmtsInfo.gridBound)!),
        resolutions: wmtsInfo.resolutions,
        matrixIds: wmtsInfo.matrixIds
      }),
      style: wmtsInfo.style
      // tileLoadFunction: function (image: any, src) {
      //   getImage(src).then((data) => {
      //     if (data) {
      //       const url = URL.createObjectURL(data);
      //       const img: any = image.getImage();
      //       img.addEventListener("load", function () {
      //         console.log("aaa");
      //         URL.revokeObjectURL(url);
      //       });
      //       img.src = url;
      //     }
      //   });
      // }
    })
  });
  map.addLayer(geoserverLayer);

  map.getView().fit(getExtentByBbox(wmtsInfo.bound)!, {
    size: map.getSize()
  });
  // map.getView().fit(
  //   boundingExtent([
  //     [11688546.533293726, 2273030.9269876895],
  //     [12045143.987260092, 2875744.624352243]
  //   ]),
  //   {
  //     size: map.getSize()
  //   }
  // );
});
</script>
<style lang="scss" scoped>
#map-container {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
}
</style>
