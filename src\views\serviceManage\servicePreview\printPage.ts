/*
 * @Description:
 * @Autor: silei
 * @Date: 2024-10-18 16:07:34
 * @LastEditors: silei
 * @LastEditTime: 2024-10-21 10:43:35
 */
import { Geometry, Point, Polygon } from "ol/geom";
import { Vector } from "ol/source";
import { WKT } from "ol/format";
import { boundingExtent, containsExtent, getCenter } from "ol/extent";
import { Map } from "ol";
import centroid from "@turf/centroid";
import { Coordinate, distance } from "ol/coordinate";
import { fromExtent } from "ol/geom/Polygon";
export class PrintPage {
  geometry: Geometry;
  handle: Point;
  scale: number = 100000;
  rotation: number = 0;
  isRotation: boolean = false;
  scales = [
    { name: "1:25,000", value: "25000.0" },
    { name: "1:50,000", value: "50000.0" },
    { name: "1:100,000", value: "100000.0" },
    { name: "1:200,000", value: "200000.0" },
    { name: "1:500,000", value: "500000.0" },
    { name: "1:1,000,000", value: "1000000.0" },
    { name: "1:2,000,000", value: "2000000.0" },
    { name: "1:4,000,000", value: "4000000.0" },
    { name: "1:8,000,000", value: "8000000.0" },
    { name: "1:16,000,000", value: "16000000.0" }
  ];

  size = { width: 440, height: 483 };
  constructor() {
    this.geometry = new WKT().readGeometry("POLYGON((-1 -1,1 -1,1 1,-1 1,-1 -1))");
    this.handle = new Point([0, 0]);
  }

  getCenter() {
    return getCenter(this.geometry.getExtent());
  }

  setScale(scale: number, units: number) {
    const bound = this.calculatePageBounds(scale, units ?? 1);
    const geom = fromExtent(bound);
    const rotation = this.rotation;
    if (rotation !== 0) {
      geom.rotate(-rotation, getCenter(geom.getExtent()));
    }
    this.scale = scale;
    this.updateFeature(geom);
  }

  setCenter(center: Coordinate) {
    const geom = this.geometry;
    const oldCenter = getCenter(geom.getExtent());
    const dx = center[0] - oldCenter[0];
    const dy = center[1] - oldCenter[1];
    geom.translate(dx, dy);
    // this.updateFeature(geom);
  }

  setRotation(rotation: number) {
    if (this.isRotation) {
      const geom = this.geometry;
      geom.rotate(this.rotation - rotation, getCenter(geom.getExtent()));
      this.rotation = rotation;
      // this.updateFeature(geom);
    }
  }

  fitPage(map: Map) {
    this.setCenter(map.getView().getCenter()!);
    const extent = map.getView().calculateExtent(map.getSize());
    const units = map.getView().getProjection().getMetersPerUnit();
    // 计算最合适的scale
    for (const rec of this.scales) {
      this.scale = Number(rec.value);
      if (!containsExtent(extent, this.calculatePageBounds(Number(rec.value), units ?? 1))) {
        break;
      }
    }
    this.setScale(this.scale, units ?? 1);
  }

  // updateByHandle(updateHandle: any) {
  //   const geometry = this.geometry;
  //   let rotation = 0;
  //   if (this.isRotation) {
  //     const hLoc = getCenter(this.handle.getExtent());
  //     const center = getCenter(geometry.getExtent());
  //     const dx = hLoc[0] - center[0];
  //     const dy = hLoc[1] - center[1];
  //     rotation = Math.round((Math.atan2(dx, dy) * 180) / Math.PI);
  //   }
  //   const dist = distance(getCenter(geometry.getExtent()), this.handle.getCoordinates());
  //   const scaleFits: number[] = [];
  //   const distHash = {};
  //   this.printProvider.scales.each(function (rec: any) {
  //     const bounds = this.calculatePageBounds(rec);
  //     const d = Math.abs(bounds.getHeight() / 2 - dist);
  //     scaleFits.push(d);
  //     distHash[d.toPrecision(8)] = rec;
  //   }, this);
  //   const min = scaleFits.concat().sort(function (a, b) {
  //     return a < b ? -1 : 1;
  //   })[0];
  //   const scale = distHash[min.toPrecision(8)];
  //   const bounds = this.calculatePageBounds(scale);
  //   const geom = bounds.toGeometry();
  //   geom.rotate(-rotation, geom.getCentroid());
  //   this.scale = scale;
  //   this.rotation = rotation;
  //   this.updateFeature(geom, updateHandle);
  // }

  updateFeature(geometry: Geometry, updateHandle?: boolean | undefined) {
    this.geometry = geometry;
    // const f = this.feature;
    // geometry.id = f.geometry.id;
    // f.geometry = geometry;
    // f.layer && f.layer.drawFeature(f);
    // if (updateHandle !== false) {
    //   this.updateHandle();
    // }
    // this.fireEvent("change", this);
  }

  // updateHandle() {
  //   const f = this.feature;
  //   const h = this.handle;
  //   const hLoc = this.calculateHandleLocation();
  //   const geom = new OpenLayers.Geometry.Point(hLoc.lon, hLoc.lat);
  //   geom.id = h.geometry.id;
  //   h.geometry = geom;
  //   h.layer && h.layer.drawFeature(h);
  // }

  // calculateHandleLocation() {
  //   const c = this.feature.geometry.components[0].components;
  //   const top = new OpenLayers.Geometry.LineString([c[2], c[3]]);
  //   return top.getBounds().getCenterLonLat();
  // }

  calculatePageBounds(scale: number, units: number) {
    const geom = this.geometry;
    const center = getCenter(geom.getExtent());
    const size = this.size;
    const inchesPerMeter = 1000 / 25.4;
    // const unitsRatio = INCHES_PER_UNIT[_units] ?? INCHES_PER_UNIT.dd;
    const w = ((size.width / 72 / inchesPerMeter / units) * scale) / 2;
    const h = ((size.height / 72 / inchesPerMeter / units) * scale) / 2;
    return [center[0] - w, center[1] - h, center[0] + w, center[1] + h];
  }
}
