/**
 * @description 地图方案业务接口定义
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

/**
 * @interface BaseMapScheme
 * @description 地图方案基础信息接口
 */
export interface BaseMapScheme {
  /** 方案ID */
  id?: number;
  /** 方案名称 */
  name: string;
  /** 配置值 */
  configValue?: string;
  /** 方案描述 */
  remark?: string;
}

/**
 * @interface MapSchemeDto
 * @description 地图方案数据传输对象（用于新增和修改）
 */
export interface MapSchemeDto extends BaseMapScheme {
  /** 用户编码列表 */
  userCodeList?: string[];
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface MapSchemeVo
 * @description 地图方案视图对象（用于详情查询）
 */
export interface MapSchemeVo extends BaseMapScheme {
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人ID */
  updateBy?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface MapSchemePageVo
 * @description 地图方案分页列表项
 */
export interface MapSchemePageVo {
  /** 方案ID */
  id: number;
  /** 方案名称 */
  name: string;
  /** 配置值 */
  configValue?: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
}

/**
 * @interface MapSchemePageQuery
 * @description 地图方案分页查询参数
 */
export interface MapSchemePageQuery {
  /** 页面大小 */
  pageSize?: number;
  /** 页码 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 是否升序 */
  isAsc?: string;
  /** 起始位置 */
  firstNum?: number;
  /** 方案名称（搜索条件） */
  name?: string;
}

/**
 * @interface PageInfo<T>
 * @description 分页信息通用接口
 */
export interface PageInfo<T> {
  /** 总记录数 */
  totalCount: number;
  /** 页面大小 */
  pageSize: number;
  /** 总页数 */
  totalPage: number;
  /** 当前页码 */
  currPage: number;
  /** 数据列表 */
  list: T[];
}

/**
 * @interface ApiResponse<T>
 * @description API响应通用接口
 */
export interface ApiResponse<T> {
  /** 响应码 */
  code: number;
  /** 响应消息 */
  msg: string;
  /** 响应数据 */
  data: T;
}

/**
 * @type MapSchemePageInfo
 * @description 地图方案分页信息类型
 */
export type MapSchemePageInfo = PageInfo<MapSchemePageVo>;

/**
 * @type MapSchemeDetailResponse
 * @description 地图方案详情响应类型
 */
export type MapSchemeDetailResponse = ApiResponse<MapSchemeVo>;

/**
 * @type MapSchemePageResponse
 * @description 地图方案分页响应类型
 */
export type MapSchemePageResponse = ApiResponse<MapSchemePageInfo>;

/**
 * @type MapSchemeOperationResponse
 * @description 地图方案操作响应类型（新增、修改、删除）
 */
export type MapSchemeOperationResponse = ApiResponse<any>;
