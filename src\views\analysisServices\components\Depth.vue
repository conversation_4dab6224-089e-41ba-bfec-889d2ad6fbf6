<!--
 * @Description: 上游分析
 * @Autor: silei
 * @Date: 2023-11-15 15:12:36
 * @LastEditors: silei
 * @LastEditTime: 2023-11-15 15:36:46
-->
<template>
  <AnalysisPanel title="埋深分析" @analysis="startAnalysis" @showRequest="showRequest">
    <el-form
      :model="info"
      ref="formRef"
      :rules="rules"
      class="demo-ruleForm custom-sub-form"
      :label-position="'left'"
      status-icon
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="最小距离(m)" prop="minDistance">
            <el-input-number placeholder="请输入结点编号" v-model="info.minDistance" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </AnalysisPanel>
</template>
<script lang="ts" setup>
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref } from "vue";
import { networkAnalysis } from "@/api/analysis/network";
import { useRoute } from "vue-router";
import AnalysisPanel from "./AnalysisPanel.vue";
const info = ref({
  minDistance: 1
});
const formRef = ref();
const route = useRoute(); // 活跃状态的路由
const rules = reactive<FormRules>({
  minDistance: [{ required: true, message: "请输入最小距离", trigger: "blur" }]
});
const startAnalysis = async (callback: any) => {
  const data = await formRef.value.validate();
  if (data) {
    const res = await networkAnalysis(route.query.name as string, "depth", info.value);
    ElMessage({
      message: "请求成功",
      type: "success"
    });
    callback(res);
  }
};
const showRequest = (callback: any) => {
  callback(info.value);
};
</script>
<style lang="scss" scoped>
.box {
  width: 60%;
  border: 1px solid #e6eaef;
  padding: 20px 20px 30px;
}
.info-bg {
  width: 98%;
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
  padding-left: 2%;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
.result-value {
  margin-top: 20px;
  width: 98%;
  :deep(.el-textarea__inner) {
    min-height: 280px !important;
  }
}
</style>
