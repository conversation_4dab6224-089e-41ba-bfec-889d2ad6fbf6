<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    :disabled="editable"
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="服务名称" prop="name">
          <el-input v-model.trim="formData.name" placeholder="请输入服务名称" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务类型" prop="config.serviceType">
          <el-select
            class="custom-dialog-select"
            v-model="formData.config.serviceType"
            placeholder="请选择服务类型"
            clearable
            @change="handleServiceTypeChange"
          >
            <el-option
              v-for="item in serviceTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="城市名称" prop="config.cityName">
          <el-select
            class="custom-dialog-select"
            v-model="formData.config.cityName"
            placeholder="请选择城市"
            clearable
            filterable
            @change="handleCityChange"
          >
            <el-option
              v-for="item in cityOptions"
              :key="item.code"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="行政区域" prop="config.adminArea">
          <el-input v-model.trim="formData.config.adminArea" placeholder="请输入行政区域" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item label="服务瓦片地址" prop="config.tileUrl">
          <el-input
            v-model.trim="formData.config.tileUrl"
            placeholder="请输入服务瓦片地址，如：https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="前端密钥" prop="config.frontendKey">
          <el-input v-model.trim="formData.config.frontendKey" placeholder="前端验证密钥" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="服务密钥" prop="config.serviceKey">
          <el-input v-model.trim="formData.config.serviceKey" placeholder="服务token或API密钥" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32" v-if="showZoomLevels">
      <el-col :span="12">
        <el-form-item label="最大缩放级别" prop="config.maxZoom">
          <el-input
            type="number"
            v-model.trim="formData.config.maxZoom"
            placeholder="请输入最大缩放级别"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="最小缩放级别" prop="config.minZoom">
          <el-input
            type="number"
            v-model.trim="formData.config.minZoom"
            placeholder="请输入最小缩放级别"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="描述" prop="remark">
      <el-input
        class="custom-textarea"
        placeholder="请输入描述"
        v-model.trim="formData.remark"
        type="textarea"
        resize="none"
        :rows="3"
        maxlength="500"
      ></el-input>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { ref, onMounted, watch, computed } from "vue";
import { getCityList, getCityCoordinates } from "@/api/accessService";
import { ServiceType, type CityInfo } from "@/interface/business/serviceAccess";

const props = defineProps({
  formData: Object,
  editable: Boolean
});
const formData: any = ref(props.formData);
const editable = ref(props.editable);
watch(
  () => props.formData,
  (val) => {
    resetForm();
    formData.value = val;
  }
);
watch(
  () => props.editable,
  (val) => {
    editable.value = val;
  }
);

/**
 * @description 服务类型选项
 */
const serviceTypeOptions = [
  { label: "天地图", value: ServiceType.TIANDITU },
  { label: "百度地图", value: ServiceType.BAIDU },
  { label: "高德地图", value: ServiceType.AMAP },
  { label: "第三方地图", value: ServiceType.CUSTOM }
];

/**
 * @description 城市选项
 */
const cityOptions = ref<CityInfo[]>([]);

/**
 * @description 是否显示缩放级别设置
 */
const showZoomLevels = computed(() => {
  return formData.value.config?.serviceType === ServiceType.CUSTOM;
});

/**
 * @description 处理服务类型变化
 */
const handleServiceTypeChange = (serviceType: ServiceType) => {
  // 根据服务类型设置默认配置
  switch (serviceType) {
    case ServiceType.TIANDITU:
      formData.value.config.frontendKey = "FIXED_FRONTEND_KEY_2025";
      formData.value.config.tileUrl =
        "https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}";
      break;
    case ServiceType.BAIDU:
      formData.value.config.frontendKey = "FIXED_FRONTEND_KEY_2025";
      formData.value.config.tileUrl =
        "https://maponline{0-3}.bdimg.com/tile/?qt=vtile&x={x}&y={y}&z={z}&styles=pl&scaler=1&udt=20210709&ak={token}";
      break;
    case ServiceType.AMAP:
      formData.value.config.frontendKey = "FIXED_FRONTEND_KEY_2025";
      formData.value.config.tileUrl =
        "https://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}&key={token}";
      break;
    case ServiceType.CUSTOM:
      formData.value.config.frontendKey = "FIXED_FRONTEND_KEY_2025";
      formData.value.config.maxZoom = 18;
      formData.value.config.minZoom = 1;
      break;
  }
};

/**
 * @description 处理城市变化
 */
const handleCityChange = async (cityName: string) => {
  if (cityName) {
    const coordinates = await getCityCoordinates(cityName);
    if (coordinates) {
      formData.value.config.centerLng = coordinates.longitude;
      formData.value.config.centerLat = coordinates.latitude;
    }
  }
};

/**
 * @description 验证URL格式
 */
const validateUrl = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error("请输入服务瓦片地址"));
  }
  const urlRegex = /^https?:\/\/.+/;
  if (!urlRegex.test(value)) {
    return callback(new Error("请输入有效的URL地址"));
  }
  callback();
};

/**
 * @description 验证缩放级别
 */
const validateZoomLevel = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback();
  }
  if (isNaN(Number(value))) {
    callback(new Error("必须输入数字"));
  } else if (Number(value) <= 0 || Number(value) > 20) {
    callback(new Error("缩放级别必须在1-20之间"));
  } else {
    callback();
  }
};

/**
 * @description 表单验证规则
 */
const rules = {
  name: [{ required: true, message: "请输入服务名称", trigger: "blur" }],
  config: {
    serviceType: [{ required: true, message: "请选择服务类型", trigger: "change" }],
    cityName: [{ required: true, message: "请选择城市", trigger: "change" }],
    adminArea: [{ required: true, message: "请输入行政区域", trigger: "blur" }],
    tileUrl: [
      { required: true, message: "请输入服务瓦片地址", trigger: "blur" },
      { validator: validateUrl, trigger: "blur" }
    ],
    frontendKey: [{ required: true, message: "请输入前端密钥", trigger: "blur" }],
    serviceKey: [{ required: true, message: "请输入服务密钥", trigger: "blur" }],
    maxZoom: [{ validator: validateZoomLevel, trigger: "blur" }],
    minZoom: [{ validator: validateZoomLevel, trigger: "blur" }]
  }
};
const form: any = ref(null);

/**
 * @description 提交表单
 */
const submitForm = async (): Promise<any> => {
  formData.value.configValue = JSON.stringify(formData.value.config);
  return (await form.value?.validate()) ? formData.value : null;
};

/**
 * @description 重置表单
 */
const resetForm = () => {
  form.value?.resetFields();
};

/**
 * @description 加载城市数据
 */
const loadCityData = async () => {
  try {
    cityOptions.value = await getCityList();
  } catch (error) {
    console.error("加载城市数据失败:", error);
  }
};

/**
 * @description 组件挂载时初始化
 */
onMounted(async () => {
  resetForm();
  await loadCityData();
});

defineExpose({
  submitForm,
  resetForm
});
</script>
<style scoped lang="scss"></style>
