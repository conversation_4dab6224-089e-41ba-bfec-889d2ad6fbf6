/*
 * @Description: xyq
 * @Autor: silei
 * @Date: 2023-02-01 14:13:19
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-19 15:11:39
 */
import { Module } from "vuex";
import { login } from "@/api/auth";
import localCatch from "@/utils/auth";
const loginModule: Module<any, any> = {
  namespaced: true,
  state() {
    return {
      currentService: "",
      token: localCatch.getCache("Latias"),
      projectId: localCatch.getCache("PROJECT-ID"),
      loginStatus: false,
      userName: "未登录"
    };
  },
  mutations: {
    changeToken(state, token) {
      state.token = token;
    },
    changeProJectId(state, projectId) {
      state.projectId = projectId;
    },
    changeLoginStatus(state, status) {
      state.loginStatus = status;
    },
    changeUserName(state, name) {
      state.userName = name;
    }
  },
  actions: {
    async login({ commit, dispatch }, loginData) {
      // 登录
      try {
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        const loginResult = await login(loginData);
        // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
        if (loginResult) {
          commit("changeLoginStatus", true);
        }
      } catch (error) {
        // commit("changeToken", "");
        localCatch.setCache("Latias", "");
      }
    }
  }
};

export default loginModule;
