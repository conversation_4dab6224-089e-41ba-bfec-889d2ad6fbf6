/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-01-31 16:29:04
 * @LastEditors: silei
 * @LastEditTime: 2023-02-06 14:56:05
 */
interface LayerGroups {
  layerGroups: {
    layerGroup: LayerGroup[];
  };
}
interface LayerGroup {
  name: string;
  href: string;
}
interface LayerGroupDto {
  name: string;
  href?: string;
  mode: string;
  title: string;
  abstractTxt: string;
  enabled: boolean;
  internationalTitle?: string;
  internationalAbstract?: string;
  workspace?: WorkSpace;
  publishables: {
    published: Object[];
  };
  styles?: {
    style: string[];
  };
  bounds?: {
    minx: number;
    maxx: number;
    miny: number;
    maxy: number;
    crs: Object;
  };
  metadata?: Object;
  dateModified?: string;
}
