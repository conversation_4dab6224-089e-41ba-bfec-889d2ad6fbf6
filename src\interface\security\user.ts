/**
 * @description 用户管理相关接口定义
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

/**
 * @interface SysUserDto
 * @description 系统用户数据传输对象
 */
export interface SysUserDto {
  /** 用户ID */
  id?: number;
  /** 用户编码 */
  code?: string;
  /** 用户名 */
  username?: string;
  /** 昵称 */
  nickname?: string;
  /** 姓名 */
  name?: string;
  /** 邮箱 */
  email?: string;
  /** 电话 */
  phone?: string;
  /** 身份证号 */
  idNo?: string;
  /** 性别 */
  gender?: string;
  /** 生日 */
  birthday?: string;
  /** 关联客户端编码 */
  relationClientCode?: string;
  /** 关联客户端名称 */
  relationClientName?: string;
  /** 实名状态 */
  realStatus?: number;
  /** 来源 */
  source?: string;
  /** 激活状态 */
  activation?: number;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 类型 */
  type?: string;
  /** 来源平台 */
  sourcePlatform?: string;
  /** 头像 */
  photo?: string;
  /** 钉钉ID */
  dingId?: string;
  /** 创建人 */
  createBy?: string;
}

/**
 * @interface UserListResponse
 * @description 用户列表响应接口
 */
export interface UserListResponse {
  /** 状态码 */
  code: number;
  /** 消息 */
  msg: string;
  /** 用户列表数据 */
  data: SysUserDto[];
}

/**
 * @interface UserSelectOption
 * @description 用户下拉选项接口
 */
export interface UserSelectOption {
  /** 显示标签 */
  label: string;
  /** 选项值 */
  value: string;
  /** 用户编码 */
  code?: string;
  /** 用户ID */
  id?: number;
}
