<!--
 * @Author: xiao
 * @Date: 2023-03-02 17:21:20
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-19 09:35:45
 * @Description:
-->
<template>
  <div class="custom-text">高速·稳定</div>
  <div class="custom-title">
    <div>
      <img :src="require('@/assets/img/login/logo.png')" alt="" />
      <span>{{ title }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
const title = process.env.VUE_APP_NAME;
</script>

<style scoped lang="scss">
.custom-text {
  position: absolute;
  left: 226px;
  top: calc(443px / 1080px * 100%);
  font-size: 40px;
  font-family: HuXiaoBo-NanShen, HuXiaoBo-NanShen-Regular;
  font-weight: 400;
  text-align: left;
  color: #ffffff;
  letter-spacing: 8px;
}

.custom-title {
  position: absolute;
  left: 213px;
  top: calc(695px / 1080px * 100%);
  > div {
    display: flex;
    align-items: center;
    span {
      font-size: 50px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #333333;
      margin: -20px 0 0 13px;
    }
  }
}

.custom-copyright {
  position: absolute;
  left: 226px;
  top: calc(960px / 1080px * 100%);
  font-size: 18px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: left;
  color: #7d8da1;
}
</style>
