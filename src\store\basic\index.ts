/*
 * @Description: xyq
 * @Autor: silei
 * @Date: 2023-02-01 14:13:19
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-02-28 18:01:11
 */
import { Module } from "vuex";
const basicDataModule: Module<any, any> = {
  namespaced: true,
  state() {
    return {
      avtiveMenu: "",
      historyOperate: [],
      serverType: ""
    };
  },
  mutations: {
    changeAvtiveMenu(state, key) {
      state.avtiveMenu = key;
    },
    changeHistoryOperate(state, history) {
      state.historyOperate = history;
    },
    changeServerType(state, status) {
      state.serverType = status;
    }
  }
};

export default basicDataModule;
