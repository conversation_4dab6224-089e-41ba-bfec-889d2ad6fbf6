<!--
 * @Description: 服务管理界面
 * @Autor: silei
 * @Date: 2023-02-01 11:57:47
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 11:44:25
-->
<template>
  <div class="custom-content">
    <el-collapse v-loading="loading" v-model="activeName" accordion>
      <div class="custom-table-header-row">
        <div class="custom-table-title">服务管理</div>
        <div class="custom-button-group">
          <div class="search-input">
            <el-input
              v-model="search"
              clearable
              class="w-50 m-2"
              placeholder="输入服务名称..."
              :prefix-icon="Search"
              @change="filterServers"
            />
          </div>

          <div class="custom-table-wihte" @click="reloadService">
            <img src="../../assets/img/reStart.png" alt="" />
            <span>重启所有服务</span>
          </div>
          <div class="custom-table-add" @click="createService">
            <img src="../../assets/img/add.png" alt="" />
            <span>创建服务</span>
          </div>
        </div>
      </div>
      <div class="sever-box">
        <div
          class="server-item"
          v-for="(service, index) in services"
          @click="selectServer(service)"
          :key="index"
        >
          <img v-if="severActive === service.title" :src="service.activeImg" alt="" />
          <img v-else :src="service.img" alt="" />
          <div class="right">
            <div class="titile" :class="[severActive === service.title ? 'active-title' : '']">
              {{ service.title }}
            </div>
            <span>{{ service.children.length }}个服务</span>
          </div>
        </div>
      </div>
      <ServiceList v-model="serverChild" @service-deleted="loadLayerGroups" />
    </el-collapse>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="创建服务"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">创建服务</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <ServiceCeate @canceled="dialogVisible = false" @completed="createCompleted" />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import { onMounted, Ref, ref } from "vue";
import ServiceList from "./ServiceList.vue";
import ServiceCeate from "./ServiceCeate.vue";
import { BaseServiceInfo, reload, SecurityManager, ServiceUtil } from "geoserver-manager";
import localCache from "@/utils/auth";
import store from "@/store";
const count = ref(0);
const severActive = ref("");
const selectServer = (server: any) => {
  severActive.value = server.title;
  if (count.value++ > 0) {
    store.commit("basic/changeServerType", server.title);
  }
  filterServers();
};
const activeName: Ref<string> = ref("");
const serverChild = ref([]);
// eslint-disable-next-line no-undef
const services: Ref<any[]> = ref([]);
const dialogVisible: Ref<boolean> = ref(false);
const search = ref("");
const filterServers = async () => {
  const list = services.value.find((s) => s.title === severActive.value);
  serverChild.value = list.children.filter((s: BaseServiceInfo) => s.title.includes(search.value));
};
/**
 * 获取图层组列表
 */
const loadLayerGroups = async () => {
  const serviceList = await ServiceUtil.getAllServices();
  // 根据角色筛选服务
  const manager = await SecurityManager.Instance();
  const roles = localCache.getCache("userRoles");
  if (!roles.includes("ADMIN")) {
    for (const service of serviceList) {
      service.children = service.children.filter((s: BaseServiceInfo) => {
        const b = manager.isServiceReadable(s.name, roles);
        return b;
      });
    }
  }
  serviceList.forEach((item) => {
    if (item.title === "地图服务") {
      item.activeImg = require("@/assets/img/serverType/map-active.png");
      item.img = require("@/assets/img/serverType/map.png");
    }
    if (item.title === "三维服务") {
      item.activeImg = require("@/assets/img/serverType/three-dimensional-active.png");
      item.img = require("@/assets/img/serverType/three-dimensional.png");
    }
    if (item.title === "地形服务") {
      item.activeImg = require("@/assets/img/serverType/terrain-active.png");
      item.img = require("@/assets/img/serverType/terrain.png");
    }
    if (item.title === "地图聚合") {
      item.activeImg = require("@/assets/img/serverType/polymerization-active.png");
      item.img = require("@/assets/img/serverType/polymerization.png");
    }
  });
  console.log(serviceList, "-------------------");
  services.value = serviceList;
  let currentServer = null;
  services.value.forEach((item) => {
    if (item.title === severActive.value) {
      currentServer = item;
    }
  });
  selectServer(currentServer);
};

const createService = () => {
  dialogVisible.value = true;
};
const createCompleted = () => {
  dialogVisible.value = false;
  loadLayerGroups();
};
const loading = ref(false);
/**
 * 重启所有服务
 */
const reloadService = async () => {
  loading.value = true;
  try {
    await reload();
    await loadLayerGroups();
  } finally {
    loading.value = false;
  }
};
const loadServices = async () => {
  loading.value = true;
  try {
    await loadLayerGroups();
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  severActive.value = (store.state as any).basic.serverType
    ? (store.state as any).basic.serverType
    : "地图服务";
  console.log(severActive.value);
  loadServices();
});
</script>
<style lang="scss" scoped>
.search-input {
  width: 350px;
  display: flex;
  margin-right: 20px;
  height: 45px;
}

.sever-box {
  width: 1600px;
  height: 80px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.server-item {
  cursor: pointer;
  width: 200px;
  height: 100%;
  display: flex;
  align-items: center;

  img {
    width: 35px;
    height: 35px;
    margin: 0 20px;
  }

  .right {
    width: 120px;
    display: flex;
    flex-direction: column;

    .titile {
      width: 72px;
      height: 27px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #232a3a;
    }

    span {
      width: 88px;
      height: 20px;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #7d8da1;
    }
  }
}

.active-title {
  color: #4076f3 !important;
}

::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
