<!--
 * @Description: 
 * @Date: 2023-02-06 11:42:26
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-19 12:02:05
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title">风格样式</div>
      <div class="custom-button-group">
        <div class="custom-table-wihte" @click="styleAdd">
          <img src="../../../assets/img/table-add.png" alt="" />
          <span>新增风格</span>
        </div>
        <div class="custom-table-add" @click="styleExport">
          <img src="../../../assets/img/table-import.png" alt="" />
          <span>导入样式</span>
        </div>
      </div>
    </div>
    <div class="custom-table-box">
      <el-form :model="queryform" label-position="top" class="custom-table-search-box">
        <div class="custom-search-item">
          <el-form-item label="风格名称">
            <el-input class="custom-input" v-model="queryform.name" placeholder="请输入" />
          </el-form-item>
        </div>
        <div class="custom-search-item">
          <el-form-item class="online-form-btns">
            <div class="custom-query-button" @click="queryData">查询</div>
            <div class="custom-reset-button" @click="reset">重置</div>
          </el-form-item>
        </div>
      </el-form>
      <el-table
        :data="selectTable"
        class="custom-table"
        header-row-class-name="custom-header-row"
        header-cell-class-name="custom-header-cell"
        style="width: 96%"
      >
        <el-table-column align="center" width="50">
          <template v-slot="scop">
            {{ scop.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" width="550" />
        <el-table-column prop="format" label="格式" width="350" />
        <el-table-column prop="type" label="类型" width="350">
          <template v-slot="scope">
            {{ scope.row.type === 0 ? "系统" : scope.row.type === 1 ? "配置" : "文件" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template v-slot="scope">
            <el-button :disabled="scope.row.type === 0" text @click="editHandler(scope.row)"
              >编辑</el-button
            >
            <el-button
              :disabled="scope.row.type === 0"
              text
              class="table-del-btn"
              @click="delteHandler(scope.row)"
              >删除</el-button
            >
            <el-button text @click="downloadHandler(scope.row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination-box">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :small="small"
          :disabled="disabled"
          :background="background"
          layout="prev, pager, next, jumper"
          :total="total"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="28%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">{{ dialogTitle }}</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <el-form
        ref="styleFormRef"
        :model="ruleForm"
        :rules="rules"
        :size="formSize"
        label-width="140px"
        status-icon
        label-position="top"
        class="demo-ruleForm custom-sub-form"
      >
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item label="图层类型" prop="existStyle">
              <el-select
                :disabled="dialogTitle !== '风格添加'"
                v-model="ruleForm.existStyle"
                class="m-2"
                placeholder="Select"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item label="风格名称" prop="styleName">
              <el-input
                :disabled="dialogTitle !== '风格添加'"
                v-model="ruleForm.styleName"
                placeholder="请输入风格名称"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item v-if="ruleForm.existStyle === 'point'" label="标签类型" prop="lableType">
              <el-select v-model="ruleForm.lableType" placeholder="请选择" class="m-2">
                <el-option
                  v-for="item in lableOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item v-if="ruleForm.existStyle === 'point'" label="标签颜色" prop="lableColor">
              <el-color-picker v-model="ruleForm.lableColor" /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item
              v-if="ruleForm.existStyle === 'point'"
              label="标签比例"
              prop="lableProportion"
            >
              <el-input
                type="number"
                placeholder="请输入标签比列"
                v-model="ruleForm.lableProportion"
              /> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              v-if="ruleForm.existStyle === 'point'"
              label="标记属性"
              prop="tagProperties"
            >
              <el-input
                placeholder="请输入标记属性"
                v-model="ruleForm.tagProperties"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item
              v-if="ruleForm.existStyle === 'polyline' || ruleForm.existStyle === 'polygon'"
              label="线宽度"
              prop="lineWidth"
            >
              <el-input type="number" placeholder="请输入线宽度" v-model="ruleForm.lineWidth" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="ruleForm.existStyle === 'polyline' || ruleForm.existStyle === 'polygon'"
              label="线颜色"
              prop="lineColor"
            >
              <el-color-picker v-model="ruleForm.lineColor" /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="12" v-if="ruleForm.existStyle === 'point'">
            <el-form-item label="文本颜色" prop="textColor">
              <el-color-picker v-model="ruleForm.textColor" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="ruleForm.existStyle === 'polygon'"
              label="多边形填充颜色"
              prop="fillColor"
            >
              <el-color-picker v-model="ruleForm.fillColor" />
            </el-form-item>
            <el-form-item
              v-if="ruleForm.existStyle === 'point'"
              label="标签文本大小"
              prop="labelTextSize"
            >
              <el-input
                type="number"
                placeholder="请输入文本大小"
                v-model="ruleForm.labelTextSize"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-cancle" @click="resetForm(styleFormRef)">取消</div>
          <div class="dialog-submit" @click="submitForm(styleFormRef)">确定</div>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      :before-close="exporthandleClose"
      :close-on-click-modal="false"
      v-model="exportVisible"
      :title="exportDialogTitle"
      width="28%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">{{ exportDialogTitle }}</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <el-form
        ref="styleExportForm"
        :model="styleDataForm"
        :rules="rules"
        label-position="top"
        class="demo-ruleForm custom-sub-form"
        :size="formSize"
        status-icon
      >
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item label="风格名称" prop="stylename">
              <el-input
                :disabled="exportDialogTitle !== '风格导入'"
                v-model="styleDataForm.stylename"
                placeholder="请输入风格名称"
              /> </el-form-item
          ></el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="24">
            <el-form-item label="风格文件" prop="fileData">
              <el-upload
                v-model:file-list="fileData"
                class="upload-demo"
                :auto-upload="false"
                accept=".sld,.zip,.json"
                :on-change="handleFileChanged"
              >
                <div class="upload-buttom">
                  <el-icon class="el-icon--upload"><upload-filled /></el-icon><span>上传文件</span>
                </div>
                <template #tip> </template>
              </el-upload> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-cancle" @click="resetExportForm(styleExportForm)">取消</div>
          <div class="dialog-submit" @click="submitExportForm(styleExportForm)">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import {
  getStyles,
  importStyle,
  delStyle,
  importPutStyle,
  importPutZipStyle,
  styleStyles,
  styleDetial,
  styleDetials,
  styleEdit,
  downloadStyle
} from "@/api/style";
import { UploadFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { inputSearch, setScoped } from "@/utils/basic";
const formSize = ref("default");
const dialogTitle = ref("");
const exportDialogTitle = ref("");
const styleFormRef = ref<FormInstance>();
const styleExportForm = ref<FormInstance>();
const ruleForm = reactive({
  name: "",
  existStyle: "point",
  styleName: "",
  lableType: "",
  lableColor: "#409EFF",
  lableProportion: 0,
  tagProperties: "",
  textColor: "#409EFF",
  imgUrl: "",
  lineColor: "#409EFF",
  lineWidth: 0,
  fillColor: "#409EFF",
  labelTextSize: 0
});
const QueryForm = class {
  modifiedStartDate = "";
  modifiedEndDate = "";
  // eslint-disable-next-line no-useless-constructor
  constructor(
    public name = "",
    public state = "",
    public dataCoordinates = "",
    public pageNum = 1,
    public pageSize = 10,
    public editDateInterval = []
  ) {}
};
const searchStart = ref(false);
const searchData: any = ref([]);
const queryform = ref(new QueryForm());
const searchDataDeal = ref([]);
const queryData = () => {
  currentPage.value = 1;
  searchStart.value = true;
  searchData.value = inputSearch(tableData.value, queryform.value.name, "name");
  searchDataDeal.value = fenge(searchData.value, pageSize.value);
  selectTable.value = searchDataDeal.value[currentPage.value - 1];
  total.value = searchData.value.length;
};
const reset = () => {
  currentPage.value = 1;
  searchStart.value = false;
  queryform.value = new QueryForm();
  onloadPage();
};
const styleDataForm = reactive({
  stylename: "",
  stylefile: ""
});
const fileData = ref<File[]>([]);
const options = [
  {
    value: "point",
    label: "点"
  },
  {
    value: "polyline",
    label: "线"
  },
  {
    value: "polygon",
    label: "面"
  }
];
const lableOptions = [
  {
    label: "square",
    value: "square"
  },
  {
    label: "circle",
    value: "circle"
  },
  {
    label: "triangle",
    value: "triangle"
  },
  {
    label: "star",
    value: "star"
  },
  {
    label: "cross",
    value: "cross"
  },
  {
    label: "x",
    value: "x"
  }
];
const uploadXml = ref("");
const isZIP = ref(true);
const rules = reactive<FormRules>({
  existStyle: [{ required: true, message: "请选择特层类型", trigger: "blur" }],
  styleName: [{ required: true, message: "请输入风格名称", trigger: "blur" }],
  lableType: [{ required: true, message: "请选择标签类型", trigger: "blur" }],
  lableColor: [{ required: true, message: "请输入标签颜色", trigger: "blur" }],
  lableProportion: [{ required: true, message: "请输入标签比例", trigger: "blur" }],
  tagProperties: [{ required: true, message: "请输入标记属性", trigger: "blur" }],
  lineWidth: [{ required: true, message: "请输入线宽", trigger: "blur" }],
  lineColor: [{ required: true, message: "请选择线颜色", trigger: "blur" }],
  textColor: [{ required: true, message: "请选择文本颜色", trigger: "blur" }],
  labelTextSize: [{ required: true, message: "请输入便签文本大小", trigger: "blur" }],
  stylename: [{ required: true, message: "请输入风格名称", trigger: "blur" }]
});
const handleFileChanged = async (uploadFile: any) => {
  fileData.value = [];
  fileData.value.push(uploadFile.raw!);
  styleDataForm.stylefile = uploadFile.name;
  if (uploadFile.name.split(".")[1] === "sld" || uploadFile.name.split(".")[1] === "json") {
    const reader = new FileReader();
    reader.readAsText(uploadFile.raw);
    reader.onload = (e: any) => {
      // 读取文件内容
      const fileString = e.target.result;
      console.log(fileString);
      uploadXml.value = fileString;
    };
  }
};
const handleClose: any = (done: () => void) => {
  ruleForm.name = "";
  ruleForm.styleName = "";
  ruleForm.lableType = "";
  ruleForm.lableProportion = 0;
  ruleForm.tagProperties = "";
  ruleForm.imgUrl = "";
  ruleForm.lineWidth = 0;
  ruleForm.styleName = "";
  styleFormRef.value?.resetFields();
  dialogVisible.value = false;
};
const exporthandleClose: any = (done: () => void) => {
  styleDataForm.stylename = "";
  styleDataForm.stylefile = "";
  styleExportForm.value?.resetFields();
  exportVisible.value = false;
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (dialogTitle.value === "风格添加") {
        await styleStyles(ruleForm.styleName, {
          styleInfoDto: {
            name: ruleForm.styleName,
            type: ruleForm.existStyle,
            lineColor: ruleForm.lineColor,
            lineWidth: Number(ruleForm.lineWidth),
            fillColor: ruleForm.fillColor,
            labelType: ruleForm.lableType,
            labelColor: ruleForm.lableColor,
            labelSize: Number(ruleForm.lableProportion),
            labelPropertyName: ruleForm.tagProperties,
            labelTextColor: ruleForm.textColor,
            labelTextSize: Number(ruleForm.labelTextSize)
          }
        });
        setScoped(`风格添加-${ruleForm.styleName}`);
        ElMessage({
          message: "新增成功",
          type: "success"
        });
      } else {
        await styleEdit(ruleForm.styleName, {
          styleInfoDto: {
            name: ruleForm.styleName,
            type: ruleForm.existStyle,
            lineColor: ruleForm.lineColor,
            lineWidth: Number(ruleForm.lineWidth),
            fillColor: ruleForm.fillColor,
            labelType: ruleForm.lableType,
            labelColor: ruleForm.lableColor,
            labelSize: Number(ruleForm.lableProportion),
            labelPropertyName: ruleForm.tagProperties,
            labelTextColor: ruleForm.textColor,
            labelTextSize: Number(ruleForm.labelTextSize)
          }
        });
        setScoped(`风格修改-${ruleForm.styleName}`);
        ElMessage({
          message: "修改成功",
          type: "success"
        });
      }
      handleClose();
      await onloadPage();
      if (queryform.value.name !== "") {
        queryData();
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};
const submitExportForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (exportDialogTitle.value === "风格导入") {
        await importStyle({
          style: {
            name: styleDataForm.stylename,
            filename: styleDataForm.stylefile,
            metadata: {
              uploadMethod: "file"
            }
          }
        });
        setScoped(`风格导入-${styleDataForm.stylefile}`);
        ElMessage({
          message: "新增成功",
          type: "success"
        });
      }
      if (styleDataForm.stylefile.endsWith(".zip")) {
        await importPutZipStyle(styleDataForm.stylename, fileData.value[0]);
      } else {
        const style = styleDataForm.stylefile.endsWith(".json")
          ? "application/vnd.geoserver.mbstyle+json"
          : "application/vnd.ogc.sld+xml";
        await importPutStyle(styleDataForm.stylename, uploadXml.value, style);
      }
      uploadXml.value = "";
      if (exportDialogTitle.value !== "风格导入") {
        ElMessage({
          message: "修改成功",
          type: "success"
        });
        setScoped(`风格导入修改-${styleDataForm.stylefile}`);
      }
      exporthandleClose();
      await onloadPage();
      if (queryform.value.name !== "") {
        queryData();
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};
const resetExportForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  exportVisible.value = false;
};
const tableData: any = ref([]);
const dealTableData: any = ref([]);
const selectTable: any = ref([]);
const getAllListData = async () => {
  tableData.value = [];
  dealTableData.value = [];
  selectTable.value = [];
  const data = await getStyles();
  tableData.value = data;
  dealTableData.value = fenge(tableData.value, pageSize.value);
  selectTable.value = dealTableData.value[currentPage.value - 1];

  total.value = tableData.value.length;
};
const fenge = (arr: [], N: number) => {
  const result: any = [];
  for (let i = 0; i < arr.length; i += N) {
    result.push(arr.slice(i, i + N));
  }
  return result;
};
/* const handleSizeChange = (val: number) => {
  dealTableData.value = fenge(tableData.value, val);
  currentPage.value = 1;
  selectTable.value = dealTableData.value[0];
}; */
const handleCurrentChange: any = (val: number) => {
  if (!searchStart.value) {
    selectTable.value = dealTableData.value[val - 1];
  } else {
    selectTable.value = searchDataDeal.value[val - 1];
  }
};
//  编辑
const editHandler = async (row: any) => {
  console.log(row);
  if (row.type === 2) {
    const result = await styleDetial(row.name);
    styleDataForm.stylename = result.style.name;
    styleDataForm.stylefile = result.style.filename;
    exportDialogTitle.value = "修改样式导入";
    exportVisible.value = true;
  }
  if (row.type === 1) {
    const result = await styleDetials(row.name);
    ruleForm.existStyle = result.type;
    ruleForm.styleName = result.name;
    ruleForm.lableType = result.labelType;
    ruleForm.lableColor = result.labelColor;
    ruleForm.lableProportion = result.labelSize;
    ruleForm.tagProperties = result.labelPropertyName;
    ruleForm.textColor = result.labelTextColor;
    ruleForm.lineColor = result.lineColor;
    ruleForm.lineWidth = result.lineWidth;
    ruleForm.fillColor = result.fillColor;
    ruleForm.labelTextSize = result.labelTextSize;
    dialogTitle.value = "修改风格";
    ruleForm.name = row.name;
    dialogVisible.value = true;
  }
};
//  删除
const delteHandler = async (row: any) => {
  ElMessageBox.confirm("确定要删除当前样式吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  }).then(async () => {
    await delStyle(row.name);
    setScoped(`风格删除-${row.name}`);
    ElMessage({
      message: "删除成功",
      type: "success"
    });
    await onloadPage();
    if (queryform.value.name !== "") {
      queryData();
    }
  });
};
//  下载
const downloadHandler = async (row: any) => {
  const data: string = await downloadStyle(
    row.name,
    row.format === "mbstyle" ? "application/vnd.geoserver.mbstyle+json" : undefined
  );
  const a = document.createElement("a");
  a.href = URL.createObjectURL(new Blob([data], { type: "application/octet-stream" }));
  a.download = row.name + "." + (row.format === "mbstyle" ? "json" : "sld");
  a.click();
  URL.revokeObjectURL(a.href);
};
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const small = ref(false);
const background = ref("#ffffff");
const disabled = ref(false);
const dialogVisible = ref(false);
const exportVisible = ref(false);
const styleAdd = () => {
  dialogTitle.value = "风格添加";
  ruleForm.styleName = "";
  ruleForm.lableType = "";
  ruleForm.lableProportion = 0;
  ruleForm.tagProperties = "";
  ruleForm.imgUrl = "";
  ruleForm.lineWidth = 0;
  ruleForm.styleName = "";
  dialogVisible.value = true;
};
const styleExport = () => {
  exportDialogTitle.value = "风格导入";
  styleDataForm.stylename = "";
  styleDataForm.stylefile = "";
  fileData.value = [];
  exportVisible.value = true;
};
const onloadPage = async () => {
  await getAllListData();
  dealTableData.value = fenge(tableData.value, pageSize.value);
};

onMounted(async () => {
  onloadPage();
});
</script>
<style lang="scss" scoped>
.allData {
  margin: auto;
  width: 1300px;
  height: auto;
}
.top-operate {
  margin-top: 20px;
  display: flex;
  height: 50px;
  width: 100%;
  justify-content: flex-end;
}
::v-deep(.el-transfer__buttons) {
  padding: 0 10px;
  width: 80px;
  span {
    font-size: 12px;
    color: #000000;
    font-weight: 400;
  }
  .el-transfer__button:nth-child(2) {
    margin: 10px 0 0 0px;
    font-size: 14px;
  }
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
::v-deep(.el-transfer-panel__body) {
  height: 130px;
}
::v-deep(.el-checkbox__label) {
  font-size: 12px;
}
::v-deep(.el-dialog__header) {
  display: flex;
}
::v-deep(.el-transfer-panel__list) {
  height: 130px;
}
.el-select {
  width: 100%;
}
::v-deep(.el-color-picker) {
  width: 100%;
}
::v-deep(.el-color-picker__trigger) {
  width: 100%;
}
::v-deep(.el-color-picker .el-color-picker__icon) {
  position: absolute;
  right: 8px;
}
.upload-demo {
  width: 100%;
  height: 150px;
  background: #fafafa;
  border: 1px dashed #dee0df;
}
::v-deep(.el-upload) {
  margin-left: 50%;
  margin-top: 40px;
  transform: translate(-50%);
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 100px;
  .el-upload__text {
    font-size: 14px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #5485b2;
    margin-bottom: 7px;
  }
}
::v-deep(.el-table td.el-table__cell) {
  border-bottom: 1px solid #ebebeb;
}
.upload-buttom {
  width: 120px;
  height: 32px;
  background: #ffffff;
  border: 1px solid #dee0df;
  display: flex;
  align-items: center;
  justify-content: center;
  span {
    font-size: 14px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: #334d6e;
  }
}
</style>
