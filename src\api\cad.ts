/*
 * @Description: cad地图服务
 * @Autor: silei
 * @Date: 2023-12-12 11:25:38
 * @LastEditors: silei
 * @LastEditTime: 2023-12-12 13:53:02
 */
import { CadServiceInfo } from "@/interface/business/cadServiceInfo";
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
export const getServices = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/cad"
  });
  return result;
};
export const getServiceByName = async (serverName: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/cad/dxf/${serverName}`
  });
  return result;
};
export const createService = async (info: CadServiceInfo) => {
  const result: any = await hRequest.post<DataType>({
    url: `/cad/dxf`,
    data: {
      cadInfo: info
    }
  });
  return result;
};

export const editService = async (info: CadServiceInfo) => {
  const result: any = await hRequest.put<DataType>({
    url: `/cad/dxf/${info.name}`,
    data: {
      cadInfo: info
    }
  });
  return result;
};

export const deleteService = async (serviceName: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/cad/dxf/${serviceName}`
  });
  return result;
};
