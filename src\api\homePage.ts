/*
 * @Description: 用户权限接口
 * @Autor: silei
 * @Date: 2023-01-31 13:50:50
 * @LastEditors: silei
 * @LastEditTime: 2023-03-13 14:28:51
 */
import hRequest from "@/utils/http";
import csvTo<PERSON>son from "csvtojson";
import { DataType } from "@/utils/http/types";
export const getMonitorRequest = async (data: {}) => {
  const result: any = await hRequest.get<DataType>({
    url: "/monitor/requests",
    params: data,
    maxRedirects: 0,
    timeout: 10000
  });
  const json = await csvToJson().fromString(result);
  return json;
};
export const getLoginRecord = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/monitor/requests",
    params: {
      filter: "Path:EQ:/auth/login",
      order: "EndTime;DESC"
    },
    headers: {
      Accept: "application/csv"
    }
  });
  const json = await csvToJson().fromString(result);
  return json;
};
