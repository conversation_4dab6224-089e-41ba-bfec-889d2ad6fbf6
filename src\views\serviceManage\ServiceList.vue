<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-14 09:33:07
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 11:42:28
-->
<template>
  <div id="serviceContainer">
    <div class="service-container">
      <ServiceCard
        v-for="(service, index) in services"
        :key="index"
        v-model="services[index]"
        @deleted="onServiceDeleted(service)"
      />
    </div>
    <div class="custom-pagination-box">
      <el-pagination
        v-model:current-page="pageInfo.currentPage"
        v-model:page-size="pageInfo.pageSize"
        layout="prev, pager, next, jumper"
        :total="pageInfo.total"
        background
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import ServiceCard from "./ServiceCard.vue";
import { onMounted, PropType, Ref, ref, watch } from "vue";
import { BaseServiceInfo } from "geoserver-manager";

const props = defineProps({
  modelValue: {
    // eslint-disable-next-line no-undef
    type: Object as PropType<BaseServiceInfo[]>,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  }
});
watch(
  () => props.modelValue,
  (newValue, oldValue) => {
    serviceList.value = props.modelValue;
    serviceList.value.sort((a, b) => {
      return a.dateModified < b.dateModified ? 1 : -1;
    });
    handleCurrentChange(1);
  }
);
const pageInfo = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});
// eslint-disable-next-line no-undef
const services: Ref<BaseServiceInfo[]> = ref([]);
const serviceList: Ref<BaseServiceInfo[]> = ref([]);
// 处理当前页
const handleCurrentChange = (val: number) => {
  pageInfo.value.total = serviceList.value.length;
  const pageIndex = val - 1;
  // 查询页码
  services.value = serviceList.value.filter((service, index) => {
    if (
      index >= pageIndex * pageInfo.value.pageSize &&
      index < (pageIndex + 1) * pageInfo.value.pageSize
    ) {
      return true;
    }
    return false;
  });
};
const emits = defineEmits(["serviceDeleted"]);
const onServiceDeleted = (service: any) => {
  emits("serviceDeleted", service);
};
onMounted(() => {
  serviceList.value = props.modelValue;
  serviceList.value.sort((a, b) => {
    return a.dateModified < b.dateModified ? 1 : -1;
  });
  handleCurrentChange(1);
});
</script>
<style lang="scss" scoped>
.service-container {
  width: 100%;
  height: 610px;
  display: flex;
  flex-wrap: wrap;
}
</style>
