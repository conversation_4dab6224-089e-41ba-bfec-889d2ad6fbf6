<!--
 * @Description: 服务创建结果显示
 * @Autor: silei
 * @Date: 2023-04-06 14:53:03
 * @LastEditors: silei
 * @LastEditTime: 2023-04-06 15:20:25
-->
<template>
  <el-table
    :data="modelValue"
    class="custom-table lyaer-table border"
    header-row-class-name="custom-header-row"
    header-cell-class-name="custom-header-cell"
    style="width: 97%"
  >
    <el-table-column label="服务" width="150">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span>{{ scope.row.name }}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="状态" width="100">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <el-icon v-if="scope.row.status === 1" :size="18" color="#00ff00">
            <SuccessFilled />
          </el-icon>
          <el-icon v-else :size="18" color="#ff0000">
            <CircleCloseFilled />
          </el-icon>
        </div>
      </template>
    </el-table-column>
    <el-table-column label="详情">
      <template #default="scope">
        <div style="display: flex; align-items: center">
          <span>{{ scope.row.details }}</span>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script lang="ts" setup>
import { CircleCloseFilled, SuccessFilled } from "@element-plus/icons-vue";
import { PropType } from "vue";

interface ServiceResult {
  name: string;
  status: number;
  details: string;
}
defineProps({
  modelValue: Object as PropType<ServiceResult>
});
</script>
