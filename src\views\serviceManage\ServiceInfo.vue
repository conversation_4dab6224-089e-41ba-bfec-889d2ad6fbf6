<!--
 * @Description: 服务信息
 * @Autor: silei
 * @Date: 2023-02-01 11:34:24
 * @LastEditors: silei
 * @LastEditTime: 2024-05-29 17:16:34
-->
<template>
  <div class="custom-content">
    <div class="info-bg">
      <el-row class="card-header" :gutter="32">
        <el-col class="title-left" :span="20"
          ><img @click="cancel" src="../../assets/img/server-back.png" alt="" /><span
            @click="cancel"
            >返回 <b>|</b></span
          >
          <div class="text">服务详情</div>
        </el-col>
        <el-col class="server-title-right" :span="4">
          <div @click="cancel" class="cancle">取消</div>
          <div @click="saveService" class="save">保存</div>
        </el-col>
      </el-row>

      <el-row class="serve-detial" :gutter="32">
        <el-col class="serve-detial-left" :span="6">
          <el-image
            class="defaultImg"
            v-if="service?.serviceTypeString === '地图服务'"
            :src="service.thumbnail + '?' + token"
          >
            <template #error>
              <div class="image-slot">
                <img class="defaultImg" src="../../assets/img/server-2d.png" alt="" />
              </div>
            </template>
          </el-image>
          <img
            class="defaultImg"
            v-else-if="
              service?.serviceTypeString === '三维服务' || service?.serviceTypeString === '地形服务'
            "
            src="../../assets/img/server-3d.png"
            alt=""
          />
          <img class="defaultImg" v-else src="../../assets/img/server-2d.png" alt="" />
        </el-col>
        <el-col class="serve-detial-right" :span="18">
          <el-row>
            <div class="right-title">{{ service?.name }}</div>
            <el-tooltip
              v-if="service.errorMsg"
              :content="service.errorMsg"
              placement="right"
              effect="light"
            >
              <span class="status-error" type="primary">异常</span>
            </el-tooltip>
            <span v-else class="status-normal" type="primary">正常</span>
          </el-row>

          <div>
            <table border="1">
              <tr>
                <td class="title">服务标题</td>
                <td class="content">
                  <div class="server-edit">
                    <span v-if="!titleEditable">{{ service.title }}</span>
                    <el-input
                      v-if="titleEditable"
                      v-model="service.title"
                      @blur="titleEditable = false"
                    ></el-input>
                    <div class="edit-icon" v-if="!titleEditable" @click="titleEditable = true">
                      <img src="../../assets/img/server-edit.png" alt="" />
                    </div>
                  </div>
                </td>
                <td class="title">服务类型</td>
                <td class="content">
                  <span>{{ service?.serviceTypeString }}</span>
                </td>
              </tr>
              <tr>
                <td class="title">服务描述</td>
                <td class="content">
                  <div class="server-edit">
                    <span v-if="!descriptionEditable">{{ service.description }}</span>
                    <el-input
                      v-if="descriptionEditable"
                      v-model="service.description"
                      @blur="descriptionEditable = false"
                    ></el-input>
                    <div
                      class="edit-icon"
                      v-if="!descriptionEditable"
                      @click="descriptionEditable = true"
                    >
                      <img src="../../assets/img/server-edit.png" alt="" />
                    </div>
                  </div>
                </td>
                <td class="title">服务预览</td>
                <td class="content">
                  <span
                    ><el-space wrap spacer="|">
                      <el-link
                        v-for="preview in previewUrl"
                        :key="preview.type"
                        @click="handlePreview(preview)"
                        >{{ preview.name }}</el-link
                      >
                    </el-space>
                  </span>
                </td>
              </tr>
              <tr>
                <td class="title">服务地址</td>
                <td class="content" colspan="3">
                  <div class="server-url">
                    <el-row
                      class="server-url-item"
                      v-for="(serviceInfo, index) in service?.urls"
                      :key="index"
                    >
                      <el-link @click="handleOverview(serviceInfo.overviewUrl)">{{
                        serviceInfo.url
                      }}</el-link>
                    </el-row>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <span class="divider-header">服务接口</span>
      </el-row>
      <el-row>
        <div class="my-server">
          <el-checkbox-group
            class="my-server-item"
            v-model="service.enabledServices"
            :disabled="
              !(service.type === ServiceType.MAP || service.type === ServiceType.POLYMERIZE)
            "
            :min="1"
            @change="handleValueChanged"
          >
            <el-checkbox v-for="item in service.supportServices" :label="item" :key="item" />
          </el-checkbox-group>
        </div>
      </el-row>
      <el-row>
        <span class="divider-header">数据源</span>
        <div
          v-if="serviceType === 'geocoding'"
          @click="updateGeocodingIndex"
          class="button-primary"
        >
          更新索引
        </div>
        <DatasourceInfo v-model="service.datasource" @change="datasourceChanged" />
      </el-row>
      <el-row class="service-info-row" v-if="service.type === ServiceType.MAP">
        <el-row>
          <span class="divider-header">地图瓦片缓存</span>
          <el-switch
            inline-prompt
            active-text="启用"
            @change="handleValueChanged"
            v-model="cacheLayer.enabled"
          />
        </el-row>
        <div @click="createTiles" class="save">地图切片</div>
      </el-row>
      <el-row v-if="service.type === ServiceType.MAP">
        <el-form class="content-container">
          <el-row :gutter="32">
            <el-col :span="12">
              <el-form-item label="元瓦片大小">
                <el-row>
                  <el-input-number
                    v-model="cacheLayer.metatileWidth"
                    :min="1"
                    :max="20"
                    controls-position="right"
                    @change="handleValueChanged"
                  />
                  <div class="text">×</div>
                  <el-input-number
                    v-model="cacheLayer.metatileHeight"
                    :min="1"
                    :max="20"
                    controls-position="right"
                    @change="handleValueChanged"
                  />
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="装订线大小">
                <el-input-number
                  v-model="cacheLayer.gutterSize"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  @change="handleValueChanged"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="瓦片格式">
            <el-checkbox-group
              class="my-server-item"
              v-model="cacheLayer.formats"
              @change="handleValueChanged"
            >
              <el-checkbox v-for="format in cacheFormats" :key="format" :label="format">{{
                format
              }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </el-row>
      <div v-if="service.type === ServiceType.MAP || service.type === ServiceType.POLYMERIZE">
        <el-row>
          <span class="divider-header">图层管理</span>
        </el-row>
        <LayerList
          v-model="layerList"
          :style-enabled="service.type === ServiceType.MAP"
          @update:model-value="handleLayersChange()"
        />
      </div>
      <el-row>
        <span class="divider-header">角色授权</span>
        <el-switch
          inline-prompt
          active-text="全部授权"
          @change="handleSelectAllRole"
          v-model="roleAll"
        />
      </el-row>
      <div>
        <RoleAuthorization
          ref="childRA"
          v-if="RoleAuthorizationShow"
          :serverName="service.name"
          :roleAll="roleAll"
        ></RoleAuthorization>
      </div>
    </div>
    <MapCacheCreate v-if="dialogVisible" v-model="dialogVisible" :name="service.name" />
  </div>
</template>
<script lang="ts" setup>
import { setScoped } from "@/utils/basic";
import { BaseServiceInfo, PreviewAddress, ServiceType, ServiceUtil } from "geoserver-manager";
import { onMounted, ref, Ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import LayerList from "./serviceInfo/LayerList.vue";
import { ElLoading, ElMessage } from "element-plus";
import RoleAuthorization from "@/components/RoleAuthorization.vue";
import localCache from "@/utils/auth";
import { getCacheFormats } from "@/api/resource";
import { createTemToken } from "@/api/security/token";
import DatasourceInfo from "./serviceInfo/DatasourceInfo.vue";
import { GeocodingService } from "@/interface/business/geocodingService";
import { updateIndex } from "@/api/geocoding";
import { CadService } from "@/interface/business/cadService";
import MapCacheCreate from "./mapcache/MapCacheCreate.vue";
const RoleAuthorizationShow = ref(false);
const route = useRoute();
const router = useRouter();
const service: Ref<BaseServiceInfo> = ref({} as any);
const serviceList: Ref<string[]> = ref([]);
const serviceType = route.params.serviceType as any;
const serviceName = route.params.serviceName as string;
const layerList: Ref<{ name: string; style: string }[]> = ref([]);
const previewUrl: Ref<PreviewAddress[]> = ref([]);
const titleEditable = ref(false);
const descriptionEditable = ref(false);
const roleAll = ref(false);
const handleSelectAllRole = () => {};
const childRA: any = ref(null);
const cacheLayer: Ref<any> = ref({});
const loadServiceInfo = async () => {
  if (serviceType === "geocoding") {
    const geocodingService = new GeocodingService();
    await geocodingService.load(serviceName);
    service.value = geocodingService;
  } else if (serviceType === "cad") {
    const cadService = new CadService();
    await cadService.load(serviceName);
    service.value = cadService;
  } else {
    service.value = await ServiceUtil.getServiceInfo(serviceName, serviceType);
  }

  serviceList.value = service.value.enabledServices.map((service) => service);
  cacheLayer.value = service.value.cacheLayer ?? {};
  cacheEnabled.value = service.value.cacheLayer?.enabled;
  selectFormats.value = service.value.cacheLayer?.formats;
  previewUrl.value = await service.value.getPreviewUrl();
  (service.value.type === ServiceType.MAP || service.value.type === ServiceType.POLYMERIZE) &&
    (layerList.value = service.value.layers);
};
const cacheEnabled: Ref<boolean> = ref(false);
const selectFormats: Ref<string[]> = ref([]);
const cacheFormats = ref([
  "application/json;type=utfgrid",
  "image/gif",
  "image/jpeg",
  "image/png",
  "image/png8",
  "image/vnd.jpeg-png",
  "image/vnd.jpeg-png8"
]);
const handleValueChanged = () => {
  service.value.changed = true;
};
// const handleCheckedFormatChange = (value: string[]) => {
//   service.value.cacheLayer = cacheLayer.value;
// };
// const handleCheckedEnabledChange = (value: boolean) => {
//   service.value.cacheLayer = cacheLayer.value;
// };
// const handleCheckedServiceChange = (value: string[]) => {
//   service.value.enabledServices = value;
// };
const handleOverview = async (previewUrl: string) => {
  const userName = localCache.getCache("realName");
  const token = await createTemToken(userName, "");
  if (previewUrl.includes("?")) {
    window.open(previewUrl + `&token=${token}`, "_blank");
  } else {
    window.open(previewUrl + `?token=${token}`, "_blank");
  }
};
let layersChanged = false;
const handleLayersChange = () => {
  layersChanged = true;
};
const datasourceChanged = (value: any) => {
  service.value.datasource = value;
};
// const handleOpenlayersPreview = () => {
// window.open(previewUrl.value.url, "_blank");
// const type = serviceType === ServiceType.POLYMERIZE ? "group" : "map";
// const routeUrl = router.resolve({
//   name: "wmsPreview",
//   query: { serviceName, serviceType: type }
// });
// };
const handlePreview = (preview: PreviewAddress) => {
  const routeUrl = router.resolve({
    name: preview.url,
    query: preview.params
  });
  window.open(routeUrl.href, "_blank");
};
const saveService = async () => {
  if (layersChanged) {
    service.value.layers = layerList.value;
  }
  await childRA.value.submitForm();
  await service.value.save();
  setScoped(`服务修改-${service.value.name}`);
  ElMessage({
    message: "修改成功",
    type: "success"
  });
  /*   router.push({
    name: "serviceManage"
  }); */
};
const cancel = () => {
  if (serviceType === "geocoding") {
    router.push({
      name: "GeoCoding"
    });
  } else {
    router.push({
      name: "serviceManage"
    });
  }
};
const token = ref("");
const getFormats = async () => {
  cacheFormats.value = await getCacheFormats();
};
const updateGeocodingIndex = async () => {
  const loading = ElLoading.service({
    lock: true,
    text: "索引更新中...",
    background: "rgba(0, 0, 0, 0.7)"
  });
  try {
    await updateIndex(serviceName);
    ElMessage({
      message: "更新成功",
      type: "success"
    });
  } catch {
  } finally {
    loading.close();
  }
};
const dialogVisible = ref(false);
const createTiles = async () => {
  dialogVisible.value = true;
};
onMounted(async () => {
  await getFormats();
  // 获取图层组
  await loadServiceInfo();
  RoleAuthorizationShow.value = true;
  const tokenCache: string = localCache.getCache("Latias");
  tokenCache.replace("Bearer ", "");
  token.value = `Bearer=${tokenCache}`;
});
</script>
<style lang="scss" scoped>
.service-info {
  width: 70%;
  margin: 25px auto;
}
span {
  font-size: 18px;
}
.info-bg {
  width: 100%;
  padding-bottom: 100px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
}
.card-header {
  height: 65px;
  line-height: 65px;
  border-bottom: 1px solid #ebebeb;
  margin-bottom: 18px;
  .title-left {
    display: flex;
    img {
      cursor: pointer;
      width: 16px;
      height: 16px;
      margin: 22px 10px 0 20px;
    }
    span {
      cursor: pointer;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #7d8da1;
      b {
        margin-left: 5px;
        font-weight: 100;
        color: rgba($color: #7d8da1, $alpha: 0.4);
      }
    }
    .text {
      margin: 0 10px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #232a3a;
    }
  }
  .server-title-right {
    display: flex;
    height: 100%;
    .cancle {
      cursor: pointer;
      margin: 12px;
      width: 96px;
      height: 34px;
      background: #ffffff;
      border: 1px solid #cdd3dc;
      border-radius: 2px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #4b5970;
      line-height: 34px;
    }
    .save {
      cursor: pointer;
      margin: 12px 0;
      width: 96px;
      height: 34px;
      background: #4076f3;
      border-radius: 2px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      line-height: 34px;
    }
  }
}
.serve-detial {
  width: 100%;
  height: 180px;
  .serve-detial-left {
    img {
      width: 300px;
      height: 176px;
      border-radius: 4px;
      margin: 0 20px;
    }
  }
  .serve-detial-right {
    .right-title {
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #232a3a;
    }
    .status-normal,
    .status-error {
      margin-left: 16px;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 500;
      height: 18px;
      align-self: center;
    }
    .status-normal {
      border: 1px solid #07da65;
      color: #07da65;
    }
    .status-error {
      border: 1px solid #ff5353;
      color: #ff5353;
      cursor: pointer;
    }
  }
}
table {
  width: 100%;
  margin: 5px 0px;
  border-collapse: collapse;
  border: 1px solid #ccc;
  tr {
    .title {
      width: 10%;
      background: #fafafa;
      border: 1px solid #e6eaef;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: center;
      color: #7d8da1;
    }
    .content {
      width: 40%;
      background: #ffffff;
      border: 1px solid #e6eaef;
      .server-edit {
        display: flex;
        align-items: center;
        span {
          margin-left: 8px;
          font-size: 14px;
          font-family: Source Han Sans CN, Source Han Sans CN-Regular;
          font-weight: 400;
          color: #7d8da1;
        }
        .edit-icon {
          cursor: pointer;
          img {
            margin: 5px 0 0 8px;
          }
        }
      }
      .server-url {
        display: flex;
        width: 100%;
        flex-wrap: wrap;
        .server-url-item {
          margin: 0 10px;
        }
      }
      span {
        margin-left: 8px;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        color: #7d8da1;
      }
    }
    td {
      height: 36px;
    }
  }
}
.divider-header {
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Bold;
  font-weight: 700;
  color: #232a3a;
  margin: 10px 0 10px 20px;
}
.my-server {
  width: 97%;
  height: 56px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-left: 20px;
  display: flex;
  align-items: center;
  .my-server-item {
    margin: 0 10px;
  }
}
.content-container {
  width: 100%;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin: 0 20px;
  padding: 10px;
  .my-server-item {
    margin: 0 10px;
  }
}
.el-switch {
  margin: 6px 5px;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
.defaultImg {
  width: 300px;
  height: 176px;
  border-radius: 4px;
  margin: 0 20px;
}
.button-primary {
  cursor: pointer;
  margin: 6px 10px;
  width: 80px;
  height: 30px;
  background: #4076f3;
  border-radius: 2px;
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  line-height: 30px;
}
.service-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .save {
    cursor: pointer;
    margin: 12px 20px;
    width: 96px;
    height: 34px;
    background: #4076f3;
    border-radius: 2px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    line-height: 34px;
  }
}
</style>
