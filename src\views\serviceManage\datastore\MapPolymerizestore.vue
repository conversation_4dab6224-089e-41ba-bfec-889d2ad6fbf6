<!--
 * @Description:
 * @Autor: silei
 * @Date: 2023-02-14 11:00:11
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-19 14:55:41
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-select class="select-search" v-model="selectValue" filterable placeholder="请选择服务">
        <el-option v-for="item in services" :key="item" :label="item" :value="item" />
      </el-select>
      <div @click="handleAdd(selectValue)" class="add-bottom">添加</div>
    </el-row>
    <el-form-item prop="services">
      <el-table
        :data="value.services"
        class="custom-table-server lyaer-table border"
        header-row-class-name="custom-header-row"
        header-cell-class-name="custom-header-cell"
        style="width: 97%"
      >
        <el-table-column label="序号" width="80">
          <template #default="scope">
            <div style="display: flex; align-items: center">
              <span>{{ scope.$index + 1 }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="服务" width="250">
          <template #default="scope">
            <span>{{ scope.row }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <div class="positonBox">
              <div
                v-if="scope.$index > 0"
                @click="moveUp(scope.$index, scope.row)"
                class="fontstyle item"
              >
                上移
              </div>
              <div
                v-if="scope.$index < value.services.length - 1"
                @click="moveDown(scope.$index, scope.row)"
                class="fontstyle item"
              >
                下移
              </div>
              <div @click="remove(scope.$index)" class="fontstyle item">移除</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </el-form>
</template>
<script lang="ts" setup>
import { computed, onMounted, PropType, reactive, ref, Ref } from "vue";
import { FormRules, ElMessage } from "element-plus";
import { getMapServiceNames } from "@/api/resource";
const validateServices = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    callback(new Error("请至少选择一个服务"));
  } else {
    callback();
  }
};
const rules = reactive<FormRules>({
  services: [{ validator: validateServices, trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        services: []
      };
    }
  }
});
const services: Ref<string[]> = ref([]);
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const selectValue: Ref<string> = ref(null!);
onMounted(async () => {
  // 获取服务列表
  services.value = await getMapServiceNames();
  value.value.services = [];
});
const handleAdd = (service: string) => {
  if (!selectValue.value) return;
  if (!value.value.services.includes(service)) {
    value.value.services.push(service);
  } else {
    ElMessage({
      message: "已选择该服务",
      type: "warning"
    });
  }
};
const moveUp = (index: number, data: string) => {
  if (index > 0) {
    const preLayer = value.value.services[index - 1];
    value.value.services.splice(index - 1, 2, ...[data, preLayer]);
  }
};
const moveDown = (index: number, data: string) => {
  if (index < value.value.services.length - 1) {
    const next = value.value.services[index + 1];
    value.value.services.splice(index, 2, ...[next, data]);
  }
};
const remove = (index: number) => {
  value.value.services.splice(index, 1);
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      // if (vaild) {
      //   value.value.services = selectService.map((s) => {
      //     return s;
      //   });
      // }
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
<style lang="scss" scoped>
.positonBox {
  display: flex;
  align-items: center;
  .item {
    margin-right: 20px;
    cursor: pointer;
  }
}
.custom-table-server {
  --el-table-border-color: #ebebeb;
  --el-table-row-hover-bg-color: #f6fbff;
  --el-table-header-text-color: #000000;
  margin: 10px 2% 0 2%;
  height: 439px;
  .custom-header-row {
    height: 30px;
    --el-table-header-bg-color: #fafafa;

    // --el-table-border-color: #fafafa;
    .is-leaf.custom-header-cell {
      border-bottom-color: #fafafa;
    }

    .cell {
      font-size: 14px;
      font-weight: 700;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
    }
  }

  .el-table__row {
    .cell {
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: rgba(0, 0, 0, 0.75);
    }
  }
}
.select-search {
  width: 400px;
  margin-left: 15px;
}
.add-bottom {
  width: 70px;
  height: 30px;
  color: #ffffff;
  background-color: #4076f3;
  font-size: 16px;
  text-align: center;
  margin-left: 20px;
  line-height: 30px;
  cursor: pointer;
}
</style>
