/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-06-19 17:53:17
 * @LastEditors: silei
 * @LastEditTime: 2023-07-12 10:00:44
 */

import {
  BoundingSphere,
  Cartesian3,
  Color,
  ComponentDatatype,
  DistanceDisplayCondition,
  DistanceDisplayConditionGeometryInstanceAttribute,
  GeometryInstance,
  GeometryInstanceAttribute,
  Material,
  MaterialAppearance,
  PolylineGeometry,
  PolylineMaterialAppearance,
  PolylineVolumeGeometry,
  Primitive,
  PrimitiveCollection,
  ShowGeometryInstanceAttribute,
  Viewer,
  Math as CesiumMath,
  Cartesian2
} from "cesium";
import { Coord } from "./coord";
import PolylineCommon from "./shader/PolylineCommon";
import PolylineMaterialAppearanceVS from "./shader/PolylineMaterialAppearanceVS";
import PolylineFS from "./shader/PolylineFS";
export class PipeLayer {
  private _pipePrimitive3D: Primitive = null!;
  private _pipePrimitive2D: Primitive = null!;
  private _pipeInfo: any[] = [];
  constructor(private readonly _viewer: Viewer) {}

  load(pipeInfo: any[]) {
    this.loadPipePrimitive(pipeInfo, this._viewer);
  }

  private getPipePrimitive(geometryInstances: any) {
    const material = new Material({
      fabric: {
        // type: Cesium.Material.PolylineImageTrailType,
        uniforms: {
          speed: 20,
          image: "/data/spriteline2.png"
        },
        source: `uniform sampler2D image;
          uniform float speed;
          varying vec4 v_color;
          czm_material czm_getMaterial(czm_materialInput materialInput){
            czm_material material = czm_getDefaultMaterial(materialInput);
            float count = abs(v_color.a);
            float highlight = 0.5;
            float direction = sign(v_color.a);
            if(v_color.a >= 10000.0){
              count -= 10000.0;
              highlight = 2.0;
            }
            vec2 st = vec2(count, 1.0) * materialInput.st;
            float time = fract(czm_frameNumber * speed / 1000.0);
            vec4 colorImage = texture2D(image, vec2(fract(direction * st.s - time), st.t));
            material.alpha = colorImage.a;
            material.diffuse = v_color.rgb * highlight;
            return material;
         }`
      },
      translucent: function (material) {
        return true;
      }
    });
    // 加载三维管线
    const polylineVolume = new Primitive({
      geometryInstances,
      appearance: new MaterialAppearance({
        material,
        renderState: {
          depthTest: {
            enabled: false
          }
        },
        vertexShaderSource: `attribute vec3 position3DHigh;
          attribute vec3 position3DLow;
          attribute vec3 normal;
          attribute vec2 st;
          attribute float batchId;
          attribute vec4 color;
          attribute vec3 bitangent;
          varying vec3 v_positionEC;
          varying vec3 v_normalEC;
          varying vec2 v_st;
          varying vec4 v_color;
          varying vec3 v_bitangent;
          void main()
          {
              vec4 p = czm_computePosition();
  
              v_positionEC = (czm_modelViewRelativeToEye * p).xyz;      // position in eye coordinates
              v_normalEC = czm_normal * normal;                         // normal in eye coordinates
              v_color = color;
              v_st = st;
              v_bitangent = bitangent;
              gl_Position = czm_modelViewProjectionRelativeToEye * p;
          }
          `,
        fragmentShaderSource: `varying vec3 v_positionEC;
          varying vec3 v_normalEC;
          varying vec2 v_st;
  
          void main()
          {
              vec3 positionToEyeEC = -v_positionEC;
  
              vec3 normalEC = normalize(v_normalEC);
          #ifdef FACE_FORWARD
              normalEC = faceforward(normalEC, vec3(0.0, 0.0, 1.0), -normalEC);
          #endif
  
              czm_materialInput materialInput;
              materialInput.normalEC = normalEC;
              materialInput.positionToEyeEC = positionToEyeEC;
              materialInput.st = v_st;
              czm_material material = czm_getMaterial(materialInput);
  
          #ifdef FLAT
              gl_FragColor = vec4(material.diffuse + material.emission, material.alpha);
          #else
              gl_FragColor = czm_phong(normalize(positionToEyeEC), material, czm_lightDirectionEC);
          #endif
          }
          `
      })
    });
    return polylineVolume;
  }

  private getPipe2DPrimitive(geometryInstances: any) {
    const material = new Material({
      fabric: {
        // type: Cesium.Material.PolylineImageTrailType,
        uniforms: {
          speed: 20,
          image: "/data/spriteline2.png"
        },
        source: `uniform sampler2D image;
        uniform float speed;
        uniform vec2 repeat;
        varying vec4 v_color;
        czm_material czm_getMaterial(czm_materialInput materialInput){
          czm_material material = czm_getDefaultMaterial(materialInput);
          float count = abs(v_color.a);
          float highlight = 0.5;
          float direction = sign(v_color.a);
          if(v_color.a >= 10000.0){
            count -= 10000.0;
            highlight = 2.0;
          }
          vec2 st = vec2(count, 1.0) * materialInput.st;
          float time = fract(czm_frameNumber * speed / 1000.0);
          vec4 colorImage = texture2D(image, vec2(fract(direction * st.s - time), st.t));
          material.alpha = colorImage.a;
          material.diffuse = v_color.rgb * highlight;
          return material;
       }`
      },
      translucent: function (material) {
        return true;
      }
    });
    const defaultVertexShaderSource = `${PolylineCommon}\n${PolylineMaterialAppearanceVS}`;
    const defaultFragmentShaderSource = PolylineFS;

    // 加载三维管线
    const polylineVolume = new Primitive({
      geometryInstances,
      appearance: new PolylineMaterialAppearance({
        material,
        renderState: {
          depthTest: {
            enabled: false
          }
        },
        vertexShaderSource: defaultVertexShaderSource,
        fragmentShaderSource: defaultFragmentShaderSource
      })
    });
    return polylineVolume;
  }

  /**
   * 加载管网数据
   * @param viewer
   */
  private loadPipePrimitive(pipeInfo: any[], viewer: Viewer) {
    this._pipeInfo = pipeInfo;
    const pipeLayer = new PrimitiveCollection();
    viewer.scene.primitives.add(pipeLayer);
    // const layer = new BC.GeoJsonLayer("layer", "data/pipe.json");
    // const datasource = await layer.delegate;
    const circlePosition = this.computeCircle(1);
    const length = 50;
    const geometryInstances3D: any[] = [];
    const geometryInstances2D: any[] = [];
    const pushGeometry = (pipe: any) => {
      const positions = [
        Coord.getCartesian({ x: pipe.xstart, y: pipe.ystart, z: pipe.elevationStart }),
        Coord.getCartesian({ x: pipe.xend, y: pipe.yend, z: pipe.elevationEnd })
      ];
      const pipeCode = pipe.pipeCode;
      const distance = Cartesian3.distance(positions[0], positions[1]);
      const count = distance / length + 10000;
      const colorData = "#00ff00";
      const color = Color.fromCssColorString(colorData);
      geometryInstances3D.push(
        new GeometryInstance({
          id: pipeCode,
          geometry: new PolylineVolumeGeometry({
            vertexFormat: MaterialAppearance.MaterialSupport.TEXTURED.vertexFormat,
            polylinePositions: positions,
            shapePositions: circlePosition
          }),
          attributes: {
            show: new ShowGeometryInstanceAttribute(true),
            distanceDisplayCondition:
              DistanceDisplayConditionGeometryInstanceAttribute.fromDistanceDisplayCondition(
                new DistanceDisplayCondition(0.0, 600.0)
              ),
            // color: ColorGeometryInstanceAttribute.fromColor(color)
            color: new GeometryInstanceAttribute({
              componentDatatype: ComponentDatatype.FLOAT,
              componentsPerAttribute: 4,
              normalize: false,
              value: [color.red, color.green, color.blue, count] as any
            })
          }
        })
      );
      geometryInstances2D.push(
        new GeometryInstance({
          id: pipeCode,
          geometry: new PolylineGeometry({
            positions,
            width: 5
          }),
          attributes: {
            show: new ShowGeometryInstanceAttribute(true),
            distanceDisplayCondition:
              DistanceDisplayConditionGeometryInstanceAttribute.fromDistanceDisplayCondition(
                new DistanceDisplayCondition(600.0, 100000.0)
              ),
            color: new GeometryInstanceAttribute({
              componentDatatype: ComponentDatatype.FLOAT,
              componentsPerAttribute: 4,
              normalize: false,
              value: new Float32Array([color.red, color.green, color.blue, count]) as any
            })
          }
        })
      );
    };
    pipeInfo.map((pipe) => pushGeometry(pipe));
    const polylineVolume = this.getPipePrimitive(geometryInstances3D);
    const polyline2D = this.getPipe2DPrimitive(geometryInstances2D);
    this._pipePrimitive3D = polylineVolume;
    this._pipePrimitive2D = polyline2D;
    pipeLayer.add(polylineVolume);
    pipeLayer.add(polyline2D);

    // 定时更新数据
    setInterval(() => {
      this.updateRender();
    }, 10000);
  }

  /**
   * 根据半径生成圆
   * @param radius 半径
   * @returns 圆坐标集合
   */
  private computeCircle(radius: number) {
    const positions = [];
    for (let i = 0; i < 360; i++) {
      const radians = CesiumMath.toRadians(i);
      positions.push(new Cartesian2(radius * Math.cos(radians), radius * Math.sin(radians)));
    }
    return positions;
  }

  updateRender() {
    this._pipeInfo.forEach((pipe) => {
      const changeColor = (attributes: any) => {
        if (attributes) {
          const oldColor = attributes.color as Float32Array;
          const colorString = this.getRenderColor();
          const color = Color.fromCssColorString(colorString);
          const alpha = Math.abs(oldColor[3]);
          attributes.color = new Float32Array([color.red, color.green, color.blue, alpha]);
        }
      };
      const attributes3d = this._pipePrimitive3D.getGeometryInstanceAttributes(pipe.pipeCode);
      changeColor(attributes3d);
      const attributes2d = this._pipePrimitive2D.getGeometryInstanceAttributes(pipe.pipeCode);
      changeColor(attributes2d);
    });
  }

  /**
   * 获取渲染颜色
   * @param value 管道值
   * @returns
   */
  getRenderColor(): string {
    const renderColors = [
      {
        max: 0.1,
        color: "#00A6A6"
      },
      {
        min: 0.1,
        max: 0.15,
        color: "#5ACC85"
      },
      {
        min: 0.15,
        max: 0.2,
        color: "#687DFB"
      },
      {
        min: 0.2,
        max: 0.25,
        color: "#99CDFD"
      },
      {
        min: 0.25,
        max: 0.3,
        color: "#FFCB43"
      },
      {
        min: 0.3,
        max: 0.35,
        color: "#F79043"
      },
      {
        min: 0.35,
        max: 0.4,
        color: "#F75A43"
      },
      {
        min: 0.4,
        color: "#FF0000"
      }
    ];
    const colors = [
      {
        min: 0,
        max: 1,
        color: "#1949fa"
      },
      {
        min: 1,
        max: 2,
        color: "#5b19fa"
      },
      {
        min: 2,
        max: 3,
        color: "#fa19f3"
      },
      {
        min: 3,
        max: 4,
        color: "#fa1962"
      },
      {
        min: 4,
        max: 5,
        color: "#fa1919"
      }
    ];
    const data = Math.random() * 0.5;
    const color = renderColors.find((c: any) => {
      let result = true;
      if (c.min !== undefined) {
        result = data >= c.min;
      }
      if (c.max !== undefined) {
        result = result && data < c.max;
      }
      return result;
    });
    return color?.color ?? "#00ff00";
  }
}
