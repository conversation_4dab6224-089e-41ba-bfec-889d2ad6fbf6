<!--
 * @Description: 服务预览
 * @Autor: silei
 * @Date: 2023-02-09 15:29:59
 * @LastEditors: silei
 * @LastEditTime: 2023-02-09 15:53:23
-->
<template>
  <div>
    <div id="map" style="width: 100%; height: 920px"></div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted } from "vue";
import "ol/ol.css";
import { Map, View } from "ol";
import { ImageWMS } from "ol/source";
import { Image as ImageLayer } from "ol/layer";
onMounted(() => {
  // eslint-disable-next-line no-unused-vars
  const map = new Map({
    target: "map-container",
    layers: [
      new ImageLayer({
        source: new ImageWMS({
          url: "http://localhost:8080/geoserver/wms",
          params: {
            layers: "tiger-ny"
          }
        })
      })
    ],
    view: new View({
      center: [-73.97764, 40.77507],
      zoom: 3,
      projection: "EPSG:4326"
    })
  });
});
</script>
