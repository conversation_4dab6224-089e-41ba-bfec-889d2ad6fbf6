<!--
 * @Description: 
 * @Date: 2023-02-06 11:42:26
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-02-16 09:45:45
-->
<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="用户" name="first">
      <User v-if="activeName === 'first'"></User>
    </el-tab-pane>
    <el-tab-pane label="用户组" name="second">
      <UserGroup v-if="activeName === 'second'"></UserGroup>
    </el-tab-pane>
    <el-tab-pane label="角色" name="third">
      <Role v-if="activeName === 'third'"></Role>
    </el-tab-pane>
  </el-tabs>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import type { TabsPaneContext } from "element-plus";
import Role from "./role/index.vue";
import User from "./user/index.vue";
import UserGroup from "./userGroup/index.vue";
const activeName: any = ref("first");
const handleClick = (tab: TabsPaneContext, event: Event) => {
  const sessionData: any = tab.props.name;
  sessionStorage.setItem("elTab", sessionData);
};
onMounted(async () => {
  activeName.value = sessionStorage.getItem("elTab");
});
</script>
<style lang="scss" scoped>
.demo-tabs {
  width: 100%;
  height: 100%;
}
::v-deep(.el-tabs__content) {
  width: 100%;
  height: 90%;
  padding: 0;
  display: flex;
  justify-content: center;
}
::v-deep(.el-tabs__nav) {
  margin-left: 100px;
}
::v-deep(.el-tabs__item) {
  margin: 0 30px;
  font-size: 14px;
}
</style>
