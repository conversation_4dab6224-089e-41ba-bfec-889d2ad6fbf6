<!--
 * @Description: 服务接入预览
 * @Author: AI Assistant
 * @Date: 2025-01-11 12:00:00
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025-01-11 12:00:00
-->
<template>
  <div class="service-access-preview">
    <div class="preview-header">
      <h2>{{ serviceInfo?.name || "服务接入预览" }}</h2>
      <div class="service-info">
        <span class="service-type">{{
          getServiceTypeLabel(serviceInfo?.config?.serviceType)
        }}</span>
        <span class="city-info"
          >{{ serviceInfo?.config?.cityName }} - {{ serviceInfo?.config?.adminArea }}</span
        >
      </div>
    </div>
    <div
      id="map-container"
      class="map-container"
      v-loading="loading"
      element-loading-text="正在加载地图..."
    ></div>
    <div class="preview-controls">
      <el-button @click="goBack">返回</el-button>
      <el-button type="primary" @click="refreshMap" :loading="loading">刷新地图</el-button>
      <el-button @click="testDifferentServices">测试其他服务</el-button>
      <el-button @click="showServiceInfo" type="info">服务信息</el-button>
    </div>

    <!-- 服务信息弹窗 -->
    <el-dialog
      v-model="showInfoDialog"
      title="服务详细信息"
      width="600px"
      :before-close="handleCloseDialog"
    >
      <div class="service-details" v-if="serviceInfo">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="服务名称">{{ serviceInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="服务类型">{{
            getServiceTypeLabel(serviceInfo.config?.serviceType)
          }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{
            serviceInfo.config?.cityName
          }}</el-descriptions-item>
          <el-descriptions-item label="行政区域">{{
            serviceInfo.config?.adminArea
          }}</el-descriptions-item>
          <el-descriptions-item label="瓦片地址" span="2">
            <el-input v-model="serviceInfo.config.tileUrl" readonly type="textarea" :rows="2" />
          </el-descriptions-item>
          <el-descriptions-item label="前端密钥">{{
            serviceInfo.config?.frontendKey
          }}</el-descriptions-item>
          <el-descriptions-item label="服务密钥">{{
            serviceInfo.config?.serviceKey || "未配置"
          }}</el-descriptions-item>
          <el-descriptions-item label="中心坐标">
            {{ serviceInfo.config?.centerLng }}, {{ serviceInfo.config?.centerLat }}
          </el-descriptions-item>
          <el-descriptions-item label="缩放级别">
            {{ serviceInfo.config?.minZoom }} - {{ serviceInfo.config?.maxZoom }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="showInfoDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 错误提示 -->
    <div v-if="errorMessage" class="error-overlay">
      <el-alert :title="errorMessage" type="error" :closable="false" show-icon>
        <template #default>
          <p>{{ errorDetails }}</p>
          <el-button type="primary" size="small" @click="retryLoad">重试</el-button>
          <el-button size="small" @click="useTestData">使用测试数据</el-button>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Map, View } from "ol";
import { Tile as TileLayer } from "ol/layer";
import { XYZ } from "ol/source";
import { fromLonLat, transformExtent } from "ol/proj";
import { defaults as defaultControls, ScaleLine, MousePosition } from "ol/control";
import { createStringXY } from "ol/coordinate";
import { ServiceType, type ServiceAccessVo } from "@/interface/business/serviceAccess";
import { detailAccess } from "@/api/accessService";
import "ol/ol.css";

/**
 * @description 路由和响应式数据
 */
const route = useRoute();
const router = useRouter();
const loading = ref(true);
const map = ref<Map | null>(null);
const serviceInfo = ref<ServiceAccessVo | null>(null);
const showInfoDialog = ref(false);
const errorMessage = ref("");
const errorDetails = ref("");

/**
 * @description 获取服务类型标签
 */
const getServiceTypeLabel = (serviceType?: ServiceType): string => {
  const typeMap = {
    [ServiceType.TIANDITU]: "天地图",
    [ServiceType.BAIDU]: "百度地图",
    [ServiceType.AMAP]: "高德地图",
    [ServiceType.CUSTOM]: "第三方地图"
  };
  return serviceType ? typeMap[serviceType] || "未知类型" : "";
};

/**
 * @description 解析瓦片URL模板
 */
const parseUrlTemplate = (template: string, serviceKey: string): string => {
  if (!template) {
    throw new Error("瓦片URL模板不能为空");
  }

  let parsedUrl = template;

  // 替换token相关占位符
  if (serviceKey) {
    parsedUrl = parsedUrl
      .replace(/\{token\}/g, serviceKey)
      .replace(/\{key\}/g, serviceKey)
      .replace(/\{ak\}/g, serviceKey);
  }

  // 替换坐标占位符（保持OpenLayers格式）
  parsedUrl = parsedUrl.replace(/\{z\}/g, "{z}").replace(/\{x\}/g, "{x}").replace(/\{y\}/g, "{y}");

  // 替换服务器编号占位符
  parsedUrl = parsedUrl
    .replace(/\{0-7\}/g, () => Math.floor(Math.random() * 8).toString())
    .replace(/\{1-4\}/g, () => Math.floor(Math.random() * 4 + 1).toString())
    .replace(/\{0-3\}/g, () => Math.floor(Math.random() * 4).toString());

  return parsedUrl;
};

/**
 * @description 获取坐标系配置
 */
const getProjectionConfig = (serviceType: ServiceType) => {
  switch (serviceType) {
    case ServiceType.TIANDITU:
      return {
        projection: "EPSG:4326",
        extent: [-180, -90, 180, 90]
      };
    case ServiceType.BAIDU:
      return {
        projection: "EPSG:3857", // 百度地图使用Web墨卡托投影
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
    case ServiceType.AMAP:
      return {
        projection: "EPSG:3857", // 高德地图使用Web墨卡托投影
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
    case ServiceType.CUSTOM:
    default:
      return {
        projection: "EPSG:3857",
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
  }
};

/**
 * @description 创建瓦片图层
 */
const createTileLayer = (config: any): TileLayer<XYZ> => {
  const { serviceType, tileUrl, serviceKey, maxZoom = 18, minZoom = 1 } = config;
  const projectionConfig = getProjectionConfig(serviceType);
  const parsedUrl = parseUrlTemplate(tileUrl, serviceKey);

  return new TileLayer({
    source: new XYZ({
      url: parsedUrl,
      maxZoom: maxZoom,
      minZoom: minZoom,
      crossOrigin: "anonymous"
    })
  });
};

/**
 * @description 初始化地图
 */
const initMap = async () => {
  try {
    // 清除之前的错误信息
    errorMessage.value = "";
    errorDetails.value = "";

    if (!serviceInfo.value?.config) {
      throw new Error("服务配置信息不完整");
    }

    const config = serviceInfo.value.config;

    // 验证必要的配置项
    if (!config.tileUrl) {
      throw new Error("瓦片地址未配置");
    }

    if (!config.serviceType) {
      throw new Error("服务类型未配置");
    }

    const projectionConfig = getProjectionConfig(config.serviceType);

    // 创建瓦片图层
    const tileLayer = createTileLayer(config);

    // 计算中心点坐标
    let center = [116.4074, 39.9042]; // 默认北京坐标
    if (config.centerLng && config.centerLat) {
      center = [config.centerLng, config.centerLat];
    }

    // 根据坐标系转换中心点
    const transformedCenter =
      projectionConfig.projection === "EPSG:4326" ? center : fromLonLat(center);

    // 销毁之前的地图实例
    if (map.value) {
      map.value.getTargetElement()?.remove();
      map.value = null;
    }

    // 创建地图
    map.value = new Map({
      target: "map-container",
      layers: [tileLayer],
      view: new View({
        center: transformedCenter,
        zoom: 10,
        projection: projectionConfig.projection,
        extent: projectionConfig.extent
      }),
      controls: defaultControls().extend([
        new ScaleLine({ units: "metric" }),
        new MousePosition({
          coordinateFormat: createStringXY(6),
          projection: "EPSG:4326"
        })
      ])
    });

    // 监听瓦片加载错误
    tileLayer.getSource()?.on("tileloaderror", (event) => {
      console.warn("瓦片加载失败:", event);
      if (!errorMessage.value) {
        errorMessage.value = "部分瓦片加载失败";
        errorDetails.value = "可能是网络问题或服务密钥配置错误，请检查服务配置";
      }
    });

    loading.value = false;
    ElMessage.success("地图加载成功");
  } catch (error: any) {
    console.error("地图初始化失败:", error);
    loading.value = false;
    errorMessage.value = "地图初始化失败";
    errorDetails.value = error.message || "未知错误，请检查服务配置";
    ElMessage.error(`地图加载失败: ${error.message}`);
  }
};

/**
 * @description 刷新地图
 */
const refreshMap = () => {
  if (map.value) {
    map.value.getTargetElement()?.remove();
    map.value = null;
  }
  loading.value = true;
  setTimeout(() => {
    initMap();
  }, 100);
};

/**
 * @description 返回上一页
 */
const goBack = () => {
  router.back();
};

/**
 * @description 显示服务信息
 */
const showServiceInfo = () => {
  showInfoDialog.value = true;
};

/**
 * @description 关闭信息弹窗
 */
const handleCloseDialog = () => {
  showInfoDialog.value = false;
};

/**
 * @description 测试不同服务类型
 */
const testDifferentServices = () => {
  const services = [
    {
      name: "天地图矢量地图",
      type: ServiceType.TIANDITU,
      url: "https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}",
      city: "北京市"
    },
    {
      name: "高德地图",
      type: ServiceType.AMAP,
      url: "https://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}&key={token}",
      city: "上海市"
    },
    {
      name: "OpenStreetMap",
      type: ServiceType.CUSTOM,
      url: "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
      city: "广州市"
    }
  ];

  const randomService = services[Math.floor(Math.random() * services.length)];

  serviceInfo.value = {
    id: Date.now(),
    name: randomService.name,
    config: {
      serviceType: randomService.type,
      cityName: randomService.city,
      adminArea: "测试区域",
      tileUrl: randomService.url,
      frontendKey: "FIXED_FRONTEND_KEY_2025",
      serviceKey: "test_token_123",
      centerLng: 116.4074,
      centerLat: 39.9042,
      maxZoom: 18,
      minZoom: 1
    },
    configValue: "",
    remark: "测试服务"
  };

  refreshMap();
  ElMessage.success(`已切换到 ${randomService.name}`);
};

/**
 * @description 重试加载
 */
const retryLoad = () => {
  errorMessage.value = "";
  errorDetails.value = "";
  loading.value = true;
  loadServiceInfo();
};

/**
 * @description 使用测试数据
 */
const useTestData = () => {
  errorMessage.value = "";
  errorDetails.value = "";
  serviceInfo.value = createTestData();
  refreshMap();
};

/**
 * @description 创建测试数据
 */
const createTestData = () => {
  return {
    id: 1,
    name: "天地图测试服务",
    config: {
      serviceType: ServiceType.TIANDITU,
      cityName: "北京市",
      adminArea: "朝阳区",
      tileUrl:
        "https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}",
      frontendKey: "FIXED_FRONTEND_KEY_2025",
      serviceKey: "your_tianditu_token_here",
      centerLng: 116.4074,
      centerLat: 39.9042,
      maxZoom: 18,
      minZoom: 1
    },
    configValue: "",
    remark: "测试用天地图服务"
  };
};

/**
 * @description 加载服务信息
 */
const loadServiceInfo = async () => {
  const serviceId = route.query.id as string;
  const testMode = route.query.test as string;
  const testConfig = route.query.config as string;

  // 如果是测试模式或没有服务ID，使用测试数据
  if (testMode === "true" || !serviceId) {
    if (testConfig) {
      try {
        const config = JSON.parse(testConfig);
        serviceInfo.value = {
          id: Date.now(),
          name: config.name,
          config: {
            serviceType: config.serviceType,
            cityName: config.cityName,
            adminArea: config.adminArea,
            tileUrl: config.tileUrl,
            frontendKey: "FIXED_FRONTEND_KEY_2025",
            serviceKey: config.serviceKey,
            centerLng: 116.4074,
            centerLat: 39.9042,
            maxZoom: 18,
            minZoom: 1
          },
          configValue: "",
          remark: "测试服务"
        };
      } catch (error) {
        console.error("解析测试配置失败:", error);
        serviceInfo.value = createTestData();
      }
    } else {
      serviceInfo.value = createTestData();
    }
    await initMap();
    return;
  }

  try {
    const result = await detailAccess(serviceId);
    if (result.code === 200) {
      serviceInfo.value = result.data;
      if (serviceInfo.value) {
        try {
          serviceInfo.value.config = JSON.parse(result.data.configValue || "{}");
          await initMap();
        } catch (parseError) {
          console.error("配置解析失败:", parseError);
          errorMessage.value = "服务配置解析失败";
          errorDetails.value = "服务配置数据格式错误，请联系管理员检查";
          loading.value = false;
        }
      }
    } else {
      errorMessage.value = "获取服务信息失败";
      errorDetails.value = `服务器返回错误: ${result.msg || "未知错误"}`;
      loading.value = false;
    }
  } catch (error: any) {
    console.error("加载服务信息失败:", error);
    errorMessage.value = "网络请求失败";
    errorDetails.value = error.message || "无法连接到服务器，请检查网络连接";
    loading.value = false;
  }
};

/**
 * @description 组件挂载
 */
onMounted(async () => {
  await loadServiceInfo();
});

/**
 * @description 组件卸载
 */
onUnmounted(() => {
  if (map.value) {
    map.value.getTargetElement()?.remove();
    map.value = null;
  }
});
</script>

<style lang="scss" scoped>
.service-access-preview {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .preview-header {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .service-info {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #666;

      .service-type {
        background: #e6f7ff;
        color: #1890ff;
        padding: 2px 8px;
        border-radius: 4px;
        font-weight: 500;
      }

      .city-info {
        color: #999;
      }
    }
  }

  .map-container {
    flex: 1;
    position: relative;
    background: #f0f0f0;
  }

  .preview-controls {
    background: white;
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
  }

  .error-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    max-width: 500px;
    width: 90%;
  }

  .service-details {
    .el-descriptions {
      margin-top: 16px;
    }

    .el-input {
      font-size: 12px;
    }
  }
}

:deep(.ol-control) {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

:deep(.ol-scale-line) {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 4px;
  padding: 2px 8px;
}

:deep(.ol-mouse-position) {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .service-access-preview {
    .preview-header {
      padding: 12px 16px;

      h2 {
        font-size: 18px;
        margin-bottom: 6px;
      }

      .service-info {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
      }
    }

    .preview-controls {
      padding: 12px 16px;
      justify-content: center;

      .el-button {
        font-size: 12px;
        padding: 6px 12px;
      }
    }

    .error-overlay {
      width: 95%;
      max-width: none;
    }
  }

  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .service-access-preview {
    .preview-controls {
      flex-direction: column;
      gap: 8px;

      .el-button {
        width: 100%;
      }
    }
  }
}
</style>
