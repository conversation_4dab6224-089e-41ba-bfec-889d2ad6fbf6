<!--
 * @Description: 服务接入预览
 * @Author: AI Assistant
 * @Date: 2025-01-11 12:00:00
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025-01-11 12:00:00
-->
<template>
  <div class="service-access-preview">
    <div class="preview-header">
      <h2>{{ serviceInfo?.name || "服务接入预览" }}</h2>
      <div class="service-info">
        <span class="service-type">{{
          getServiceTypeLabel(serviceInfo?.config?.serviceType)
        }}</span>
        <span class="city-info"
          >{{ serviceInfo?.config?.cityName }} - {{ serviceInfo?.config?.adminArea }}</span
        >
      </div>
    </div>
    <div
      id="map-container"
      class="map-container"
      v-loading="loading"
      element-loading-text="正在加载地图..."
    ></div>
    <div class="preview-controls">
      <el-button @click="goBack">返回</el-button>
      <el-button type="primary" @click="refreshMap">刷新地图</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { Map, View } from "ol";
import { Tile as TileLayer } from "ol/layer";
import { XYZ } from "ol/source";
import { fromLonLat, transformExtent } from "ol/proj";
import { defaults as defaultControls, ScaleLine, MousePosition } from "ol/control";
import { createStringXY } from "ol/coordinate";
import { ServiceType, type ServiceAccessVo } from "@/interface/business/serviceAccess";
import { detailAccess } from "@/api/accessService";
import "ol/ol.css";

/**
 * @description 路由和响应式数据
 */
const route = useRoute();
const router = useRouter();
const loading = ref(true);
const map = ref<Map | null>(null);
const serviceInfo = ref<ServiceAccessVo | null>(null);

/**
 * @description 获取服务类型标签
 */
const getServiceTypeLabel = (serviceType?: ServiceType): string => {
  const typeMap = {
    [ServiceType.TIANDITU]: "天地图",
    [ServiceType.BAIDU]: "百度地图",
    [ServiceType.AMAP]: "高德地图",
    [ServiceType.CUSTOM]: "第三方地图"
  };
  return serviceType ? typeMap[serviceType] || "未知类型" : "";
};

/**
 * @description 解析瓦片URL模板
 */
const parseUrlTemplate = (template: string, serviceKey: string): string => {
  if (!template) {
    throw new Error("瓦片URL模板不能为空");
  }

  let parsedUrl = template;

  // 替换token相关占位符
  if (serviceKey) {
    parsedUrl = parsedUrl
      .replace(/\{token\}/g, serviceKey)
      .replace(/\{key\}/g, serviceKey)
      .replace(/\{ak\}/g, serviceKey);
  }

  // 替换坐标占位符（保持OpenLayers格式）
  parsedUrl = parsedUrl.replace(/\{z\}/g, "{z}").replace(/\{x\}/g, "{x}").replace(/\{y\}/g, "{y}");

  // 替换服务器编号占位符
  parsedUrl = parsedUrl
    .replace(/\{0-7\}/g, () => Math.floor(Math.random() * 8).toString())
    .replace(/\{1-4\}/g, () => Math.floor(Math.random() * 4 + 1).toString())
    .replace(/\{0-3\}/g, () => Math.floor(Math.random() * 4).toString());

  return parsedUrl;
};

/**
 * @description 获取坐标系配置
 */
const getProjectionConfig = (serviceType: ServiceType) => {
  switch (serviceType) {
    case ServiceType.TIANDITU:
      return {
        projection: "EPSG:4326",
        extent: [-180, -90, 180, 90]
      };
    case ServiceType.BAIDU:
      return {
        projection: "EPSG:3857", // 百度地图使用Web墨卡托投影
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
    case ServiceType.AMAP:
      return {
        projection: "EPSG:3857", // 高德地图使用Web墨卡托投影
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
    case ServiceType.CUSTOM:
    default:
      return {
        projection: "EPSG:3857",
        extent: [-20037508.34, -20037508.34, 20037508.34, 20037508.34]
      };
  }
};

/**
 * @description 创建瓦片图层
 */
const createTileLayer = (config: any): TileLayer<XYZ> => {
  const { serviceType, tileUrl, serviceKey, maxZoom = 18, minZoom = 1 } = config;
  const projectionConfig = getProjectionConfig(serviceType);
  const parsedUrl = parseUrlTemplate(tileUrl, serviceKey);

  return new TileLayer({
    source: new XYZ({
      url: parsedUrl,
      maxZoom: maxZoom,
      minZoom: minZoom,
      crossOrigin: "anonymous"
    })
  });
};

/**
 * @description 初始化地图
 */
const initMap = async () => {
  if (!serviceInfo.value?.config) {
    ElMessage.error("服务配置信息不完整");
    return;
  }

  const config = serviceInfo.value.config;
  const projectionConfig = getProjectionConfig(config.serviceType);

  try {
    // 创建瓦片图层
    const tileLayer = createTileLayer(config);

    // 计算中心点坐标
    let center = [116.4074, 39.9042]; // 默认北京坐标
    if (config.centerLng && config.centerLat) {
      center = [config.centerLng, config.centerLat];
    }

    // 根据坐标系转换中心点
    const transformedCenter =
      projectionConfig.projection === "EPSG:4326" ? center : fromLonLat(center);

    // 创建地图
    map.value = new Map({
      target: "map-container",
      layers: [tileLayer],
      view: new View({
        center: transformedCenter,
        zoom: 10,
        projection: projectionConfig.projection,
        extent: projectionConfig.extent
      }),
      controls: defaultControls().extend([
        new ScaleLine({ units: "metric" }),
        new MousePosition({
          coordinateFormat: createStringXY(6),
          projection: "EPSG:4326"
        })
      ])
    });

    loading.value = false;
    ElMessage.success("地图加载成功");
  } catch (error) {
    console.error("地图初始化失败:", error);
    ElMessage.error("地图加载失败，请检查服务配置");
    loading.value = false;
  }
};

/**
 * @description 刷新地图
 */
const refreshMap = () => {
  if (map.value) {
    map.value.getTargetElement()?.remove();
    map.value = null;
  }
  loading.value = true;
  setTimeout(() => {
    initMap();
  }, 100);
};

/**
 * @description 返回上一页
 */
const goBack = () => {
  router.back();
};

/**
 * @description 创建测试数据
 */
const createTestData = () => {
  return {
    id: 1,
    name: "天地图测试服务",
    config: {
      serviceType: ServiceType.TIANDITU,
      cityName: "北京市",
      adminArea: "朝阳区",
      tileUrl:
        "https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}",
      frontendKey: "FIXED_FRONTEND_KEY_2025",
      serviceKey: "your_tianditu_token_here",
      centerLng: 116.4074,
      centerLat: 39.9042,
      maxZoom: 18,
      minZoom: 1
    },
    configValue: "",
    remark: "测试用天地图服务"
  };
};

/**
 * @description 加载服务信息
 */
const loadServiceInfo = async () => {
  const serviceId = route.query.id as string;
  const testMode = route.query.test as string;

  // 如果是测试模式或没有服务ID，使用测试数据
  if (testMode === "true" || !serviceId) {
    serviceInfo.value = createTestData();
    await initMap();
    return;
  }

  try {
    const result = await detailAccess(serviceId);
    if (result.code === 200) {
      serviceInfo.value = result.data;
      if (serviceInfo.value) {
        serviceInfo.value.config = JSON.parse(result.data.configValue || "{}");
        await initMap();
      }
    } else {
      ElMessage.error("获取服务信息失败，使用测试数据");
      serviceInfo.value = createTestData();
      await initMap();
    }
  } catch (error) {
    console.error("加载服务信息失败:", error);
    ElMessage.warning("加载服务信息失败，使用测试数据");
    serviceInfo.value = createTestData();
    await initMap();
  }
};

/**
 * @description 组件挂载
 */
onMounted(async () => {
  await loadServiceInfo();
});

/**
 * @description 组件卸载
 */
onUnmounted(() => {
  if (map.value) {
    map.value.getTargetElement()?.remove();
    map.value = null;
  }
});
</script>

<style lang="scss" scoped>
.service-access-preview {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;

  .preview-header {
    background: white;
    padding: 16px 24px;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 8px 0;
      color: #333;
      font-size: 20px;
      font-weight: 600;
    }

    .service-info {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #666;

      .service-type {
        background: #e6f7ff;
        color: #1890ff;
        padding: 2px 8px;
        border-radius: 4px;
        font-weight: 500;
      }

      .city-info {
        color: #999;
      }
    }
  }

  .map-container {
    flex: 1;
    position: relative;
    background: #f0f0f0;
  }

  .preview-controls {
    background: white;
    padding: 16px 24px;
    border-top: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

:deep(.ol-control) {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
}

:deep(.ol-scale-line) {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 4px;
  padding: 2px 8px;
}

:deep(.ol-mouse-position) {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 4px 8px;
  font-family: monospace;
  font-size: 12px;
}
</style>
