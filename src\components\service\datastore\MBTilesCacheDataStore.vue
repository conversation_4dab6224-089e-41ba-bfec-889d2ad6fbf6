<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2024-05-07 15:32:43
 * @LastEditors: silei
 * @LastEditTime: 2024-05-08 11:04:18
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="url" label="mbtiles文件路径">
          <el-input v-model="value.url" class="file-input" placeholder="文件路径"> </el-input>
          <el-button @click="handleSelect">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="url" label="瓦片编号规则">
          <el-select
            v-model="value.scheme"
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            class="custom-dialog-select"
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择服务接口"
          >
            <el-option
              v-for="item in schemes"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect
      choose-type="file"
      accept=".mbtiles"
      v-if="dialogVisible"
      @file-selected="handleFileSelected"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, PropType, reactive, ref } from "vue";
import { FormRules } from "element-plus";
import FileSelect from "@/components/FileSelect.vue";
const rules = reactive<FormRules>({
  url: [{ required: true, message: "请选择文件路径", trigger: "blur" }],
  scheme: [{ required: true, message: "请选择瓦片编号规则", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        url: "",
        scheme: 0
      };
    }
  }
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const schemes = [
  {
    name: "TMS",
    value: "TMS"
  },
  {
    name: "XYZ",
    value: "XYZ"
  }
];
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
const dialogVisible = ref(false);
const handleSelect = () => {
  dialogVisible.value = true;
};
const handleFileSelected = (fileName: string) => {
  dialogVisible.value = false;
  value.value.url = fileName;
};
defineExpose({
  submitForm
});
</script>
