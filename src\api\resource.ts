/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-03-16 10:59:14
 * @LastEditors: silei
 * @LastEditTime: 2024-09-06 15:59:44
 */
import hRequest from "@/utils/http";
/**
 * 获取元数据
 * @param workspace 工作空间名称
 * @returns
 */
export const getPath = async (path: string, accept: string) => {
  const result = await hRequest.get<any>({
    url: `/resourceManage/path`,
    params: {
      path,
      accept
    }
  });
  return result;
};

export const uploadZip = async (file: File, path: string) => {
  const form = new FormData();
  form.append("file", file);
  const result = await hRequest.put<any>({
    url: `/resourceManage/upload`,
    params: {
      path
    },
    headers: {
      "Content-Length": "multipart/form-data"
    },
    data: form,
    timeout: 600000
  });
  return result;
};

export const getCacheFormats = async () => {
  const result = await hRequest.get<any>({
    url: `/serviceManage/cache/cacheFormat`
  });
  return result;
};
export const getWmsInfo = async (serviceName: string, serviceType: string) => {
  const result = await hRequest.get<any>({
    url: `/serviceManage/serviceInfo/wms/${serviceName}`,
    params: {
      serviceType
    }
  });
  return result;
};
export const getWmtsInfo = async (serviceName: string) => {
  const result = await hRequest.get<any>({
    url: `/serviceManage/serviceInfo/wmts/${serviceName}`
  });
  return result;
};

export const getMapServiceNames = async () => {
  const result = await hRequest.get<any>({
    url: `/mapPolymerize/serviceList/allMap`
  });
  return result;
};

export const publishQuickly = async (config: any) => {
  const result = await hRequest.post<any>({
    url: `/serviceManage/fastCreate/create`,
    data: {
      createServiceDtos: {
        serviceDtos: config
      }
    }
  });
  return result;
};
export const getImage = async (url: string) => {
  const result = await hRequest.get<any>({
    url,
    responseType: "blob"
  });
  return result;
};
export const getXYZMetaInfo = async (serviceName: string) => {
  const result = await hRequest.get<any>({
    url: `../services/${serviceName}/rest/xyz/tilemapresource.xml`,
    responseType: "blob"
  });
  return result;
};
