/*
 * @Author: xiao
 * @Date: 2022-11-09 17:15:01
 * @LastEditors: silei
 * @LastEditTime: 2023-11-15 09:54:25
 * @Description:
 */
module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  extends: ["plugin:vue/vue3-essential", "standard-with-typescript", "plugin:prettier/recommended"],
  overrides: [],
  parser: "vue-eslint-parser",
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    project: ["tsconfig.json"],
    parser: "@typescript-eslint/parser",
    extraFileExtensions: [".vue"]
  },
  plugins: ["vue", "@typescript-eslint"],
  rules: {
    // "off" or 0 - turn the rule off
    // "warn" or 1 - turn the rule on as a warning (doesn’t affect exit code)
    // "error" or 2 - turn the rule on as an error (exit code is 1 when triggered)
    "vue/multi-word-component-names": "off", // 关闭组件名需要用多个单词大写驼峰或者连字符
    "prettier/prettier": ["error", { endOfLine: "auto", semi: true }],
    "@typescript-eslint/explicit-function-return-type": "off",
    "no-unused-expressions": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-expressions": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/no-unused-vars": "off",
    "@typescript-eslint/promise-function-async": "off",
    "@typescript-eslint/strict-boolean-expressions": "off"
  },
  globals: {
    window: true
  }
};
