/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-02-09 16:47:15
 * @LastEditors: silei
 * @LastEditTime: 2023-02-09 17:24:54
 */
import X2JS from "x2js";
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class XmlUtil {
  private static readonly _x2js: X2JS = new X2JS();
  static ConvertToJson(xml: string) {
    return this._x2js.xml2js<any>(xml);
  }

  static ConvertDocToJson(xml: XMLDocument) {
    this._x2js.dom2js(xml);
  }

  static loadXMLStr(xmlString: string) {
    let xmlDoc = null;
    // 判断浏览器的类型
    // 支持IE浏览器
    // eslint-disable-next-line @typescript-eslint/strict-boolean-expressions
    if (!window.DOMParser && window.ActiveXObject) {
      // window.DOMParser 判断是否是非ie浏览器
      const xmlDomVersions = [
        "MSXML.2.DOMDocument.6.0",
        "MSXML.2.DOMDocument.3.0",
        "Microsoft.XMLDOM"
      ];
      for (let i = 0; i < xmlDomVersions.length; i++) {
        try {
          xmlDoc = new ActiveXObject(xmlDomVersions[i]);
          xmlDoc.async = false;
          xmlDoc.loadXML(xmlString); // loadXML方法载入xml字符串
          break;
        } catch (e) {}
      }
    }
    // 支持Mozilla浏览器
    else if (window.DOMParser && document.implementation) {
      try {
        /* DOMParser 对象解析 XML 文本并返回一个 XML Document 对象。
         * 要使用 DOMParser，使用不带参数的构造函数来实例化它，然后调用其 parseFromString() 方法
         * parseFromString(text, contentType) 参数text:要解析的 XML 标记 参数contentType文本
         *  的内容类型
         * 可能是 "text/xml" 、"application/xml" 或 "application/xhtml+xml" 中的一个。
         *  注意，不支持 "text/html"。
         */
        const domParser = new DOMParser();
        xmlDoc = domParser.parseFromString(xmlString, "text/xml");
      } catch (e) {}
    } else {
      return null;
    }

    return xmlDoc;
  }
}
