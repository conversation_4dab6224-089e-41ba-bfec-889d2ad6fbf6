<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-03 16:00:16
 * @LastEditors: silei
 * @LastEditTime: 2023-12-12 11:53:21
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="url" label="数据路径">
          <el-input v-model="value.url" class="file-input" placeholder="请选择dxf文件"> </el-input>
          <el-button @click="handleSelect()">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="coordinateSystem" label="坐标系">
          <el-input v-model="value.coordinateSystem" placeholder="坐标系"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect
      choose-type="file"
      accept=".dxf"
      v-if="dialogVisible"
      @file-selected="handleFileSelected"
    />
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, onMounted, PropType, reactive, ref } from "vue";
import { FormRules } from "element-plus";
import FileSelect from "@/components/FileSelect.vue";
import { BaseService, DatasourceType } from "geoserver-manager";
const rules = reactive<FormRules>({
  url: [{ required: true, message: "请输入文件路径", trigger: "blur" }],
  coordinateSystem: [{ required: true, message: "请输入坐标系统", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        url: "",
        coordinateSystem: "5432"
      };
    }
  }
});
const datasets = ref<any[]>([]);
const fields = ref<any[]>([]);
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const dialogVisible = ref(false);
const handleSelect = () => {
  dialogVisible.value = true;
};
const handleFileSelected = async (fileName: string) => {
  dialogVisible.value = false;
  value.value.url = fileName;
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
