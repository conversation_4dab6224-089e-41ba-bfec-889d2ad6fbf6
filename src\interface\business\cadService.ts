/*
 * @Description: Cad地图服务
 * @Autor: silei
 * @Date: 2023-12-12 11:28:59
 * @LastEditors: silei
 * @LastEditTime: 2024-05-29 16:40:43
 */
import {
  BaseServiceInfo,
  BaseSetting,
  GeoServerLayer,
  PreviewAddress,
  ServiceType
} from "geoserver-manager";
import { CadServiceInfo } from "./cadServiceInfo";
import { deleteService, editService, getServiceByName } from "@/api/cad";
export class CadService extends BaseServiceInfo {
  get layers(): Array<{ name: string; style: string }> {
    throw new Error("Method not implemented.");
  }

  set layers(value: Array<{ name: string; style: string }>) {
    throw new Error("Method not implemented.");
  }

  private _urls = [] as any[];
  private _changed = false;
  private _enabledServices = ["WMS", "WMTS"];
  get visits(): number {
    return this._service.visits ?? 0;
  }

  get errorMsg(): string {
    return this._service.errorMsg ?? "";
  }

  get dateCreated(): Date {
    if (this._service.dateCreated) {
      return new Date(this._service.dateCreated);
    }
    return new Date();
  }

  get dateModified(): Date {
    if (this._service.dateModified) {
      return new Date(this._service.dateModified);
    }
    return this.dateCreated;
  }

  get changed(): boolean {
    return this._changed;
  }

  set changed(value: boolean) {
    this._changed = value;
  }

  get name(): string {
    return this._service?.name;
  }

  get title(): string {
    return this._service?.title as any;
  }

  set title(value: string) {
    this._service && (this._service.title = value);
    this._changed = true;
  }

  get description(): string {
    return this._service?.description as any;
  }

  set description(value: string) {
    this._service && (this._service.description = value);
    this._changed = true;
  }

  get type(): ServiceType {
    return "cad" as any;
  }

  get enabledServices(): string[] {
    return this._enabledServices;
  }

  set enabledServices(value: string[]) {
    this._enabledServices = value;
  }

  get supportServices(): string[] {
    return this.getServiceList();
  }

  get serviceTypeString(): string {
    return "DXF地图服务";
  }

  get thumbnail(): string {
    return null!;
  }

  get urls(): Array<{ url: string; overviewUrl: string }> {
    return this._urls;
  }

  get enabled(): boolean {
    return this._service?.enabled !== false;
  }

  get cacheLayer(): GeoServerLayer {
    return null!;
  }

  set cacheLayer(value: GeoServerLayer) {}

  get datasource(): { [key: string]: any } {
    return {
      url: this._service.url,
      coordinateSystem: this._service.coordinateSystem
    };
  }

  set datasource(value) {
    this._service.url = value.url;
    this._service.coordinateSystem = value.coordinateSystem;
    this._changed = true;
  }

  private _service: CadServiceInfo;
  constructor() {
    super();
    this._service = {} as any;
  }

  async load(serviceName: string): Promise<BaseServiceInfo> {
    const service = await getServiceByName(serviceName);
    this._service = service.cadInfo;
    this._urls = this.getServiceUrl();
    return this;
  }

  from(service: CadServiceInfo): BaseServiceInfo {
    this._service = service;
    this._urls = this.getServiceUrl();
    return this;
  }

  async getPreviewUrl(): Promise<PreviewAddress[]> {
    const previews: PreviewAddress[] = [];
    previews.push({
      name: "for openlayers(wmts)",
      type: "openlayers",
      url: `wmtsPreview`,
      params: {
        serviceName: this.name
      }
    });
    previews.push({
      name: "for openlayers(wms)",
      type: "openlayers",
      url: `wmsPreview`,
      params: {
        serviceName: this.name,
        serviceType: "cad"
      }
    });
    return previews;
  }

  getServiceList() {
    return ["REST"];
  }

  getServiceUrl() {
    const serviceName = this.name;
    const urls = [];
    const basePath = BaseSetting.getBaseUrl();
    const wmsServiceInfo = {
      url: `${basePath}/bcgiscad/${serviceName}/wms`,
      overviewUrl: `${basePath}/bcgiscad/${serviceName}/wms?REQUEST=GetCapabilities`
    };
    const wmtsServiceInfo = {
      url: `${basePath}/bcgiscad/${serviceName}/gwc/service/wmts`,
      overviewUrl: `${basePath}/bcgiscad/${serviceName}/gwc/service/wmts?REQUEST=GetCapabilities`
    };
    urls.push(wmsServiceInfo, wmtsServiceInfo);
    return urls;
  }

  getServiceType() {
    return this.type;
  }

  async setEnabled(value: boolean): Promise<void> {
    this._service.enabled = value;
    await editService(this._service);
  }

  async delete(): Promise<void> {
    const serviceName = this.name;
    // 删除图层组
    await deleteService(serviceName);
  }

  async save() {
    if (this._changed) {
      await editService(this._service);
    }
  }
}
