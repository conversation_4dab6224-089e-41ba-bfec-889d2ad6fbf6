<!--
 * @Description: 等值线分析
 * @Autor: silei
 * @Date: 2023-11-17 15:39:26
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-01-02 11:34:15
-->
<template>
  <div class="custom-content">
    <div class="info-bg">
      <el-row class="card-header" :gutter="32">
        <el-col class="title-left" :span="20"
          ><img @click="cancel" src="@/assets/img/server-back.png" alt="" /><span @click="cancel"
            >返回 <b>|</b></span
          >
          <div class="text">等值线分析</div>
        </el-col>
      </el-row>
      <div class="box">
        <el-form
          :model="info"
          ref="formRef"
          :rules="crossRules"
          class="demo-ruleForm custom-sub-form"
          :label-position="'left'"
          status-icon
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="点图层" prop="layerName">
                <el-select
                  v-model="info.layerName"
                  class="custom-dialog-select"
                  placeholder="选择数据源"
                >
                  <el-option
                    v-for="(item, index) in layers"
                    :key="index"
                    :label="item.title"
                    :value="item.name"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="属性名称" prop="propertyName">
                <el-input placeholder="请输入" v-model="info.propertyName" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="间距" prop="interval">
                <el-input-number placeholder="请输入间距" v-model="info.interval" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="简化" prop="simplify">
                <el-select
                  v-model="info.simplify"
                  class="custom-dialog-select"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, index) in booleanValue"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="平滑" prop="smooth">
                <el-select v-model="info.smooth" class="custom-dialog-select" placeholder="请选择">
                  <el-option
                    v-for="(item, index) in booleanValue"
                    :key="index"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row>
          <el-col :span="24">
            <div class="custom-dialog-footer">
              <div class="dialog-submit" style="width: 140px" @click="showRequest">
                查看请求参数
              </div>
              <div class="dialog-submit" @click="startAnalysis">提交</div>
            </div>
          </el-col>
        </el-row>
        <div class="result-box" v-if="Object.keys(analysisResult).length > 0">
          <div class="result-title">分析结果</div>
          <div class="result-value">
            <el-input :readonly="true" type="textarea" v-model="analysisResult" />
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="请求参数"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="@/assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">请求参数详情</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <div style="white-space: pre-wrap">{{ showText }}</div>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-submit" @click="dialogVisible = false">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage, FormRules } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { contourAnalysis } from "@/api/analysis/network";
import { useRoute } from "vue-router";
import { getLayers } from "@/api/map/map";
import router from "@/router";
const dialogVisible = ref<boolean>(false);
const info = ref({} as any);
const formRef = ref();
const route = useRoute(); // 活跃状态的路由
const validate = (rule: any, value: any, callback: any) => {
  if (info.value.edgeId || info.value.nodeId) {
    callback();
  } else {
    callback(new Error("必须指定结点和边中的一个"));
  }
};
const cancel = () => {
  router.go(-1);
};
const crossRules = reactive<FormRules>({
  sourceLayer: [{ required: true, message: "源图层不能为空", trigger: "blur" }],
  targetLayer: [{ required: true, message: "目标图层不能为空", trigger: "blur" }],
  relation: [{ required: true, message: "关系不能为空", trigger: "blur" }]
});
const analysisResult: any = ref("");
const serviceName = route.query.name as string;
const startAnalysis = async () => {
  const data = await formRef.value.validate();
  if (data) {
    contourAnalysis(serviceName, info.value).then((res) => {
      ElMessage({
        message: "请求成功",
        type: "success"
      });
      analysisResult.value = JSON.stringify(res);
    });
  }
};
const showText = ref("");
const showRequest = () => {
  showText.value = JSON.stringify(info.value);
  dialogVisible.value = true;
};
const layers = ref([] as any);
const relation = [
  {
    name: "擦除",
    value: 0
  },
  {
    name: "相交",
    value: 1
  },
  {
    name: "联合",
    value: 2
  }
];
const booleanValue = [
  {
    name: "是",
    value: true
  },
  {
    name: "否",
    value: false
  }
];
const initData = async () => {
  const data = await getLayers(serviceName);
  layers.value = data;
};
onMounted(() => {
  initData();
});
</script>
<style lang="scss" scoped>
.box {
  width: 60%;
  border: 1px solid #e6eaef;
  padding: 20px 20px 30px;
}
.info-bg {
  width: 98%;
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
  padding-left: 2%;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
.result-value {
  margin-top: 20px;
  width: 98%;
  :deep(.el-textarea__inner) {
    min-height: 280px !important;
  }
}
.card-header {
  height: 65px;
  line-height: 65px;
  .title-left {
    display: flex;
    img {
      cursor: pointer;
      width: 16px;
      height: 16px;
      margin: 22px 10px 0 0px;
    }
    span {
      cursor: pointer;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #7d8da1;
      b {
        margin-left: 5px;
        font-weight: 100;
        color: rgba($color: #7d8da1, $alpha: 0.4);
      }
    }
    .text {
      margin: 0 10px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #232a3a;
    }
  }
}
</style>
