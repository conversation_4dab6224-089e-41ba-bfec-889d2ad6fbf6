<!--
 * @Description: 
 * @Date: 2023-02-06 11:42:26
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 15:20:48
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">切片缓存</div>
    </div>
    <div class="custom-table-box">
      <el-form label-position="top" class="custom-table-search-box">
        <div class="custom-search-item">
          <el-form-item label="服务名称">
            <el-input class="custom-input" v-model="serviceName" placeholder="请输入" />
          </el-form-item>
        </div>
        <div class="custom-search-item">
          <el-form-item class="online-form-btns">
            <div class="custom-query-button" @click="queryData">查询</div>
            <div class="custom-reset-button" @click="reset">重置</div>
          </el-form-item>
        </div>
      </el-form>
      <el-table
        :data="searchData"
        class="custom-table"
        header-row-class-name="custom-header-row"
        header-cell-class-name="custom-header-cell"
        style="width: 96%"
      >
        <el-table-column align="center" width="50">
          <template v-slot="scop">
            {{ scop.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="服务名称">
          <template v-slot="{ row }">{{ row.name.split(":")[0] }} </template>
        </el-table-column>
        <el-table-column prop="status" width="200" label="状态">
          <template v-slot="{ row }">{{ status[row.status] ?? "未知状态" }} </template>
        </el-table-column>
        <el-table-column prop="type" width="200" label="类型">
          <template v-slot="{ row }">{{ type[row.type] ?? "未知状态" }} </template>
        </el-table-column>
        <el-table-column prop="total" label="总切片数量" />
        <el-table-column prop="completed" label="已切片数量" />
        <el-table-column prop="elapsedTime" label="已用时间" />
        <el-table-column prop="remainingTime" label="剩余时间" />
        <el-table-column width="220" align="center" label="操作">
          <template v-slot="scope">
            <el-button text @click="cancel(scope.row.name)">取消切片</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { cancelTask, getGwcList } from "@/api/map/map";
import { ElMessageBox } from "element-plus";
const status = {
  RUNNING: "运行中",
  READY: "准备中"
} as Record<string, string>;
const type = {
  UNSET: "未设置",
  SEED: "只生成缺失瓦片",
  RESEED: "重新生成所有瓦片",
  TRUNCATE: "清空并重新生成"
} as Record<string, string>;
const searchData: any = ref([]);
const serviceName = ref("");
const queryData = async () => {
  searchData.value = await getGwcList(serviceName.value);
};
queryData();
const reset = async () => {
  serviceName.value = "";
  await queryData();
};
const cancel = async (name: string = "") => {
  await ElMessageBox.confirm("确定要取消当前任务吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  });
  await cancelTask(name);
  await queryData();
};
</script>
<style lang="scss" scoped>
.allData {
  width: 1300px;
  height: 100%;
}
.top-operate {
  display: flex;
  height: 50px;
  width: 100%;
  justify-content: flex-end;
}
.pagination-box {
  margin-top: 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
}
::v-deep(.el-transfer__buttons) {
  padding: 0 10px;
  width: 80px;
  span {
    font-size: 12px;
    color: #000000;
    font-weight: 400;
  }
  .el-transfer__button:nth-child(2) {
    margin: 10px 0 0 0px;
    font-size: 14px;
  }
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
::v-deep(.el-transfer-panel__body) {
  height: 130px;
}
::v-deep(.el-checkbox__label) {
  font-size: 12px;
}
::v-deep(.el-dialog__header) {
  display: flex;
}
::v-deep(.el-transfer-panel__list) {
  height: 130px;
}
.role-row-width {
  width: 90%;
}
.over-length {
  width: 100%;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap; // 默认不换行；
}
</style>
