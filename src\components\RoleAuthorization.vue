<!--
 * @Description: 
 * @Date: 2022-09-21 14:52:55
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-27 10:02:33
-->
<template>
  <div class="header">
    <el-table
      :data="tableData"
      class="custom-table role-table border"
      header-row-class-name="custom-header-row"
      header-cell-class-name="custom-header-cell"
      style="width: 97%"
    >
      <el-table-column label="可用角色" width="850">
        <template #default="scope">
          <div>{{ scope.row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="读" width="180">
        <template #default="scope">
          <div>
            <el-checkbox
              v-model="scope.row.read"
              @change="rowChange(scope.row, 'read')"
              name="type"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="写">
        <template #default="scope">
          <div>
            <el-checkbox
              v-model="scope.row.write"
              @change="rowChange(scope.row, 'write')"
              name="type"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from "vue";
import {
  getRolesList,
  getAllRoleLayers,
  roleAuthorizationAdd,
  roleAuthorizationEdit,
  roleAuthorizationDel
} from "@/api/security/role";
const props = defineProps(["serverName", "roleAll"]);
watch(
  () => props.roleAll,
  (value) => {
    console.log(value);
    allSelect.value = value;
    selectALL();
  }
);
const selectALL = () => {
  if (allSelect.value) {
    tableData.value.forEach((item: any) => {
      item.write = true;
      item.read = true;
      readList.value = allRoles.value;
      writeList.value = allRoles.value;
    });
  } else {
    tableData.value.forEach((item: any) => {
      item.write = false;
      item.read = false;
      readList.value = [];
      writeList.value = [];
    });
  }
};
const allSelect: any = ref(false);
const tableData: any = ref([]);
const writeName = `${props.serverName}.*.w`;
const readName = `${props.serverName}.*.r`;
const addRoleAuthorization = async () => {
  if (staticReadList.value.length === 0) {
    const obj: any = {};
    obj[readName] = readList.value.toString();
    await roleAuthorizationAdd(obj);
  }
  if (staticWriteList.value.length === 0) {
    const obj: any = {};
    obj[writeName] = writeList.value.toString();
    await roleAuthorizationAdd(obj);
  }
};
const editRoleAuthorization = async () => {
  if (staticReadList.value.length > 0 || staticWriteList.value.length > 0) {
    const obj: any = {};
    if (staticWriteList.value.length !== 0) {
      obj[writeName] = writeList.value.toString();
    }
    if (staticReadList.value.length !== 0) {
      obj[readName] = readList.value.toString();
    }
    await roleAuthorizationEdit(obj);
  }
};
// 记录读写改变
const rowChange = async (row: any, type: string) => {
  if (type === "write") {
    if (writeList.value.includes(row.name)) {
      writeList.value = writeList.value.filter((item: any) => {
        return item !== row.name;
      });
    } else {
      writeList.value.push(row.name);
    }
  }
  if (type === "read") {
    if (readList.value.includes(row.name)) {
      readList.value = readList.value.filter((item: any) => {
        return item !== row.name;
      });
    } else {
      readList.value.push(row.name);
    }
  }
};
// 提交
const submitForm = async () => {
  await addRoleAuthorization();
  await editRoleAuthorization();
  // 删除
  if (writeList.value.length === 0) {
    await roleAuthorizationDel(writeName);
  }
  if (readList.value.length === 0) {
    await roleAuthorizationDel(readName);
  }
  await inittableData();
};
const staticReadList: any = ref([]);
const staticWriteList: any = ref([]);
const readList: any = ref([]);
const writeList: any = ref([]);
const allRoles: any = ref([]);
const inittableData = async () => {
  tableData.value = [];
  const result: any = await getRolesList();
  allRoles.value = result.roles;
  result.forEach((item: any) => {
    tableData.value.push({
      name: item.name,
      read: false,
      write: false
    });
  });
  staticReadList.value = [];
  readList.value = [];
  staticWriteList.value = [];
  writeList.value = [];
  const allLayers: any = await getAllRoleLayers();
  const filterRole: any = [];
  for (const key of Object.keys(allLayers)) {
    if (key.split(".")[0] === props.serverName && key.split(".")[1] === "*") {
      let read = false;
      let write = false;
      if (key.split(".")[2] === "r") {
        read = true;
        if (allLayers[key]) {
          staticReadList.value = [...allLayers[key].split(",")];
          readList.value = [...allLayers[key].split(",")];
        } else {
          staticReadList.value = [];
          readList.value = [];
        }
        filterRole.push({
          read,
          write,
          names: [...allLayers[key].split(",")]
        });
      }
      if (key.split(".")[2] === "w") {
        write = true;
        if (allLayers[key]) {
          staticWriteList.value = [...allLayers[key].split(",")];
          writeList.value = [...allLayers[key].split(",")];
        } else {
          staticWriteList.value = [];
          writeList.value = [];
        }
        filterRole.push({
          read,
          write,
          names: [...allLayers[key].split(",")]
        });
      }
    }
  }
  for (let i = 0; i < tableData.value.length; i++) {
    for (let j = 0; j < filterRole.length; j++) {
      if (filterRole[j].names.includes(tableData.value[i].name)) {
        if (!tableData.value[i].read) {
          tableData.value[i].read = filterRole[j].read;
        }
        if (!tableData.value[i].write) {
          tableData.value[i].write = filterRole[j].write;
        }
      }
    }
  }
};
onMounted(async () => {
  await inittableData();
});
defineExpose({
  submitForm
});
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
}
.role-table {
  margin-left: 20px;
  height: 400px;
}
.border {
  border: 1px solid #e6eaef;
  border-radius: 4px;
}
</style>
