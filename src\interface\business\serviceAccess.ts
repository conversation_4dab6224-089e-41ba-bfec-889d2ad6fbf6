/**
 * @description 服务接入业务接口定义
 * <AUTHOR> Assistant
 * @date 2025-01-11
 */

/**
 * @enum ServiceType
 * @description 地图服务类型枚举
 */
export enum ServiceType {
  /** 天地图 */
  TIANDITU = "tianditu",
  /** 百度地图 */
  BAIDU = "baidu",
  /** 高德地图 */
  AMAP = "amap",
  /** 第三方地图 */
  CUSTOM = "custom"
}

/**
 * @interface CityInfo
 * @description 城市信息接口
 */
export interface CityInfo {
  /** 城市代码 */
  code: string;
  /** 城市名称 */
  name: string;
  /** 省份名称 */
  province: string;
  /** 中心点经度 */
  longitude: number;
  /** 中心点纬度 */
  latitude: number;
}

/**
 * @interface ServiceAccessConfig
 * @description 服务接入配置接口
 */
export interface ServiceAccessConfig {
  /** 服务类型 */
  serviceType: ServiceType;
  /** 城市名称 */
  cityName: string;
  /** 行政区域 */
  adminArea: string;
  /** 服务瓦片地址 */
  tileUrl: string;
  /** 前端密钥 */
  frontendKey: string;
  /** 服务密钥（token） */
  serviceKey: string;
  /** 中心点经度 */
  centerLng?: number;
  /** 中心点纬度 */
  centerLat?: number;
  /** 最大缩放级别 */
  maxZoom?: number;
  /** 最小缩放级别 */
  minZoom?: number;
}

/**
 * @interface BaseServiceAccess
 * @description 服务接入基础信息接口
 */
export interface BaseServiceAccess {
  /** 服务ID */
  id?: number;
  /** 服务名称 */
  name: string;
  /** 配置信息 */
  config: ServiceAccessConfig;
  /** 配置值（JSON字符串） */
  configValue?: string;
  /** 服务描述 */
  remark?: string;
}

/**
 * @interface ServiceAccessDto
 * @description 服务接入数据传输对象（用于新增和修改）
 */
export interface ServiceAccessDto extends BaseServiceAccess {
  /** 创建时间 */
  createTime?: string;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface ServiceAccessVo
 * @description 服务接入视图对象（用于详情查询）
 */
export interface ServiceAccessVo extends BaseServiceAccess {
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
  /** 修改人ID */
  updateBy?: number;
  /** 修改时间 */
  updateTime?: string;
}

/**
 * @interface ServiceAccessPageVo
 * @description 服务接入分页列表项
 */
export interface ServiceAccessPageVo {
  /** 服务ID */
  id: number;
  /** 服务名称 */
  name: string;
  /** 配置值 */
  configValue?: string;
  /** 创建人ID */
  createBy?: number;
  /** 创建时间 */
  createTime?: string;
}

/**
 * @interface ServiceAccessPageQuery
 * @description 服务接入分页查询参数
 */
export interface ServiceAccessPageQuery {
  /** 页面大小 */
  pageSize?: number;
  /** 页码 */
  pageNum?: number;
  /** 排序列 */
  orderByColumn?: string;
  /** 是否升序 */
  isAsc?: string;
  /** 起始位置 */
  firstNum?: number;
  /** 服务名称（搜索条件） */
  name?: string;
}

/**
 * @interface PageInfo<T>
 * @description 分页信息通用接口
 */
export interface PageInfo<T> {
  /** 总记录数 */
  totalCount: number;
  /** 页面大小 */
  pageSize: number;
  /** 总页数 */
  totalPage: number;
  /** 当前页码 */
  currPage: number;
  /** 数据列表 */
  list: T[];
}

/**
 * @interface ApiResponse<T>
 * @description API响应通用接口
 */
export interface ApiResponse<T> {
  /** 响应码 */
  code: number;
  /** 响应消息 */
  msg: string;
  /** 响应数据 */
  data: T;
}

/**
 * @type ServiceAccessPageInfo
 * @description 服务接入分页信息类型
 */
export type ServiceAccessPageInfo = PageInfo<ServiceAccessPageVo>;

/**
 * @type ServiceAccessDetailResponse
 * @description 服务接入详情响应类型
 */
export type ServiceAccessDetailResponse = ApiResponse<ServiceAccessVo>;

/**
 * @type ServiceAccessPageResponse
 * @description 服务接入分页响应类型
 */
export type ServiceAccessPageResponse = ApiResponse<ServiceAccessPageInfo>;

/**
 * @type ServiceAccessOperationResponse
 * @description 服务接入操作响应类型
 */
export type ServiceAccessOperationResponse = ApiResponse<any>;
