<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-21 09:21:27
 * @LastEditors: silei
 * @LastEditTime: 2024-10-21 10:07:39
-->
<template>
  <div>
    <div id="map-container"></div>
    <div class="operator-container">
      <el-form>
        <el-form-item label="">
          <el-button @click="handlePrint">打印</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- <el-upload
      ref="uploadRef"
      class="upload-demo"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :show-file-list="false"
      accept=".dwg"
      :on-change="loadCad"
      :auto-upload="false"
    >
      <template #trigger>
        <el-button>上传</el-button>
      </template>
    </el-upload> -->
  </div>
</template>
<script lang="ts" setup>
import { View, Map } from "ol";
import { Image as ImageLayer } from "ol/layer";
import * as Layers from "ol/layer";
import { Image, ImageWMS, TileWMS } from "ol/source";
import { GeoJSON, TopoJSON } from "ol/format";
import { onMounted } from "vue";
import { useRoute } from "vue-router";
import { getImage, getWmsInfo } from "../../../api/resource";
import "ol/ol.css";
import { BaseSetting, ServiceUtil } from "geoserver-manager";
import proj4 from "proj4";
import { boundingExtent, extend } from "ol/extent";
import { Projection } from "ol/proj";
import { ElMessage, UploadFile } from "element-plus";
import CadHelper from "@/utils/CadHelper";
import BaseLayer from "ol/layer/Base";
import { PrintPage } from "./printPage";
const route = useRoute();
const serviceName = route.query.serviceName as string;
const serviceType = (route.query.serviceType ?? "map") as any;
const baseUrl = BaseSetting.getBaseUrl();
let map: Map = null!;
const getExtentByBbox = (bbox: string) => {
  try {
    const bound = bbox.split(",");
    const minx = parseFloat(bound[0]);
    const miny = parseFloat(bound[1]);
    const maxx = parseFloat(bound[2]);
    const maxy = parseFloat(bound[3]);
    const mapExtent = boundingExtent([
      [minx, miny],
      [maxx, maxy]
    ]);
    return mapExtent;
  } catch {
    return undefined;
  }
};
const getBbox = (bbox: string) => {
  try {
    const bound = bbox.split(",");
    const minx = parseFloat(bound[0]);
    const miny = parseFloat(bound[1]);
    const maxx = parseFloat(bound[2]);
    const maxy = parseFloat(bound[3]);
    return [minx, miny, maxx, maxy];
  } catch {
    return undefined;
  }
};

onMounted(async () => {
  const wmsInfo = await getWmsInfo(serviceName as string, serviceType as string);
  proj4.defs(wmsInfo.srs, wmsInfo.srsWkt);
  const projection = new Projection({
    code: wmsInfo.srs,
    units: wmsInfo.units,
    axisOrientation: wmsInfo.neu ? "neu" : undefined
  });
  map = new Map({
    target: "map-container",
    view: new View({
      projection
    })
  });
  const name = serviceType === "cad" ? `bcgiscad/${serviceName}` : serviceName;
  const geoserverLayer = new Layers.Image({
    source: new ImageWMS({
      url: `${baseUrl}/${name}/wms`, // 请求地址
      params: {
        // 请求的参数设置，其余参数可以不设置，使用默认的参数
        LAYERS: wmsInfo.name, // 请求的图层名
        VERSION: "1.1.0", // wms请求的版本，也可用1.3.0
        FORMAT: wmsInfo.format,
        TILED: false,
        BBOX: getBbox(wmsInfo.bbox)
      }
      // imageLoadFunction: function (image, src) {
      //   getImage(src).then((data) => {
      //     if (data) {
      //       const url = URL.createObjectURL(data);
      //       const img: any = image.getImage();
      //       img.addEventListener("load", function () {
      //         URL.revokeObjectURL(url);
      //       });
      //       img.src = url;
      //     }
      //   });
      // }
    })
  });
  map.addLayer(geoserverLayer);
  map.getView().fit(getExtentByBbox(wmsInfo.bbox)!, {
    size: map.getSize()
  });
});

/**
 * 加载cad
 * @param uploadFile
 */
const loadCad = async (uploadFile: UploadFile) => {
  if (!uploadFile.name.endsWith("dwg")) {
    ElMessage({
      message: "请选择dwg文件",
      type: "warning"
    });
    return;
  }
  const cadwms = await CadHelper.initCadWms_Vj(uploadFile.raw!, "EPSG:4544");
  const cadLayer = new Layers.Tile({
    extent: cadwms.extent,
    source: new TileWMS({
      url: cadwms.url,
      params: cadwms.params
    })
  });
  map.addLayer(cadLayer);
};
const getAbsoluteUrl = (url: string) => {
  return url;
  // return Ext.DomHelper.overwrite(document.createElement("a"), { tag: "a", href: url }).href;
};
const encoders = {
  layers: {
    WMS: (layer: Layers.Image<ImageWMS>) => {
      let enc = encoders.layers.HTTPRequest.call(this, layer) as any;
      const params = layer.getSource()?.getParams();
      enc = {
        ...enc,
        type: "WMS",
        layers: [params.LAYERS].join(",").split(","),
        format: params.FORMAT,
        styles: [params.STYLES].join(",").split(","),
        customParams: {}
      };
      let param;
      for (const p in params) {
        param = p.toLowerCase();
        if ("layers,styles,width,height,srs".indexOf(param) === -1) {
          enc.customParams[p] = params[p];
        }
      }
      return enc;
    },
    // OSM: (layer: Osm) => {
    //   const enc = encoders.layers.TileCache.call(this, layer);
    //   return {
    //     ...enc,
    //     type: "Osm",
    //     baseURL: enc.baseURL.substring(0, enc.baseURL.indexOf("$")),
    //     extension: "png"
    //   };
    // },
    // TileCache: (layer: TileCache) => {
    //   const enc = encoders.layers.HTTPRequest.call(this, layer);
    //   return {
    //     ...enc,
    //     type: "TileCache",
    //     layer: layer.layername,
    //     maxExtent: layer.maxExtent.toArray(),
    //     tileSize: [layer.tileSize.w, layer.tileSize.h],
    //     extension: layer.extension,
    //     resolutions: layer.serverResolutions || layer.resolutions
    //   };
    // },
    HTTPRequest: (layer: Layers.Image<ImageWMS>) => {
      const url = layer.getSource()?.getUrl() as any;
      return {
        baseURL: getAbsoluteUrl(url instanceof Array ? url[0] : url),
        opacity: layer.getOpacity() != null ? layer.getOpacity() : 1.0,
        singleTile: true
      };
    }
    // Image: function (layer: Layers.Image<Image>) {
    //   const source = layer.getSource();
    //   return {
    //     type: "Image",
    //     baseURL: getAbsoluteUrl(source.getURL(layer.extent)),
    //     opacity: layer.opacity != null ? layer.opacity : 1.0,
    //     extent: layer.extent.toArray(),
    //     pixelSize: [layer.size.w, layer.size.h],
    //     name: layer.name
    //   };
    // },
    // Vector: function (layer: Vector) {
    //   if (!layer.features.length) {
    //     return;
    //   }
    //   const encFeatures = [];
    //   const encStyles = {} as any;
    //   const features = layer.features;
    //   const featureFormat = new GeoJSON();
    //   const styleFormat = new TopoJSON();
    //   let nextId = 1;
    //   const styleDict = {} as any;
    //   let feature, style, dictKey, dictItem;
    //   for (let i = 0, len = features.length; i < len; ++i) {
    //     feature = features[i];
    //     style =
    //       feature.style ||
    //       layer.style ||
    //       layer.styleMap.createSymbolizer(feature, feature.renderIntent);
    //     dictKey = styleFormat.write(style);
    //     dictItem = styleDict[dictKey];
    //     let styleName;
    //     if (dictItem) {
    //       styleName = dictItem;
    //     } else {
    //       styleDict[dictKey] = styleName = nextId++;
    //       if (style.externalGraphic) {
    //         encStyles[styleName] = {
    //           externalGraphic: getAbsoluteUrl(style.externalGraphic),
    //           ...style
    //         };
    //       } else {
    //         encStyles[styleName] = style;
    //       }
    //     }
    //     const featureGeoJson = featureFormat.writeFeature(feature);
    //     // featureGeoJson.properties = OpenLayers.Util.extend(
    //     //   { _gx_style: styleName },
    //     //   featureGeoJson.properties
    //     // );
    //     encFeatures.push(featureGeoJson);
    //   }
    //   return {
    //     type: "Vector",
    //     styles: encStyles,
    //     styleProperty: "_gx_style",
    //     geoJson: { type: "FeatureCollection", features: encFeatures },
    //     name: layer.name,
    //     opacity: layer.opacity != null ? layer.opacity : 1.0
    //   };
    // }
  }
  // legends: {
  //   gx_wmslegend:  (legend)=> {
  //     return encoders.legends.base.call(this, legend);
  //   },
  //   gx_urllegend: (legend)=> {
  //     return encoders.legends.base.call(this, legend);
  //   },
  //   base: (legend)=> {
  //     const enc = [];
  //     legend.items.each((cmp)=> {
  //       if (cmp instanceof Ext.form.Label) {
  //         enc.push({ name: cmp.text, classes: [] });
  //       } else if (cmp instanceof GeoExt.LegendImage) {
  //         enc.push({ name: "", icon: getAbsoluteUrl(cmp.url), classes: [] });
  //       }
  //     }, this);
  //     return enc;
  //   }
  // }
};
const getPrintCapabilities = async () => {
  const json = await fetch(`${baseUrl}/pdf/info.json`);
  return await json.json();
};
const encodeLayer = (layer: BaseLayer) => {
  let encLayer;
  if (layer instanceof ImageLayer) {
    encLayer = encoders.layers.WMS.call(this, layer);
  }
  return encLayer && encLayer.type ? encLayer : null;
};
// let feature = new OpenLayers.Feature.Vector(
//   OpenLayers.Geometry.fromWKT("POLYGON((-1 -1,1 -1,1 1,-1 1,-1 -1))")
// );
// const setCenter = (center) => {
//   const geom = this.feature.geometry;
//   const oldCenter = geom.getBounds().getCenterLonLat();
//   const dx = center.lon - oldCenter.lon;
//   const dy = center.lat - oldCenter.lat;
//   geom.move(dx, dy);
//   this.updateFeature(geom);
// };
// const calculatePageBounds = (scale: number, units: string) => {
//   const s = scale.get("value");
//   const geom = this.feature.geometry;
//   const center = geom.getBounds().getCenterLonLat();
//   const size = { width: 440, height: 483 };
//   const _units = units || "dd";
//   const unitsRatio = INCHES_PER_UNIT[_units];
//   const w = ((size.width / 72 / unitsRatio) * s) / 2;
//   const h = ((size.height / 72 / unitsRatio) * s) / 2;
//   return new (center.lon - w, center.lat - h, center.lon + w, center.lat + h)();
// };
const handlePrint = async () => {
  const jsonData = {
    units: map.getView().getProjection().getUnits(),
    srs: map.getView().getProjection().getCode(),
    layout: "A4 portrait",
    dpi: 75
  } as any;
  // var pagesLayer = pages[0].feature.layer;
  const encodedLayers = [] as any[];
  map.getLayers().forEach((layer) => {
    if (layer.getVisible()) {
      const enc = encodeLayer(layer);
      enc && encodedLayers.push(enc);
    }
  });
  jsonData.layers = encodedLayers;
  const encodedPages = [] as any[];
  const printPage = new PrintPage();
  printPage.fitPage(map);
  const center = printPage.getCenter();
  const service = await ServiceUtil.getServiceInfo(serviceName, serviceType);
  encodedPages.push({
    center: [center[0], center[1]],
    scale: printPage.scale,
    rotation: printPage.rotation,
    mapTitle: service.title,
    comment: service.description ?? service.title
  });
  // Ext.each(
  //   pages,
  //   function (page) {
  //     const center = page.getCenter();
  //     encodedPages.push(
  //       Ext.apply(
  //         {
  //           center: [center.lon, center.lat],
  //           scale: page.scale.get("value"),
  //           rotation: page.rotation
  //         },
  //         page.customParams
  //       )
  //     );
  //   },
  //   this
  // );
  jsonData.pages = encodedPages;
  // if (options.legend) {
  //   let encodedLegends = [];
  //   options.legend.items.each(function (cmp) {
  //     const encFn = this.encoders.legends[cmp.getXType()];
  //     encodedLegends = encodedLegends.concat(encFn.call(this, cmp));
  //   }, this);
  //   jsonData.legends = encodedLegends;
  // }
  const capabilities = await getPrintCapabilities();
  const data = JSON.stringify(jsonData);
  console.log(data);
  window.open(capabilities.printURL + "?spec=" + encodeURIComponent(data));
};
</script>
<style lang="scss" scoped>
#map-container {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
}
.upload-demo {
  position: absolute;
  top: 20px;
  left: 40px;
}
.operator-container {
  position: absolute;
  right: 0;
  width: 200px;
  padding: 20px;
  z-index: 1;
}
</style>
