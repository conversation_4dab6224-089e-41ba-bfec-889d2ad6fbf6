/*
 * @Description: 角色
 * @Autor: xyq
 * @Date: 2023-01-31 13:50:50
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-13 14:41:53
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
/**
 * 获取角色列表
 * @returns all
 */
export const getRolesList = async () => {
  const result = await hRequest.get<DataType>({
    url: "/authManage/roles",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加角色名称
 * @returns all
 */
export const addRoleName = async (data: {}) => {
  const result = await hRequest.post<DataType>({
    url: `/authManage/roles`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 修改角色描述
 * @returns all
 */
export const modifyRoleDescription = async (data: any) => {
  const result = await hRequest.put<DataType>({
    // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
    url: `/authManage/roles/${data.roleDto.name}`,
    data,
    maxRedirects: 0,
    timeout: 1000
  });
  return result;
};
/**
 * 删除角色
 * @returns all
 */
export const delRole = async (role: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/roles/role/${role}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加角色与用户关联
 * @returns all
 */
export const addRoleUser = async (role: string, user: string) => {
  const result = await hRequest.post<DataType>({
    url: `/security/roles/role/${role}/user/${user}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 取消角色与用户关联
 * @returns all
 */
export const delRoleUser = async (role: string, user: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/roles/role/${role}/user/${user}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加角色与用户租关联
 * @returns all
 */
export const addRoleGroup = async (role: string, group: string) => {
  const result = await hRequest.post<DataType>({
    url: `/security/roles/role/${role}/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 取消角色与用户组关联
 * @returns all
 */
export const delRoleGroup = async (role: string, group: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/roles/role/${role}/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 获取用户下角色去判断是否存在该用户
 * @returns all
 */
export const getRoleUser = async (user: string) => {
  const result = await hRequest.get<DataType>({
    url: `/security/roles/user/${user}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加用户与用户租关联
 * @returns all
 */
export const addUserGroup = async (user: string, group: string) => {
  const result = await hRequest.post<DataType>({
    url: `/security/usergroup/user/${user}/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 取消用户与用户租关联
 * @returns all
 */
export const delUserGroup = async (user: string, group: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/usergroup/user/${user}/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 角色授权回显
 * @returns all
 */
export const getAllRoleLayers = async () => {
  const result = await hRequest.get<DataType>({
    url: `/security/acl/layers`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 角色授权添加
 * @returns all
 */
export const roleAuthorizationAdd = async (data: {}) => {
  const result = await hRequest.post<DataType>({
    url: `/security/acl/layers`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 角色授权修改
 * @returns all
 */
export const roleAuthorizationEdit = async (data: {}) => {
  const result = await hRequest.put<DataType>({
    url: `/security/acl/layers`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 角色授权删除关联
 * @returns all
 */
export const roleAuthorizationDel = async (data: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/acl/layers/${data}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
