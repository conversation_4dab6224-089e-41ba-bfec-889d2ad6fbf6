<!--
 * @Description: cad地图服务
 * @Autor: silei
 * @Date: 2023-12-12 11:24:34
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-01-02 14:53:38
-->
<template>
  <div class="custom-content">
    <el-collapse v-loading="loading" v-model="activeName" accordion>
      <div class="custom-table-header-row">
        <div class="custom-table-title">服务管理</div>
        <div class="custom-button-group">
          <div class="search-input">
            <el-input
              v-model="search"
              clearable
              class="w-50 m-2"
              placeholder="输入服务名称"
              :prefix-icon="Search"
              @change="filterServices"
            />
          </div>

          <div class="custom-table-wihte" @click="reloadService">
            <img src="../../assets/img/reStart.png" alt="" />
            <span>重启所有服务</span>
          </div>
          <div class="custom-table-add" @click="createService">
            <img src="../../assets/img/add.png" alt="" />
            <span>创建服务</span>
          </div>
        </div>
      </div>
      <ServiceList v-model="serverChild" @service-deleted="loadServices" />
    </el-collapse>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="创建服务"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">创建服务</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <ServiceCeate @canceled="dialogVisible = false" @completed="createCompleted" />
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Search } from "@element-plus/icons-vue";
import { onMounted, Ref, ref } from "vue";
import { getServices } from "@/api/cad";
import ServiceList from "../serviceManage/ServiceList.vue";
import ServiceCeate from "./serviceCreate/ServiceCeate.vue";
import { CadService } from "@/interface/business/cadService";
const severActive = ref("地图服务");
const selectServer = (server: any) => {
  severActive.value = server.title;
  filterServices();
};
const activeName: Ref<string> = ref("");
const serverChild = ref<any[]>([]);
// eslint-disable-next-line no-undef
const services: Ref<any[]> = ref([]);
const dialogVisible: Ref<boolean> = ref(false);
const search = ref("");
const filterServices = async () => {
  serverChild.value = services.value.filter((s: any) => s.title.includes(search.value));
};
/**
 * 获取图层组列表
 */
const loadServices = async () => {
  const serviceList = await getServices();
  // 根据角色筛选服务
  // const manager = await SecurityManager.Instance();
  // const roles = localCache.getCache("userRoles");
  // if (!roles.includes("ADMIN")) {
  //   for (const service of serviceList) {
  //     service.children = service.children.filter((s: BaseServiceInfo) => {
  //       const b = manager.isServiceReadable(s.name, roles);
  //       return b;
  //     });
  //   }
  // }
  const datas = !serviceList.cadInfos?.cadInfo
    ? []
    : serviceList.cadInfos.cadInfo instanceof Array
    ? serviceList.cadInfos.cadInfo
    : [serviceList.cadInfos.cadInfo];
  services.value = datas.map((s: any) => new CadService().from(s));
  selectServer(services.value[0]);
};

const createService = () => {
  dialogVisible.value = true;
};
const createCompleted = () => {
  dialogVisible.value = false;
  loadServices();
};
const loading = ref(false);
/**
 * 重启所有服务
 */
const reloadService = async () => {
  loading.value = true;
  try {
    await loadServices();
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  loadServices();
});
</script>
<style lang="scss" scoped>
.search-input {
  width: 350px;
  display: flex;
  margin-right: 20px;
  height: 45px;
}
.sever-box {
  width: 1600px;
  height: 80px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.server-item {
  cursor: pointer;
  width: 200px;
  height: 100%;
  display: flex;
  align-items: center;
  img {
    width: 35px;
    height: 35px;
    margin: 0 20px;
  }
  .right {
    width: 120px;
    display: flex;
    flex-direction: column;
    .titile {
      width: 72px;
      height: 27px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #232a3a;
    }
    span {
      width: 88px;
      height: 20px;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #7d8da1;
    }
  }
}
.active-title {
  color: #4076f3 !important;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
