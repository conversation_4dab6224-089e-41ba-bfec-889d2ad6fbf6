/*
 * @Descripttion: 基于vjmap解析cad进项地图加载、预览
 * @Author: Tanqy
 * @Date: 2022-10-26 09:24:26
 * @LastEditors: silei
 * @LastEditTime: 2023-07-11 18:02:52
 */
import vjmap from "vjmap";
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export default class CadHelper {
  /**
   * @description:
   * @param {}
   * @return:
   */
  static async initCadWms_Vj(file: File, epsg: string) {
    // const vjmap_env = await queryDeploy({ code: "vjmap_env" });
    // const env = JSON.parse(vjmap_env.data?.value);
    const token =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJJRCI6MSwiVXNlcm5hbWUiOiJyb290MSIsIk5pY2tOYW1lIjoicm9vdDEiLCJBdXRob3JpdHlJZCI6InJvb3QiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxOTQyMzA1MjQxLCJpc3MiOiJ2am1hcCIsIm5iZiI6MTYyNjk0NDI0MX0.29OAA4Cnjtw780VxIxWqduLtszp1EtpSNHAmWjiL_OM";
    const svc = new vjmap.Service("https://vjmap.com/server/api/v1", token);
    const res = await svc.uploadMap(file);
    const cadMapId: string = res.mapid;
    const fileid = res.fileid;
    // 获取缩略图
    // let thumbnailurl = svc.thumbnailUrl(cadMapId, "v1", 200, 200);
    // palanForm.value.thumbnailUrl = thumbnailurl;

    const res1 = await svc.openMap({
      mapid: cadMapId, // 地图ID
      fileid,
      mapopenway: vjmap.MapOpenWay.GeomRender, // 以几何数据渲染方式打开
      style: vjmap.openMapDarkStyle() // div为深色背景颜色时，这里也传深色背景样式
    });
    if (res1.error) {
      // 如果打开出错
      console.log("CAD图纸渲染错误：", res1.err);
    }
    const style = await svc.createStyle(
      {
        backcolor: 0xffffff // 浅色主题
      },
      cadMapId
    );
    // const mapBounds = vjmap.GeoBounds.fromString(res1.bounds);
    // const points = [];
    // points.push([mapBounds.min.x, mapBounds.min.y]);
    // points.push([mapBounds.max.x, mapBounds.max.y]);
    // // 得到坐标转换后的墨卡托点
    // const mktPoints = await svc.cmdTransform(
    //   epsg,
    //   "EPSG:4326",
    //   points.map((a) => vjmap.geoPoint(a as any))
    // );
    // 增加cad的wms图层
    const wmsUrl = svc.wmsTileUrl({
      mapid: cadMapId, // 地图id
      layers: style.stylename, // 图层名称
      bbox: "", // bbox这里不需要传，cesium会自动加上
      srs: "EPSG:4326", // cesium地图是wgs84
      crs: epsg
      // crs: "EPSG:3857", // 先把wgs84转3857
      // fourParameter: [fourparam.dx.toString(),fourparam.dy.toString(),fourparam.scale.toString(),fourparam.rotate.toString()] // 转成 3857后，再用四参数去转成cad坐标
    });
    // const wmsUrl = {
    //   url: `https://vjmap.com/server/api/v1/map/cmd/wms/${cadMapId}/v1?token=${token}`,
    //   // mapid: cadMapId, // 地图id
    //   layers: style.stylename, // 图层名称
    //   bbox: [...mktPoints[0], ...mktPoints[1]], // bbox这里不需要传，cesium会自动加上
    //   srs: "EPSG:4326", // cesium地图是wgs84
    //   crs: epsg
    //   // crs: "EPSG:3857", // 先把wgs84转3857
    //   // fourParameter: [fourparam.dx.toString(),fourparam.dy.toString(),fourparam.scale.toString(),fourparam.rotate.toString()] // 转成 3857后，再用四参数去转成cad坐标
    // };
    // 增加cad的wms图层
    function getQueryStringArgs(url: string) {
      const theRequest: any = {};
      const idx = url.indexOf("?");
      if (idx !== -1) {
        const str = url.substr(idx + 1);
        const strs = str.split("&");
        for (let i = 0; i < strs.length; i++) {
          const items = strs[i].split("=");
          theRequest[items[0]] = items[1];
        }
      }
      return theRequest;
    }

    const mapBounds = vjmap.GeoBounds.fromString(res1.bounds);
    // cad图坐标转web wgs84坐标
    const cadToWebCoordinate = async (point: vjmap.GeoPoint | vjmap.GeoPoint[]) => {
      const co = await svc.cmdTransform(epsg, "EPSG:4326", point);
      return co[0];
    };
    // cad转wgs84经纬度
    const boundsMin = await cadToWebCoordinate(mapBounds.min);
    const boundsMax = await cadToWebCoordinate(mapBounds.max);
    // wgs84经纬度转墨卡托
    // boundsMin = vjmap.Projection.lngLat2Mercator(boundsMin);
    // boundsMax = vjmap.Projection.lngLat2Mercator(boundsMax);
    const result = {
      extent: [boundsMin[0], boundsMin[1], boundsMax[0], boundsMax[1]],
      url: wmsUrl.substr(0, wmsUrl.indexOf("?")),
      // url: wmsUrl,
      params: { ...getQueryStringArgs(wmsUrl), TILED: true, VERSION: "1.1.1" }
    };
    return result;
  }
}
