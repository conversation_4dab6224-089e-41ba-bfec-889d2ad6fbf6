<!--
 * @Description: 选择发布图层
 * @Autor: silei
 * @Date: 2023-11-20 11:24:22
 * @LastEditors: silei
 * @LastEditTime: 2023-11-20 17:34:35
-->
<template>
  <el-table ref="multipleTableRef" :data="layerData" style="width: 100%">
    <el-table-column type="selection" width="55" />
    <el-table-column type="index" label="序号" width="55" />
    <el-table-column property="name" label="名称" />
    <el-table-column property="type" label="类型" />
  </el-table>
</template>
<script setup lang="ts">
import { nextTick, ref, watch } from "vue";
const props = defineProps({
  layerData: {
    type: Array,
    default: () => {
      return {};
    }
  }
});

watch(
  () => props.layerData,
  () => {
    nextTick(() => {
      props.layerData.forEach((layer) => {
        multipleTableRef.value.toggleRowSelection(layer, undefined);
      });
    });
  }
);
const multipleTableRef = ref();
const submitForm = () => {
  const data = multipleTableRef.value.getSelectionRows();
  if (data.length > 0) {
    return data;
  } else {
    return null;
  }
};
defineExpose({
  submitForm
});
</script>
