/*
 * @Description: 用户权限接口
 * @Autor: silei
 * @Date: 2023-01-31 13:50:50
 * @LastEditors: silei
 * @LastEditTime: 2023-06-13 10:10:39
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
/**
 * 用户登录
 * @returns 成功返回true,失败返回false
 */
export const login = async (data: {}) => {
  const result = await hRequest.post<DataType>({
    url: "../auth/login",
    data: {
      loginUser: data
    },
    timeout: 10000
  });
  return result;
};
/**
 * 退出登录
 * @returns
 */
export const logout = async () => {
  const result = await hRequest.post<DataType>({
    url: "../j_spring_security_logout",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * 验证授权
 * @returns
 */
export const verify = async () => {
  const result = await hRequest.post<DataType>({
    url: "../authorize/verify"
  });
  return result;
};

/**
 * 注册授权码
 * @returns
 */
export const verifySave = async (code: string) => {
  const result = await hRequest.post<DataType>({
    url: "../authorize/verifySave",
    data: code
  });
  return result;
};
