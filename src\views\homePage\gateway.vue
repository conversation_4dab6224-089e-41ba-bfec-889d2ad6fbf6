<template>
  <div class="container">
    <div class="bg">
      <div class="title">提供全功能的GIS服务发布、管理与聚合能力， 并支持多层次的扩展开发</div>
      <div class="top">
        <div v-for="item in topList" :key="item.name" class="item">
          <div class="icon-bg">
            <img :src="item.icon" alt="" />
          </div>
          <div>
            <div class="name">{{ item.name }}</div>
            <div class="value">
              <span>{{ item.value }}</span>
              <span>{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="btm">
      <div class="item" v-for="item in list" :key="item.name">
        <div class="name">{{ item.name }}</div>
        <div class="value">{{ item.content }}</div>
        <div class="lt">
          <img src="@/assets/img/home/<USER>" alt="" />
        </div>
        <div class="rt">
          <img src="@/assets/img/home/<USER>" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
const topList = ref([
  {
    name: "服务数量",
    value: "261",
    unit: "个",
    icon: require("@/assets/img/home/<USER>")
  },
  {
    name: "访问数量",
    value: "261",
    unit: "个",
    icon: require("@/assets/img/home/<USER>")
  },
  {
    name: "调用服务次数",
    value: "261",
    unit: "次",
    icon: require("@/assets/img/home/<USER>")
  }
]);
const list = ref([
  {
    name: "服务能力",
    content:
      "提供强大的空间数据管理、分析及可视化服务，支持多源数据集成与实时更新，具备高效的地理空间查询、空间分析、网络分析等能力。"
  },
  {
    name: "平台特性",
    content:
      "以“智能、高效、开放、安全”为核心特性，具备多源数据融合、高性能空间计算、实时动态可视化及跨平台协同能力。"
  },
  {
    name: "服务扩展能力",
    content:
      "提供标准化API接口和开发套件(SDK)，支持与第三方系统无缝对接，并允许用户基于业务需求定制专属空间分析功能与可视化应用。"
  },
  {
    name: "水务专题",
    content:
      "为智慧水务“产-供-销”全流程提供智能化空间决策支持，覆盖水厂生产管理、管网资产数字化、漏损精准定位、风险预警防控、供水优化调度、二次供水监管及营销服务分析等核心场景。"
  }
]);
</script>
<style lang="scss" scoped>
.container {
  padding: 20px;
  box-sizing: border-box;
}
.bg {
  width: 100%;
  // height: 528px;
  height: calc(100vh - 510px);
  background: url(@/assets/img/home/<USER>
  background-size: 100% 100%;
  padding: 0 40px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-bottom: 20px;
  .title {
    font-size: 42px;
    font-weight: 600;
    width: 869px;
  }
  .top {
    display: flex;
    margin-top: 60px;
  }
  .icon-bg {
    width: 72px;
    height: 72px;
    border-radius: 8px;
    background: rgba(64, 118, 243, 1);
    box-shadow: 0px 14px 30px 0px rgba(64, 118, 243, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
  }
  .name {
    font-size: 18px;
    font-weight: 500;
    color: #232a3a;
  }
  .value {
    color: #4076f3;
    font-size: 30px;
    font-weight: 600;
  }
  .item {
    display: flex;
    align-items: center;
    margin-right: 100px;
  }
}
.btm {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  .item {
    width: 380px;
    height: 384px;
    background: #fff;
    margin-bottom: 20px;
    box-sizing: border-box;
    padding: 20px;
    position: relative;
  }
  .name {
    font-size: 28px;
    font-weight: 600;
    color: #232a3a;
  }
  .value {
    font-size: 18px;
    font-weight: 400;
    color: #858a9c;
    margin-top: 10px;
  }
  .rt {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .lt {
    position: absolute;
    left: 20px;
    bottom: 20px;
  }
}
</style>
