<!--
 * @Description: 服务管理
 * @Autor: silei
 * @Date: 2023-01-31 16:18:32
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-02-17 13:53:10
-->
<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-demo"
    mode="horizontal"
    @select="handleSelect"
  >
    <el-menu-item index="serviceOverview">概述</el-menu-item>
    <el-menu-item index="serviceManage">服务管理</el-menu-item>
    <el-menu-item index="serviceStyleManage">样式</el-menu-item>
  </el-menu>
  <router-view :key="key" />
</template>
<script lang="ts" setup>
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
const key = computed(() => {
  return route.path + Math.random();
});
const activeIndex = ref("");
const handleSelect = (key: string, keyPath: string[]) => {
  router.push({ name: key });
};
onMounted(() => {
  activeIndex.value = route.path.split("/")[route.path.split("/").length - 1];
});
</script>
<style lang="scss" scoped>
.el-menu-demo {
  width: 100%;
}
::v-deep(.el-tabs__content) {
  width: 100%;
  height: 90%;
  padding: 0;
  display: flex;
  justify-content: center;
}
::v-deep(.el-menu-item) {
  margin: 0 30px;
  font-size: 14px;
}
.el-menu {
  padding: 0 0 0 80px !important;
}
</style>
