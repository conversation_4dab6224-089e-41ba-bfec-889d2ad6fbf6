<template>
  <el-dialog
    v-model="value"
    :close-on-click-modal="false"
    destroy-on-close
    title="创建服务"
    width="34%"
  >
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">切片缓存</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <el-form
      :rules="rules"
      :model="form"
      ref="formRef"
      class="demo-ruleForm custom-sub-form"
      label-position="top"
      status-icon
    >
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item prop="type" label="操作类型">
            <el-select v-model="form.type" class="custom-dialog-select" placeholder="选择操作类型">
              <el-option
                v-for="(item, index) in types"
                :key="index"
                :label="item.name"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="threadCount" label="线程数">
            <el-input-number
              v-model="form.threadCount"
              :min="0"
              :max="20"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item prop="gridSetId" label="坐标系">
            <el-select
              v-model="form.gridSetId"
              class="custom-dialog-select"
              placeholder="选择坐标系"
            >
              <el-option
                v-for="(item, index) in serviceInfo.gridSets"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="format" label="切片格式">
            <el-select v-model="form.format" class="custom-dialog-select" placeholder="选择坐标系">
              <el-option
                v-for="(item, index) in serviceInfo.formats"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item prop="zoomStart" label="最小层级">
            <el-input-number
              v-model="form.zoomStart"
              :min="0"
              :max="20"
              controls-position="right"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="zoomStop" label="最大层级">
            <el-input-number v-model="form.zoomStop" :min="0" :max="20" controls-position="right" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row class="custom-dialog-footer create-footer">
      <el-button :loading="btnLoading" class="dialog-cancle" @click="cancel">取消</el-button>
      <el-button :loading="btnLoading" class="dialog-submit" @click="submit"> 确定 </el-button>
    </el-row>
  </el-dialog>
</template>
<script setup lang="ts">
import { getGwcList, getWmtsInfo, gwcSeed } from "@/api/map/map";
import { ElMessage, FormRules } from "element-plus";
import { computed, reactive, ref } from "vue";
import { useRouter } from "vue-router";
const props = defineProps<{
  modelValue: boolean;
  name: string;
}>();
const formRef = ref();
const emit = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(val: boolean) {
    emit("update:modelValue", val);
  }
});
const btnLoading = ref(false);
const form = reactive({
  name: `${props.name}:${props.name}`,
  //   bounds: {
  //     coords: {}
  //   },
  gridSetId: "",
  zoomStart: 0,
  zoomStop: 15,
  type: "seed",
  threadCount: 0,
  format: ""
  //   parameters: {
  //     entry: {
  //       string: ""
  //     }
  //   }
});
const zoomChecked = (rule: any, value: any, callback: any) => {
  if (form.zoomStart > form.zoomStop) {
    callback(new Error("最小层级不能大于最大层级"));
  } else {
    callback();
  }
};
const rules = reactive<FormRules>({
  zoomStart: [{ required: true, validator: zoomChecked, trigger: "change" }],
  zoomStop: [{ required: true, validator: zoomChecked, trigger: "change" }],
  gridSetId: [{ required: true, message: "请选择管线图层", trigger: "change" }],
  format: [{ required: true, message: "请选择管线编码", trigger: "change" }]
});
const types = [
  {
    name: "生成所有切片",
    value: "reseed"
  },
  {
    name: "生成缺失切片",
    value: "seed"
  }
];
const serviceInfo = ref({
  gridSets: [] as string[],
  formats: [] as string[]
});
const getServiceInfo = async () => {
  const data = await getWmtsInfo(props.name);
  serviceInfo.value = data;
  form.format = data.formats[0];
  form.gridSetId = data.gridSets[0];
};
getServiceInfo();
const cancel = () => {
  value.value = false;
};
const router = useRouter();
const submit = async () => {
  btnLoading.value = true;
  try {
    const res = await new Promise((resolve) => {
      formRef.value.validate((vaild: boolean) => {
        resolve(vaild);
      });
    });
    if (res) {
      const res = await gwcSeed(form);
      ElMessage.success("操作成功");
      router.push({
        name: "taskRunning"
      });
    }
  } finally {
    btnLoading.value = false;
  }
};
</script>
