<!--
 * @Description: EPSG坐标系统查询
 * @Date: 2023-02-06 11:42:26
 * @Author: G<PERSON>er<PERSON>
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-12-22 15:20:48
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">坐标系统查询</div>
    </div>
    <div class="custom-table-box" style="height: 600px">
      <div class="coordinate-system-container">
        <!-- 查询输入区域 -->
        <div class="search-section">
          <div class="search-input-group">
            <div class="input-wrapper">
              <label>EPSG代码查询</label>
              <div class="input-container">
                <input
                  v-model="searchQuery"
                  type="text"
                  class="epsg-input"
                  placeholder="输入EPSG代码或坐标系名称，如：4326、WGS84"
                  @input="handleSearch"
                  @keyup.enter="handleQuickSearch"
                />
                <div class="input-actions">
                  <button
                    @click="handleQuickSearch"
                    :disabled="!searchQuery.trim() || isLoading"
                    class="search-btn"
                  >
                    <span v-if="isLoading">查询中...</span>
                    <span v-else>搜索</span>
                  </button>
                  <button @click="clearSearch" class="clear-btn">清空</button>
                </div>
              </div>
            </div>

            <!-- 自动补全建议 -->
            <div v-if="suggestions.length > 0 && showSuggestions" class="suggestions-dropdown">
              <div class="suggestions-header">搜索建议</div>
              <div
                v-for="suggestion in suggestions"
                :key="suggestion.code"
                class="suggestion-item"
                @click="selectSuggestion(suggestion)"
              >
                <span class="epsg-code">EPSG:{{ suggestion.code }}</span>
                <span class="epsg-name">{{ suggestion.name }}</span>
              </div>
            </div>
          </div>

          <!-- 常用坐标系快捷选择 -->
          <div class="quick-select">
            <label>常用坐标系</label>
            <div class="quick-buttons">
              <button
                v-for="common in commonEpsgCodes"
                :key="common.code"
                class="quick-btn"
                @click="selectCommonEpsg(common)"
                :disabled="isLoading"
              >
                {{ common.code }} - {{ common.name }}
              </button>
            </div>
          </div>
        </div>

        <!-- 查询结果区域 -->
        <div class="result-section">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-state">
            <div class="loading-spinner"></div>
            <span>正在查询坐标系详细信息...</span>
          </div>

          <!-- 查询结果 -->
          <div v-else-if="currentResult" class="result-content">
            <div class="result-header">
              <h3>{{ currentResult.name }}</h3>
              <div class="result-actions">
                <button @click="copyEpsgCode" class="copy-btn">复制EPSG代码</button>
              </div>
            </div>

            <div class="result-details">
              <!-- 基本信息 -->
              <div class="info-section">
                <h4>基本信息</h4>
                <div class="params-table">
                  <div class="param-row">
                    <span class="param-name">EPSG代码：</span>
                    <span class="param-value">{{
                      currentResult.id?.code || currentResult.code
                    }}</span>
                  </div>
                  <div class="param-row">
                    <span class="param-name">类型：</span>
                    <span class="param-value">{{
                      getCoordinateSystemType(currentResult.type)
                    }}</span>
                  </div>
                  <div class="param-row" v-if="currentResult.coordinate_system">
                    <span class="param-name">单位：</span>
                    <span class="param-value">{{
                      getCoordinateUnit(currentResult.coordinate_system)
                    }}</span>
                  </div>
                  <div class="param-row" v-if="currentResult.area">
                    <span class="param-name">适用区域：</span>
                    <span class="param-value">{{ currentResult.area }}</span>
                  </div>
                  <div class="param-row" v-if="currentResult.scope">
                    <span class="param-name">应用范围：</span>
                    <span class="param-value">{{ currentResult.scope }}</span>
                  </div>
                </div>
              </div>

              <!-- 地理范围 -->
              <div class="bounds-section" v-if="currentResult.bbox">
                <h4>地理范围</h4>
                <div class="bounds-grid">
                  <div class="bounds-item">
                    <label>西边界：</label>
                    <span>{{ currentResult.bbox.west_longitude }}°</span>
                  </div>
                  <div class="bounds-item">
                    <label>东边界：</label>
                    <span>{{ currentResult.bbox.east_longitude }}°</span>
                  </div>
                  <div class="bounds-item">
                    <label>南边界：</label>
                    <span>{{ currentResult.bbox.south_latitude }}°</span>
                  </div>
                  <div class="bounds-item">
                    <label>北边界：</label>
                    <span>{{ currentResult.bbox.north_latitude }}°</span>
                  </div>
                </div>
              </div>

              <!-- 基准面信息 -->
              <div
                class="datum-section"
                v-if="currentResult.base_crs?.datum || currentResult.datum"
              >
                <h4>基准面信息</h4>
                <div class="params-table">
                  <div class="param-row" v-if="getDatumInfo(currentResult).name">
                    <span class="param-name">基准面：</span>
                    <span class="param-value">{{ getDatumInfo(currentResult).name }}</span>
                  </div>
                  <div class="param-row" v-if="getDatumInfo(currentResult).ellipsoid">
                    <span class="param-name">椭球体：</span>
                    <span class="param-value">{{
                      getDatumInfo(currentResult).ellipsoid.name
                    }}</span>
                  </div>
                  <div class="param-row" v-if="getDatumInfo(currentResult).ellipsoid">
                    <span class="param-name">长半轴：</span>
                    <span class="param-value"
                      >{{ getDatumInfo(currentResult).ellipsoid.semi_major_axis }} m</span
                    >
                  </div>
                  <div class="param-row" v-if="getDatumInfo(currentResult).ellipsoid">
                    <span class="param-name">扁率倒数：</span>
                    <span class="param-value">{{
                      getDatumInfo(currentResult).ellipsoid.inverse_flattening
                    }}</span>
                  </div>
                </div>
              </div>

              <!-- 投影参数 -->
              <div class="projection-section" v-if="currentResult.conversion">
                <h4>投影参数</h4>
                <div class="params-table">
                  <div class="param-row" v-if="currentResult.conversion.method">
                    <span class="param-name">投影方法：</span>
                    <span class="param-value">{{ currentResult.conversion.method.name }}</span>
                  </div>
                  <div
                    v-for="param in currentResult.conversion.parameters"
                    :key="param.name"
                    class="param-row"
                  >
                    <span class="param-name">{{ param.name }}：</span>
                    <span class="param-value">{{ param.value }} {{ param.unit }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="errorMessage" class="error-state">
            <div class="error-icon">❌</div>
            <h3>查询出错</h3>
            <p>{{ errorMessage }}</p>
            <button @click="clearError" class="retry-btn">重试</button>
          </div>

          <!-- 空状态 -->
          <div v-else-if="hasSearched && !currentResult" class="empty-state">
            <div class="empty-icon">🔍</div>
            <h3>未找到匹配的坐标系</h3>
            <p>请检查EPSG代码是否正确，或尝试使用坐标系名称搜索</p>
          </div>

          <!-- 初始状态 -->
          <div v-else class="initial-state">
            <div class="initial-icon">🌍</div>
            <h3>欢迎使用坐标系统查询</h3>
            <p>输入EPSG代码或选择常用坐标系开始查询</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";

/**
 * @interface CrsListItem
 * @description 坐标系列表项接口（来自本地JSON文件）
 */
interface CrsListItem {
  code: string;
  name: string;
  kind: string;
  deprecated?: boolean;
}

/**
 * @interface ProjJsonResponse
 * @description PROJ JSON API响应接口（来自spatialreference.org）
 */
interface ProjJsonResponse {
  $schema?: string;
  type: string;
  name: string;
  id: {
    authority: string;
    code: number;
  };
  code?: string;
  base_crs?: {
    type: string;
    name: string;
    datum: {
      type: string;
      name: string;
      ellipsoid: {
        name: string;
        semi_major_axis: number;
        inverse_flattening: number;
      };
    };
  };
  datum?: {
    type: string;
    name: string;
    ellipsoid: {
      name: string;
      semi_major_axis: number;
      inverse_flattening: number;
    };
  };
  conversion?: {
    name: string;
    method: {
      name: string;
      id: {
        authority: string;
        code: number;
      };
    };
    parameters: Array<{
      name: string;
      value: number;
      unit: string;
      id: {
        authority: string;
        code: number;
      };
    }>;
  };
  coordinate_system: {
    subtype: string;
    axis: Array<{
      name: string;
      abbreviation: string;
      direction: string;
      unit: string;
    }>;
  };
  scope: string;
  area: string;
  bbox: {
    south_latitude: number;
    west_longitude: number;
    north_latitude: number;
    east_longitude: number;
  };
}

/**
 * 响应式数据
 */
const searchQuery = ref<string>("");
const currentResult = ref<ProjJsonResponse | null>(null);
const isLoading = ref<boolean>(false);
const hasSearched = ref<boolean>(false);
const showSuggestions = ref<boolean>(false);
const errorMessage = ref<string>("");
const crsList = ref<CrsListItem[]>([]);

/**
 * 常用坐标系（从本地数据中预定义）
 */
const commonEpsgCodes = ref<CrsListItem[]>([
  { code: "4326", name: "WGS 84", kind: "CRS-GEOGCRS" },
  { code: "3857", name: "WGS 84 / Pseudo-Mercator", kind: "CRS-PROJCRS" },
  { code: "4490", name: "China Geodetic Coordinate System 2000", kind: "CRS-GEOGCRS" },
  { code: "4549", name: "CGCS2000 / 3-degree Gauss-Kruger zone 38", kind: "CRS-PROJCRS" },
  { code: "2437", name: "Beijing 1954 / 3-degree Gauss-Kruger zone 38", kind: "CRS-PROJCRS" }
]);

/**
 * 计算属性 - 搜索建议
 */
const suggestions = computed(() => {
  if (!searchQuery.value.trim() || searchQuery.value.length < 2) {
    return [];
  }

  const query = searchQuery.value.toLowerCase();
  return crsList.value
    .filter((item) => item.code.includes(query) || item.name.toLowerCase().includes(query))
    .slice(0, 8);
});

/**
 * 组件挂载时加载本地坐标系列表
 */
onMounted(async () => {
  try {
    const response = await fetch("/data/crslist.json");
    if (response.ok) {
      crsList.value = await response.json();
    } else {
      console.warn("无法加载本地坐标系列表，使用默认数据");
    }
  } catch (error) {
    console.error("加载坐标系列表失败:", error);
  }
});

/**
 * 处理搜索输入
 */
function handleSearch(): void {
  showSuggestions.value = true;
  errorMessage.value = "";
}

/**
 * 执行快速搜索
 */
async function handleQuickSearch(): Promise<void> {
  if (!searchQuery.value.trim()) return;

  isLoading.value = true;
  hasSearched.value = true;
  showSuggestions.value = false;
  errorMessage.value = "";

  try {
    await nextTick();

    const query = searchQuery.value.trim();
    let epsgCode: string = "";

    // 尝试EPSG代码查询
    const numericCode = parseInt(query);
    if (!isNaN(numericCode)) {
      epsgCode = numericCode.toString();
    } else {
      // 尝试名称查询
      const foundItem = crsList.value.find((item) =>
        item.name.toLowerCase().includes(query.toLowerCase())
      );
      if (foundItem) {
        epsgCode = foundItem.code;
      }
    }

    if (epsgCode) {
      await fetchEpsgDetails(epsgCode);
    } else {
      currentResult.value = null;
      errorMessage.value = "未找到匹配的坐标系，请检查输入";
    }
  } catch (error) {
    console.error("查询失败:", error);
    errorMessage.value = "查询过程中发生错误，请稍后重试";
    currentResult.value = null;
  } finally {
    isLoading.value = false;
  }
}

/**
 * 从spatialreference.org获取坐标系详细信息
 */
async function fetchEpsgDetails(epsgCode: string): Promise<void> {
  try {
    const apiUrl = `https://spatialreference.org/ref/epsg/${epsgCode}/projjson.json`;
    const response = await fetch(apiUrl);

    if (response.ok) {
      const data: ProjJsonResponse = await response.json();
      data.code = epsgCode; // 添加代码字段用于显示
      currentResult.value = data;
      isLoading.value = false;
    } else {
      throw new Error(`API返回状态: ${response.status}`);
    }
  } catch (error) {
    console.error("获取坐标系详情失败:", error);
    errorMessage.value = "无法获取坐标系详细信息，请检查网络连接或稍后重试";
    currentResult.value = null;
  }
}

/**
 * 选择搜索建议
 */
function selectSuggestion(suggestion: CrsListItem): void {
  searchQuery.value = suggestion.code;
  showSuggestions.value = false;
  fetchEpsgDetails(suggestion.code);
  hasSearched.value = true;
  isLoading.value = true;
}

/**
 * 选择常用EPSG代码
 */
function selectCommonEpsg(epsg: CrsListItem): void {
  searchQuery.value = epsg.code;
  showSuggestions.value = false;
  fetchEpsgDetails(epsg.code);
  hasSearched.value = true;
  isLoading.value = true;
}

/**
 * 清空搜索
 */
function clearSearch(): void {
  searchQuery.value = "";
  currentResult.value = null;
  hasSearched.value = false;
  showSuggestions.value = false;
  errorMessage.value = "";
}

/**
 * 清除错误状态
 */
function clearError(): void {
  errorMessage.value = "";
  hasSearched.value = false;
}

/**
 * 复制EPSG代码
 */
async function copyEpsgCode(): Promise<void> {
  if (!currentResult.value) return;

  try {
    const code = currentResult.value.id?.code || currentResult.value.code;
    await navigator.clipboard.writeText(`EPSG:${code}`);
    // 这里可以添加成功提示
  } catch (error) {
    console.error("复制失败:", error);
  }
}

/**
 * 获取坐标系类型的中文描述
 */
function getCoordinateSystemType(type: string): string {
  const typeMap: Record<string, string> = {
    GeographicCRS: "地理坐标系",
    ProjectedCRS: "投影坐标系",
    GeodeticCRS: "大地坐标系",
    VerticalCRS: "垂直坐标系",
    CompoundCRS: "复合坐标系"
  };
  return typeMap[type] || type;
}

/**
 * 获取坐标单位
 */
function getCoordinateUnit(coordinateSystem: any): string {
  if (!coordinateSystem?.axis?.length) return "未知";
  return coordinateSystem.axis[0]?.unit || "未知";
}

/**
 * 获取基准面信息
 */
function getDatumInfo(result: ProjJsonResponse): any {
  return result.base_crs?.datum || result.datum || {};
}

/**
 * 监听搜索查询变化
 */
watch(searchQuery, () => {
  if (!searchQuery.value.trim()) {
    showSuggestions.value = false;
    errorMessage.value = "";
  }
});
</script>

<style lang="scss" scoped>
.coordinate-system-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;

  .search-section {
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-input-group {
      position: relative;
      margin-bottom: 20px;

      .input-wrapper {
        label {
          display: block;
          font-size: 14px;
          color: #333;
          font-weight: 600;
          margin-bottom: 8px;
        }

        .input-container {
          display: flex;
          gap: 12px;

          .epsg-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;

            &:focus {
              outline: none;
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }

          .input-actions {
            display: flex;
            gap: 8px;

            .search-btn,
            .clear-btn {
              padding: 12px 20px;
              border: none;
              border-radius: 4px;
              font-size: 14px;
              cursor: pointer;
              transition: all 0.3s;
              min-width: 80px;
            }

            .search-btn {
              background: #409eff;
              color: white;

              &:hover:not(:disabled) {
                background: #66b1ff;
              }

              &:disabled {
                background: #c0c4cc;
                cursor: not-allowed;
              }
            }

            .clear-btn {
              background: #f56c6c;
              color: white;

              &:hover {
                background: #f78989;
              }
            }
          }
        }
      }

      .suggestions-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 100px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;

        .suggestions-header {
          padding: 8px 16px;
          background: #f5f7fa;
          font-size: 12px;
          color: #666;
          border-bottom: 1px solid #eee;
        }

        .suggestion-item {
          padding: 12px 16px;
          display: flex;
          justify-content: space-between;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background: #f5f7fa;
          }

          .epsg-code {
            font-weight: 600;
            color: #409eff;
          }

          .epsg-name {
            color: #666;
            font-size: 12px;
            flex: 1;
            margin-left: 12px;
            text-align: right;
          }
        }
      }
    }

    .quick-select {
      label {
        display: block;
        font-size: 14px;
        color: #333;
        font-weight: 600;
        margin-bottom: 12px;
      }

      .quick-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .quick-btn {
          padding: 8px 12px;
          background: #e6f7ff;
          color: #1890ff;
          border: 1px solid #91d5ff;
          border-radius: 4px;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover:not(:disabled) {
            background: #bae7ff;
            border-color: #69c0ff;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  .result-section {
    flex: 1;
    background: white;
    padding: 20px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow-y: auto;

    .loading-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #666;

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 16px;
      }
    }

    .result-content {
      .result-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #e9ecef;

        h3 {
          margin: 0;
          color: #333;
          font-size: 18px;
        }

        .result-actions {
          .copy-btn {
            padding: 6px 12px;
            background: #67c23a;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: #85ce61;
            }
          }
        }
      }

      .result-details {
        .info-section,
        .bounds-section,
        .datum-section,
        .projection-section {
          margin-bottom: 24px;

          h4 {
            margin: 0 0 12px 0;
            color: #333;
            font-size: 14px;
            font-weight: 600;
          }
        }

        .info-grid,
        .bounds-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;

          .info-item,
          .bounds-item {
            display: flex;
            align-items: center;

            label {
              font-weight: 600;
              color: #666;
              min-width: 80px;
            }

            span {
              color: #333;
            }
          }
        }

        .params-table {
          background: #f8f9fa;
          border-radius: 4px;
          padding: 16px;

          .param-row {
            display: flex;
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }

            .param-name {
              font-weight: 600;
              color: #666;
              min-width: 120px;
            }

            .param-value {
              color: #333;
            }
          }
        }
      }
    }

    .error-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      text-align: center;
      color: #666;

      .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #f56c6c;
      }

      p {
        margin: 0 0 16px 0;
        color: #999;
      }

      .retry-btn {
        padding: 8px 16px;
        background: #409eff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          background: #66b1ff;
        }
      }
    }

    .empty-state,
    .initial-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      text-align: center;
      color: #666;

      .empty-icon,
      .initial-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 8px 0;
        color: #333;
      }

      p {
        margin: 0;
        color: #999;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .coordinate-system-container {
    padding: 16px;
    gap: 16px;

    .search-section,
    .result-section {
      padding: 16px;
    }

    .search-section {
      .search-input-group .input-wrapper .input-container {
        flex-direction: column;
        gap: 8px;

        .input-actions {
          justify-content: stretch;

          .search-btn,
          .clear-btn {
            flex: 1;
          }
        }
      }

      .quick-select .quick-buttons {
        .quick-btn {
          flex: 1;
          min-width: 0;
          text-align: center;
        }
      }
    }

    .result-section {
      .result-content .result-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
      }

      .result-details {
        .info-grid,
        .bounds-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
