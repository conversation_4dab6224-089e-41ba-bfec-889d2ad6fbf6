/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-06-29 18:17:27
 * @LastEditors: silei
 * @LastEditTime: 2023-06-29 18:18:33
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
export const mapDownloadPost = async (data: any) => {
  const result: any = await hRequest.post<DataType>({
    url: "/map/download",
    maxRedirects: 0,
    timeout: 1000000,
    data
  });
  return result;
};

export const getLayers = async (serviceName: string) => {
  const result = await hRequest.get<DataType>({
    url: "/serviceManage/layers",
    params: {
      serviceName
    }
  });
  return result;
};

export const getWmtsInfo = async (serviceName: string) => {
  const result = await hRequest.get<any>({
    url: `/serviceManage/serviceInfo/wmtsinfo/${serviceName}`
  });
  return result;
};

export const gwcSeed = async (data: any) => {
  const { name, ...body } = data;
  const result = await hRequest.post<any>({
    url: `../gwc/rest/seed/${name as string}.json`,
    data: { seedRequest: body }
  });
  return result;
};
export const getGwcList = async (serviceName: string) => {
  const result = await hRequest.get<any>({
    url: `/serviceManage/serviceInfo/running`,
    params: {
      serviceName
    }
  });
  return result;
};

export const cancelTask = async (serviceName: string) => {
  const result = await hRequest.put<any>({
    url: `/serviceManage/serviceInfo/cancelTask`,
    params: {
      serviceName
    }
  });
  return result;
};
