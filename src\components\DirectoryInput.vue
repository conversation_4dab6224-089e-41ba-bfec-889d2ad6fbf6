<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-04-21 15:23:50
 * @LastEditors: silei
 * @LastEditTime: 2023-10-25 15:38:38
-->
<template>
  <div>
    <div class="select" v-if="menuShow" @click="menuClick">
      <el-input></el-input>
      <div class="upFileBox">
        <div ref="myRef" class="content">
          <el-row
            v-for="(item, index) in menuList"
            class="menu-item"
            @click.stop="handlePathChanged(item)"
            :key="index"
          >
            <div class="menu-item-label">{{ item.label }}</div>
            >
          </el-row>
        </div>
      </div>
    </div>
    <el-input
      v-if="!menuShow"
      ref="inputRef"
      v-model="value"
      @change="valueChanged"
      @blur="menuShow = true"
    />
  </div>
</template>
<script lang="ts" setup>
import { computed, nextTick, onMounted, ref } from "vue";
import elementResizeDetector from "element-resize-detector";
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  }
});
const emits = defineEmits(["update:modelValue", "change"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
const myRef = ref<HTMLElement>();
const overWidth = ref(false);
const menuList = computed(() => {
  const splitter = value.value.includes("/") ? "/" : "\\";
  // 处理linux系统问题

  const isLinux = value.value.startsWith("/");
  let menuValue = value.value;
  if (isLinux) {
    menuValue = value.value.substring(1);
  }
  const list = menuValue.split(splitter);
  const result = isLinux
    ? [
        {
          label: "/",
          value: "/"
        }
      ]
    : [];
  for (let i = 0; i < list.length; i++) {
    if (!list[i]) {
      break;
    }
    const label = list[i];
    let valueTem: string =
      i === 0 ? list[i] : `${result[result.length - 1].value}${splitter}${list[i]}`;
    if (isLinux && i === 0) {
      valueTem = splitter + valueTem;
    }
    result.push({
      label,
      value: valueTem
    });
  }
  if (overWidth.value) {
    result.splice(0, result.length - 2);
  }
  return result;
});

const menuShow = ref(true);
const inputRef = ref<HTMLInputElement>();
const menuClick = () => {
  menuShow.value = false;
  nextTick(() => {
    if (inputRef.value) {
      inputRef.value.focus();
    }
  });
};
const valueChanged = () => {
  menuShow.value = false;
  emits("change", value.value);
};
const handlePathChanged = (item: any) => {
  if (item.value && !item.value.includes("/") && !item.value.includes("\\")) {
    value.value = item.value + "\\";
  } else {
    value.value = item.value;
  }
  emits("change", value.value);
};
onMounted(() => {
  elementResizeDetector().listenTo(myRef.value!, (ele: any) => {
    if (ele.clientWidth > 250) {
      overWidth.value = true;
    }
  });
});
</script>
<style lang="scss" scoped>
.select {
  position: relative;
  .upFileBox {
    top: 5px;
    left: 14px;
    position: absolute;
    .content {
      height: 100%;
      width: 100%;
      overflow: hidden;
      display: flex;
    }
    .menu-item {
      margin-left: 5px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      color: #232a3a;

      .menu-item-label {
        max-width: 150px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .menu-item:hover {
      cursor: pointer;
      border-bottom: 1px solid;
    }
  }
}
</style>
