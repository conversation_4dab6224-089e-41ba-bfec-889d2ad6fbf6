<!--
 * @Description: 
 * @Date: 2023-03-03 10:01:40
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2024-01-02 14:52:51
-->
import { ref } from "vue"; import { ref } from "vue";

<!--
 * @Description: 
 * @Date: 2023-02-06 11:42:26
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-06 10:40:23
-->
<template>
  <div class="home-page">
    <div class="content">
      <div class="content-operate">
        <div class="operate-left">
          <div class="fast-card">
            <div class="card" v-for="(item, index) in fastCardList" :key="index">
              <div class="card-left" :style="{ background: item.color }">
                <img :src="item.icon" alt="" />
              </div>
              <div class="card-right">
                <div class="title">{{ item.title }}</div>
                <div class="content">{{ item.content }}</div>
              </div>
              <div class="card-foot" @click="linkTo(item)">
                <div class="foot-text" :style="{ color: item.color }">{{ item.doText }}</div>
                <img :src="item.doIcon" alt="" />
              </div>
            </div>
          </div>
          <div class="server">
            <div class="myChartSever-title">
              <div class="left">服务器当前性能</div>
              <div class="right">
                <img src="../../assets/img/severCount.png" alt="" />{{ serverVerformanceCount }}
                请求数/秒
              </div>
            </div>
            <div ref="myChartSever" class="broken-line-box"></div>
          </div>
          <div class="server-total">
            <div class="myChartSever-title">
              <div class="left">服务器访问统计</div>
            </div>
            <div class="server-total-box">
              <div ref="serverExample" class="pie-chart-box"></div>
              <div ref="serverType" class="pie-chart-box"></div>
              <div ref="userTotal" class="pie-chart-box"></div>
            </div>
          </div>
        </div>
        <div class="operate-right">
          <MineCard></MineCard>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="创建服务"
      width="40%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">创建服务</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <ServiceCreateResult v-model="serviceResult" />
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-submit" @click="createCompleted">确定</div>
        </span>
      </template>
    </el-dialog>
    <el-upload
      ref="uploadRef"
      style="display: none"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      :show-file-list="false"
      accept=".json"
      :on-change="handleUpload"
      :auto-upload="false"
    >
      <template #trigger>
        <button ref="refCreate"></button>
      </template>
    </el-upload>
  </div>
</template>
<script lang="ts" setup>
import router from "@/router";
import store from "@/store";
import { ref, onMounted, onBeforeUnmount } from "vue";
import MineCard from "@/components/MineCard.vue";
import * as echarts from "echarts";
import { getMonitorRequest } from "@/api/homePage";
import moment from "moment";
import { ElMessage, UploadFile } from "element-plus";
import { publishQuickly } from "@/api/resource";
import ServiceCreateResult from "@/components/ServiceCreateResult.vue";
import { useRoute } from "vue-router";
import localCatch from "@/utils/auth";
import { getCurrentUser } from "@/api/user";
import { getRoleUser } from "@/api/security/role";
const myChartSever = ref();
const serverVerformanceCount = ref(0);
const dialogVisible = ref(false);
const refCreate = ref();
const route = useRoute();
const setUserRole = async (username: string) => {
  const temPromise: any = await getRoleUser(username);
  const roles: string[] = temPromise.roles;
  localCatch.setCache("userRoles", roles);
};
const createService = () => {
  // dialogVisible.value = true;
  refCreate.value.click();
};
const serviceResult = ref();
const handleUpload = async (uploadFile: UploadFile) => {
  if (!uploadFile.name.endsWith("json")) {
    ElMessage({
      message: "请选择json配置文件",
      type: "warning"
    });
    return;
  }
  const data = await uploadFile.raw?.text();
  const config = JSON.parse(data!);
  serviceResult.value = await publishQuickly(config);
  dialogVisible.value = true;
};
const createCompleted = () => {
  dialogVisible.value = false;
  router.push({ name: fastCardList[0].rePath });
  store.commit("basic/changeAvtiveMenu", fastCardList[0].rePath);
};
const fastCardList = [
  {
    color: "#4076F3",
    icon: require("../../assets/img/server-icon.png"),
    title: "快速发布服务",
    content: "通过快速发布服务向导，您可以将已有数据发布为标准服务",
    doText: "去发布",
    doIcon: require("../../assets/img/server-do.png"),
    rePath: "serviceManage"
  },
  {
    color: "#46BB95",
    icon: require("../../assets/img/style-icon.png"),
    title: "风格配置",
    content: "您可以管理风格信息，指定矢量数据集所使用的风格",
    doText: "去配置",
    doIcon: require("../../assets/img/style-do.png"),
    path: "serviceStyleManage"
  }
];
const linkTo = (row: any) => {
  if (row.path) {
    router.push({ name: row.path });
    store.commit("basic/changeAvtiveMenu", row.path);
  } else {
    createService();
  }
};
const myChartSeverColony: any = ref();
const serverVerformance = () => {
  const myChartSeverColony1 = echarts.init(myChartSever.value);
  let option: any = {};
  const data: any = [];

  const oneDay = 1000;
  let value = Math.random() * 1000;
  let now = new Date();
  let afterNow = new Date();
  const randomData = () => {
    getEcharsDataPerSecond();
    const timeStr = moment().subtract(2, "second").format("HH:mm:ss");
    afterNow = new Date(+afterNow + oneDay);
    value = echartsObj.value[timeStr] ? echartsObj.value[timeStr] : 0;
    return {
      name: afterNow.toString(),
      value: [afterNow, value]
    };
  };
  const dataInit = () => {
    now = new Date(+now - oneDay);
    const temData = moment(now);
    const timeStr = temData.format("HH:mm:ss");
    value = echartsObj.value[timeStr.toString()] ? echartsObj.value[timeStr.toString()] : 0;
    return {
      name: now.toString(),
      value: [now, value]
    };
  };
  for (let i = 0; i < 300; i++) {
    data.unshift(dataInit());
  }
  option = {
    tooltip: {
      trigger: "axis",
      formatter: function (params: any) {
        params = params[0];
        const date = new Date(params.name);
        return date.getHours() + ":" + date.getMinutes() + "\n" + params.value[1] + "请求数/秒";
      },
      extraCssText:
        "width:90px;white-space:pre-wrap;background:#4076f3;border-radius: 4px;color:white",
      axisPointer: {
        type: "shadow",
        label: { show: true, backgroundColor: "transparent" },
        shadowStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: "rgba(100, 101, 171, 0)" },
              { offset: 0.5, color: "rgba(100, 101, 171, 0.2)" },
              { offset: 0.999999, color: "rgba(100, 101, 171, 1)" },
              { offset: 1, color: "rgba(100, 101, 171, 1)" }
            ],
            global: false
          }
        }
      }
    },

    xAxis: {
      type: "time",
      splitLine: {
        show: false
      },
      axisTick: { show: false },
      axisLabel: {
        textStyle: {
          color: "#232A3A",
          fontSize: 14
        }
      }
    },
    yAxis: {
      type: "value",
      boundaryGap: [0, "100%"],
      splitLine: {
        show: true
      },
      axisLabel: {
        // y轴文字的配置
        textStyle: {
          color: "#CDD3DC"
        }
      }
    },
    grid: {
      show: false,
      top: "18%",
      right: "4%",
      bottom: "20%",
      left: "5%"
    },
    series: [
      {
        name: "data",
        type: "line",
        showSymbol: false,
        data,
        lineStyle: {
          // 阴影部分
          shadowOffsetX: 0, // 折线的X偏移
          shadowOffsetY: 9, // 折线的Y偏移
          shadowBlur: 8, // 折线模糊
          shadowColor: "rgba(145, 132, 132, 1)" // 折线颜色
        },
        itemStyle: {
          normal: {
            lineStyle: {
              width: 4, // 折线宽度
              // 折线颜色渐变
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: "#af45ff"
                },
                {
                  offset: 1,
                  color: "#6493ff"
                }
              ])
            }
          }
        }
      }
    ]
  };
  const timer1: any = setInterval(function () {
    data.shift();
    data.push(randomData());
    myChartSeverColony1.setOption({
      series: [
        {
          data
        }
      ]
    });
  }, 1000);
  option && myChartSeverColony1.setOption(option);
  return {
    time1: timer1,
    myChartSeverColony: myChartSeverColony1
  };
};
const serverExample = ref();
const serverExampleinit = () => {
  // 基于准备好的dom，初始化echarts实例
  const myChart = echarts.init(serverExample.value);
  // 指定图表的配置项和数据
  const option = {
    title: {
      subtext: "服务\n实例",
      x: "center",
      y: "31%",
      textStyle: {
        fontSize: 18,
        fontWeight: "normal",
        color: ["#232a3a"]
      },
      subtextStyle: {
        color: "#232a3a",
        fontSize: 24,
        fontWeight: 400,
        fontFamily: "Source Han Sans CN, Source Han Sans CN-Regular"
      }
    },
    grid: {
      bottom: 100,
      left: 0,
      right: "10%"
    },
    legend: {
      show: false,
      orient: "vertical",
      top: "middle",
      right: "5%",
      textStyle: {
        color: "#f2f2f2",
        fontSize: 25
      },
      icon: "roundRect"
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)"
    },
    series: [
      {
        type: "pie",
        radius: ["33%", "61%"],
        center: ["50%", "50%"],
        data: [{ value: 335, name: "" }],
        labelLine: {
          show: false
        },
        label: {
          normal: {}
        }
      }
    ]
  };
  const temData = [];
  serverExampleData = sortUp(serverExampleData);
  if (temServiceLength.value > 5) {
    let num = 0;
    let otherTotal: any = 0;
    for (const item in serverExampleData) {
      num++;
      if (num < 5) {
        temData.push({
          name: item,
          value: serverExampleData[item]
        });
      } else {
        otherTotal += serverExampleData[item];
      }
    }
    temData.push({
      name: "其他服务",
      value: otherTotal
    });
  } else {
    for (const item in serverExampleData) {
      console.log(item, "777778888");
      temData.push({
        name: item,
        value: serverExampleData[item]
      });
    }
  }
  // 赋值
  option.series = [
    {
      type: "pie",
      radius: ["45%", "80%"],
      center: ["50%", "50%"],
      data: temData,
      labelLine: {
        show: false
      },
      label: {
        normal: {
          position: "inner",
          formatter: function (param: { percent: number }) {
            if (!param.percent) return "";
            const f = Math.round(param.percent * 10) / 10;
            let s = f.toString();
            let rs = s.indexOf(".");
            if (rs < 0) {
              rs = s.length;
              s += ".";
            }
            while (s.length <= rs + 1) {
              s += "0";
            }
            return s + "%";
          },
          textStyle: {
            color: "#fff",
            fontFamily: "AlibabaSans-Regular",
            fontSize: 8
          }
        }
      }
    }
  ];
  myChart.setOption(option);
  const timer = setInterval(function () {
    const temData: any = [];
    if (temServiceLength.value > 5) {
      let num = 0;
      let otherTotal: any = 0;
      for (const item in serverExampleData) {
        num++;
        if (num < 5) {
          temData.push({
            name: item,
            value: serverExampleData[item]
          });
        } else {
          otherTotal += serverExampleData[item];
        }
      }
      temData.push({
        name: "其他服务",
        value: otherTotal
      });
    } else {
      for (const item in serverExampleData) {
        temData.push({
          name: item,
          value: serverExampleData[item]
        });
      }
    }
    myChart.setOption({
      series: [
        {
          data: temData
        }
      ]
    });
  }, 10000);
  return timer;
};
const sortUp = (dict: any) => {
  console.log(dict, "-----");
  const sortValues: any = Object.values(dict).sort((a: any, b: any) => b - a);
  const sortDict: any = {};
  for (const i of sortValues) {
    for (const key in dict) {
      //  console.log(i,key)
      if (dict[key] === i) {
        sortDict[key] = i;
        delete dict[key];
      }
    }
  }
  console.log(sortDict, "sortDict");
  return sortDict;
};
const serverType = ref();
const serverTypeinit = () => {
  // 基于准备好的dom，初始化echarts实例
  const myChart = echarts.init(serverType.value);
  const temData = [];
  for (const item in serverTypeData) {
    temData.push({
      name: item,
      value: serverTypeData[item]
    });
  }
  // 指定图表的配置项和数据
  const option = {
    title: {
      subtext: "服务\n类型",
      x: "center",
      y: "31%",
      textStyle: {
        fontSize: 18,
        fontWeight: "normal",
        color: ["#232a3a"]
      },
      subtextStyle: {
        color: "#232a3a",
        fontSize: 24,
        fontWeight: 400,
        fontFamily: "Source Han Sans CN, Source Han Sans CN-Regular"
      }
    },
    grid: {
      bottom: 100,
      left: 0,
      right: "10%"
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)"
    },
    series: [
      {
        type: "pie",
        radius: ["33%", "61%"],
        center: ["50%", "50%"],
        data: [{ value: 335, name: "" }],
        labelLine: {
          show: false
        },
        label: {
          normal: {}
        }
      }
    ]
  };
  // 赋值
  option.series = [
    {
      type: "pie",
      radius: ["45%", "80%"],
      center: ["50%", "50%"],
      data: temData,
      labelLine: {
        show: false
      },
      label: {
        normal: {
          position: "inner",
          formatter: function (param: { percent: number }) {
            if (!param.percent) return "";
            const f = Math.round(param.percent * 10) / 10;
            let s = f.toString();
            let rs = s.indexOf(".");
            if (rs < 0) {
              rs = s.length;
              s += ".";
            }
            while (s.length <= rs + 1) {
              s += "0";
            }
            return s + "%";
          },
          textStyle: {
            color: "#fff",
            fontFamily: "AlibabaSans-Regular",
            fontSize: 8
          }
        }
      }
    }
  ];
  // 使用刚指定的配置项和数据显示图表。
  option && myChart.setOption(option);
  const timer = setInterval(function () {
    const temData: any = [];
    for (const item in serverTypeData) {
      temData.push({
        name: item,
        value: serverTypeData[item]
      });
    }
    myChart.setOption({
      series: [
        {
          data: temData
        }
      ]
    });
  }, 10000);
  return timer;
};
const userTotal = ref();
const userTotalinit = () => {
  // 基于准备好的dom，初始化echarts实例
  const myChart = echarts.init(userTotal.value);
  const temData = [];
  for (const item in userTotalData) {
    temData.push({
      name: item,
      value: userTotalData[item]
    });
  }
  // 指定图表的配置项和数据
  const option = {
    title: {
      subtext: "用户\n访问",
      x: "center",
      y: "31%",
      textStyle: {
        fontSize: 18,
        fontWeight: "normal",
        color: ["#232a3a"]
      },
      subtextStyle: {
        color: "#232a3a",
        fontSize: 24,
        fontWeight: 400,
        fontFamily: "Source Han Sans CN, Source Han Sans CN-Regular"
      }
    },
    grid: {
      bottom: 100,
      left: 0,
      right: "10%"
    },
    tooltip: {
      trigger: "item",
      formatter: "{b} : {c} ({d}%)"
    },
    series: [
      {
        type: "pie",
        radius: ["33%", "61%"],
        center: ["50%", "50%"],
        data: [{ value: 335, name: "" }],
        labelLine: {
          show: false
        },
        label: {
          normal: {}
        }
      }
    ]
  };
  // 赋值
  option.series = [
    {
      type: "pie",
      radius: ["45%", "80%"],
      center: ["50%", "50%"],
      data: temData,
      labelLine: {
        show: false
      },
      label: {
        normal: {
          position: "inner",
          formatter: function (param: { percent: number }) {
            if (!param.percent) return "";
            const f = Math.round(param.percent * 10) / 10;
            let s = f.toString();
            let rs = s.indexOf(".");
            if (rs < 0) {
              rs = s.length;
              s += ".";
            }
            while (s.length <= rs + 1) {
              s += "0";
            }
            return s + "%";
          },
          textStyle: {
            color: "#fff",
            fontFamily: "AlibabaSans-Regular",
            fontSize: 8
          }
        }
      }
    }
  ];
  myChart.setOption(option);
  const timer: any = setInterval(function () {
    const temData: any = [];
    for (const item in userTotalData) {
      temData.push({
        name: item,
        value: userTotalData[item]
      });
    }
    myChart.setOption({
      series: [
        {
          data: temData
        }
      ]
    });
  }, 10000);
  return timer;
};
const getFiveMinute = () => {
  const end = moment().format("YYYY-MM-DD[T]HH:mm:ss");
  const start = moment(end).subtract(5, "minute").format("YYYY-MM-DD[T]HH:mm:ss");
  const delend = moment(end).subtract(8, "hours").format("YYYY-MM-DD[T]HH:mm:ss");
  const delstart = moment(start).subtract(8, "hours").format("YYYY-MM-DD[T]HH:mm:ss");
  return {
    beforeTime: delstart,
    nowTime: delend
  };
};
const echartsObj: any = ref({});
// 每秒请求的数据
const getEcharsDataPerSecond = async () => {
  const { nowTime } = getFiveMinute();
  const brforePerSecond = moment(nowTime).subtract(1, "second").format("YYYY-MM-DD[T]HH:mm:ss");
  const allData = await getMonitorRequest({
    from: brforePerSecond,
    to: nowTime
  });
  serverVerformanceCount.value = allData.length;
  dealErgodicData(allData);
};
// 服务实例访问数据
let serverExampleData: any = {};
const temServiceLength: any = ref(0);
// 服务类型访问统计图
const serverTypeData: any = {};
// 用户访问统计图
const userTotalData: any = {};
// 初始化五分钟的数据
const getEcharsData = async () => {
  const { beforeTime, nowTime } = getFiveMinute();
  const allData = await getMonitorRequest({
    from: beforeTime,
    to: nowTime
  });
  dealErgodicData(allData);
};
// 处理请求遍历数据
const dealErgodicData = (allData: any) => {
  allData.forEach((item: any) => {
    // 服务器性能
    const subStr: string = item.EndTime.split("T")[1].split(".")[0];
    if (echartsObj.value[subStr]) {
      echartsObj.value[subStr] += 1;
    } else {
      echartsObj.value[subStr] = 1;
    }
    // 服务实例访问统计
    const temService = item.Service;
    if (temService) {
      if (serverExampleData[item.Path]) {
        serverExampleData[item.Path] += 1;
      } else {
        serverExampleData[item.Path] = 1;
        temServiceLength.value++;
      }
    }
    // const temServerExample = item.Path.split("/")[item.Path.split("/").length - 1];
    // if (temServerExample === "wms" || temServerExample === "wmts") {
    //   if (serverExampleData[item.Path]) {
    //     serverExampleData[item.Path] += 1;
    //   } else {
    //     serverExampleData[item.Path] = 1;
    //   }
    // } else {
    //   if (serverExampleData["其他"]) {
    //     serverExampleData["其他"] += 1;
    //   } else {
    //     serverExampleData["其他"] = 1;
    //   }
    // }
    // 服务类型访问统计图
    if (temService) {
      if (serverTypeData[temService]) {
        serverTypeData[temService] += 1;
      } else {
        serverTypeData[temService] = 1;
      }
    }
    // const temserverType = item.Path.split("/")[item.Path.split("/").length - 1];
    // if (temserverType === "wms") {
    //   if (serverTypeData.wms) {
    //     serverTypeData.wms += 1;
    //   } else {
    //     serverTypeData.wms = 1;
    //   }
    // } else if (temserverType === "wmts") {
    //   if (serverTypeData.wmts) {
    //     serverTypeData.wmts += 1;
    //   } else {
    //     serverTypeData.wmts = 1;
    //   }
    // } else {
    //   if (serverTypeData["其他"]) {
    //     serverTypeData["其他"] += 1;
    //   } else {
    //     serverTypeData["其他"] = 1;
    //   }
    // }
    // 用户访问统计图
    const temuserTotal = item.RemoteUser;
    userTotalData[temuserTotal] = 1;
  });
};
const timer: any = [];
const echartsInit = () => {
  const data = serverVerformance();
  timer.push(data.time1);
  myChartSeverColony.value = data.myChartSeverColony;
  // severColony();
  timer.push(serverExampleinit());
  timer.push(serverTypeinit());
  timer.push(userTotalinit());
};
const sizeFun = () => {
  myChartSeverColony.value.resize();
};

onMounted(async () => {
  await getEcharsData();
  echartsInit();
  window.addEventListener("resize", sizeFun);
});
onBeforeUnmount(() => {
  // 离开时清除定时器
  timer.forEach((item: any) => {
    clearInterval(item);
  });
});
</script>
<style lang="scss" scoped>
.home-page {
  width: 100%;
  height: 100%;
  display: flex;
  background: #f4f7fa;
  justify-content: center;
}
.content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .content-operate {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    .operate-left {
      height: 100%;
      width: 74%;
      margin: 0 1.5% 0 2%;
      display: flex;
      flex-direction: column;
      .fast-card {
        width: 100%;
        display: flex;
        height: 23vh;
        justify-content: space-between;
        align-items: center;
      }
    }
    .operate-right {
      flex-grow: 13;
      width: 24%;
      display: flex;
      flex-direction: column;
    }
  }
}
.operate-title {
  position: relative;
  width: 100%;
  height: 30px;
  font-size: 14px;
  background-color: #f7f7f7;
  font-weight: 600;
  color: #4e5f6d;
  display: flex;
  align-items: center;
  text {
    flex-grow: 17;
  }
  .operate-link {
    cursor: pointer;
    right: 0;
    position: absolute;
    width: 70px;
    flex-grow: 1;
    color: #0a5bd8;
  }
}
.operate-row {
  cursor: pointer;
  width: 100%;
  height: 45px;
  font-weight: 600;
  margin: 12px 0 12px 0;
  color: #000000;
  font-size: 13px;
  display: flex;
  .img-box {
    width: 40px;
    height: 40px;
    img {
      width: 40px;
      height: 40px;
    }
  }
  p {
    margin-left: 10px;
    align-self: center;
  }
}
.card {
  width: 596px;
  height: 180px;
  background: #ffffff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  .card-left {
    margin: 0 33px 0 53px;
    width: 72px;
    height: 72px;
    background: #4076f3;
    border-radius: 8px;
    box-shadow: 0px 14px 30px 0px rgba(64, 118, 243, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .card-right {
    width: 200px;
    margin: 0 70px 0 0;
    .title {
      width: 144px;
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #232a3a;
      line-height: 29px;
    }
    .content {
      width: 230px;
      height: 44px;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #858a9c;
      line-height: 20px;
    }
  }
  .card-foot {
    display: flex;
    align-items: center;
    cursor: pointer;
    .foot-text {
      width: 48px;
      height: 16px;
      font-size: 16px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #4076f3;
      line-height: 29px;
    }
    img {
      width: 20px;
      height: 6px;
      margin: 9px 0 0 10px;
    }
  }
}
.pie-chart-box {
  width: 33.3%;
  height: 200px;
}
.server {
  position: relative;
  width: 100%;
  height: 35vh;
  min-height: 340px;
  background: #feffff;
  border-radius: 4px;
  margin-bottom: 2vh;
  .myChartSever-title {
    margin-left: 30px;
    margin-top: 15px;
    position: absolute;
    display: flex;
    width: 98%;
    height: 40px;
    z-index: 1;
    .left {
      width: 84%;
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #232a3a;
    }
    .right {
      height: 40px;
      display: flex;
      align-items: center;
      img {
        width: 24px;
        height: 24px;
        margin: 0 5px 0 0;
      }
      font-size: 22px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #4076f3;
    }
  }
  .broken-line-box {
    margin-top: 20px;
    width: 100%;
    height: 340px;
  }
}
.server-total {
  width: 100%;
  height: 30vh;
  background: #feffff;
  border-radius: 4px;
  .myChartSever-title {
    margin-left: 30px;
    margin-top: 15px;
    position: absolute;
    display: flex;
    width: 1216px;
    height: 40px;
    z-index: 1;
    .left {
      width: 668px;
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #232a3a;
    }
  }
  .server-total-box {
    margin-top: 5%;
    width: 100%;
    display: flex;
  }
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
