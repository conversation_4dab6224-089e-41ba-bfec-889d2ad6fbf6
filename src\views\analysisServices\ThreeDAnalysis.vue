<!--
 * @Description: 网络分析界面
 * @Autor: silei
 * @Date: 2023-02-01 11:57:47
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-08-08 15:56:31
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title">三维分析</div>
    </div>
    <AnalysisList v-model="services" />
  </div>
</template>
<script lang="ts" setup>
import { onMounted, Ref, ref } from "vue";
import AnalysisList from "@/views/analysisServices/components/AnalysisList.vue";
import { getScene3d } from "@/api/analysis/network";
import { BaseSetting } from "geoserver-manager";
const services: Ref<any[]> = ref([]);
/**
 * 获取图层组列表
 */
const loadLayerGroups = async () => {
  const serviceList = await getScene3d();
  console.log(serviceList, "-----");
  services.value = serviceList.scenes.scene3d;
  services.value.forEach((serviceItem: any) => {
    serviceItem.serverType = "scene3d";
    serviceItem.thumbnail = getThumbnail(serviceItem.name);
  });
};
const getThumbnail = (serviceName: string) => {
  const basePath = BaseSetting.getBaseUrl();
  return `${basePath}/rest/resource/thumbnail/map/${serviceName}.png`;
};
onMounted(() => {
  loadLayerGroups();
});
</script>
<style lang="scss" scoped>
.sever-box {
  width: 1600px;
  height: 80px;
  background: #ffffff;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
.server-item {
  cursor: pointer;
  width: 200px;
  height: 100%;
  display: flex;
  align-items: center;
  img {
    width: 35px;
    height: 35px;
    margin: 0 20px;
  }
  .right {
    width: 120px;
    display: flex;
    flex-direction: column;
    .titile {
      width: 72px;
      height: 27px;
      font-size: 18px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #232a3a;
    }
    span {
      width: 88px;
      height: 20px;
      font-size: 14px;
      font-family: Source Han Sans CN, Source Han Sans CN-Regular;
      font-weight: 400;
      text-align: left;
      color: #7d8da1;
    }
  }
}
.active-title {
  color: #4076f3 !important;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
