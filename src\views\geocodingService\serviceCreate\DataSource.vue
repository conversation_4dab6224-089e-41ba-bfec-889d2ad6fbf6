<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:54:49
 * @LastEditors: silei
 * @LastEditTime: 2023-11-30 17:26:28
-->
<template>
  <GeocodingDataStore ref="datastoreRef" v-model="value" />
</template>
<script lang="ts" setup>
import { DatasourceType } from "geoserver-manager";
import { computed, PropType, ref } from "vue";
import GeocodingDataStore from "./GeocodingDataStore.vue";
const props = defineProps({
  modelValue: Object as PropType<any>,
  datasourceType: Object as PropType<DatasourceType>
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});

const datastoreRef = ref();
const submitForm = () => {
  return datastoreRef.value.submitForm();
};
defineExpose({
  submitForm
});
</script>
