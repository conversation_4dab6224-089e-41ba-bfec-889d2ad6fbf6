<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-03 09:42:55
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-07 17:53:23
-->
<template>
  <el-form
    :model="form"
    ref="ruleFormRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    :rules="rules"
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="用户密码" prop="password">
          <el-input v-model="form.password" type="password" show-password />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item label="客户端标识类型" prop="clientType">
          <el-select v-model="form.clientType" class="m-2" placeholder="Select" size="large">
            <el-option
              v-for="item in clientTypes"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="referer" v-if="form.clientType === 1" label="HTTP Referer">
          <el-input v-model="form.referer" />
        </el-form-item>
        <el-form-item prop="ip" v-if="form.clientType === 2" label="IP">
          <el-input v-model="form.ip" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="expireDate" label="有效期">
          <el-select v-model="form.expireDate" class="m-2" placeholder="Select" size="large">
            <el-option
              v-for="item in expireDates"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="令牌">
          <el-button @click="handleCreateToken">生成令牌</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item v-if="!!tokenResult" label="令牌值">
          <el-input readonly v-model="tokenResult" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { createToken } from "@/api/security/token";
import { TokenInfo } from "@/interface/security/token";
import { FormRules } from "element-plus";
import { onMounted, reactive, ref } from "vue";
// import localCache from "@/utils/auth";
const ruleFormRef = ref();
const form: TokenInfo = reactive({
  clientType: 1,
  expireDate: 60
} as any);
const clientTypes = reactive([
  {
    id: 1,
    label: "HTTP Referer"
  },
  {
    id: 2,
    label: "客户端IP"
  },
  {
    id: 3,
    label: "当前请求的IP"
  },
  {
    id: 0,
    label: "无客户端限制"
  }
]);
const rules = reactive<FormRules>({
  userName: [{ required: true, message: "用户名不能为空", trigger: "blur" }],
  password: [{ required: true, message: "密码不能为空", trigger: "blur" }],
  clientType: [{ required: true, message: "客户端标识不能为空", trigger: "blur" }],
  referer: [{ required: true, message: "HTTP Referer不能为空", trigger: "blur" }],
  ip: [{ required: true, message: "IP不能为空", trigger: "blur" }],
  expireDate: [{ required: true, message: "过期时间不能为空", trigger: "blur" }]
});
const expireDates = reactive([
  {
    label: "一小时",
    value: 60
  },
  {
    label: "一天",
    value: 60 * 24
  },
  {
    label: "一星期",
    value: 60 * 24 * 7
  },
  {
    label: "一月",
    value: 60 * 24 * 30
  },
  {
    label: "一年",
    value: 60 * 24 * 365
  }
]);
const tokenResult = ref("");
const handleCreateToken = async () => {
  ruleFormRef.value.validate(async (vaild: boolean) => {
    if (vaild) {
      const token = await createToken(form);
      tokenResult.value = token;
    }
  });
};
// const isAdmin = ref(false);
onMounted(() => {
  // 判断当前用户是否拥有管理员权限
  // const roles = localCache.getCache("userRoles");
  // isAdmin.value = roles.includes("ADMIN");
  // if(!isAdmin.value){
  //   // 获取当前用户名密码
  // }
});
</script>
