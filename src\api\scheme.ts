import hRequest from "@/utils/http/request_server/hRequest";
import { DataType } from "@/utils/http/types";
import type {
  MapSchemeDto,
  MapSchemePageQuery,
  MapSchemePageResponse,
  MapSchemeDetailResponse,
  MapSchemeOperationResponse
} from "@/interface/business/mapScheme";

/**
 * @description 分页查询地图方案
 * @param {MapSchemePageQuery} data 分页查询参数
 * @returns {Promise<MapSchemePageResponse>} 分页查询结果
 */
export const getSchemePage = async (data: MapSchemePageQuery): Promise<MapSchemePageResponse> => {
  const result: any = await hRequest.get<DataType<MapSchemePageResponse>>({
    url: "/business/map/scheme/page",
    params: data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 新增地图方案
 * @param {MapSchemeDto} data 地图方案数据
 * @returns {Promise<MapSchemeOperationResponse>} 新增操作结果
 */
export const addScheme = async (data: MapSchemeDto): Promise<MapSchemeOperationResponse> => {
  const result: any = await hRequest.post<DataType<MapSchemeOperationResponse>>({
    url: "/business/map/scheme",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 修改地图方案
 * @param {MapSchemeDto} data 地图方案数据
 * @returns {Promise<MapSchemeOperationResponse>} 修改操作结果
 */
export const updateScheme = async (data: MapSchemeDto): Promise<MapSchemeOperationResponse> => {
  const result: any = await hRequest.put<DataType<MapSchemeOperationResponse>>({
    url: "/business/map/scheme",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 删除地图方案
 * @param {number} id 方案ID
 * @returns {Promise<MapSchemeOperationResponse>} 删除操作结果
 */
export const deleteScheme = async (id: number): Promise<MapSchemeOperationResponse> => {
  const result: any = await hRequest.delete<DataType<MapSchemeOperationResponse>>({
    url: `/business/map/scheme/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 查询地图方案详情
 * @param {number} id 方案ID
 * @returns {Promise<MapSchemeDetailResponse>} 方案详情
 */
export const getSchemeDetail = async (id: number): Promise<MapSchemeDetailResponse> => {
  const result: any = await hRequest.get<DataType<MapSchemeDetailResponse>>({
    url: `/business/map/scheme/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
