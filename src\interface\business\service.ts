/*
 * @Description:
 * @Autor: silei
 * @Date: 2023-02-01 10:52:54
 * @LastEditors: silei
 * @LastEditTime: 2023-02-14 14:17:49
 */
// interface Service {
//   name: string;
//   title?: string;
//   description?: string;
//   types: string[];
//   enabled: boolean;
//   visits: number;
//   thumbnail?: string;
// }
interface ServiceSetting {
  name?: string;
  enabled: boolean;
  workspace?: {
    name: string;
  };
}
