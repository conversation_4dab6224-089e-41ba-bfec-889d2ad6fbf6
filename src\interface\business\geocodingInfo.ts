import { BaseServiceInfo, GeoServer<PERSON><PERSON>er, PreviewAddress, ServiceType } from "geoserver-manager";

/*
 * @Description: 地理编码服务
 * @Autor: silei
 * @Date: 2023-11-28 15:04:34
 * @LastEditors: silei
 * @LastEditTime: 2023-11-28 15:04:40
 */
export interface GeocodingInfo {
  datasource: string;
  /**
   * 参与地址匹配数据的数据集里列表
   */
  datasetNames?: null | string;
  dateCreated?: null | string;
  dateModified?: null | string;
  description?: null | string;
  enabled?: boolean | null;
  /**
   * 设置分组字段，例如“province,city,county”。用户使用地址匹配功能时，将依照设置的字段限定查询的区域。
   */
  filterFields?: null | string;
  /**
   * 用于设置查询半径，设置后，用户将获得指定半径内的查询结果。使用反向地址匹配时有效。单位与数据集单位一致
   */
  geoDecodingRadius?: null | string;
  id?: null | string;
  name: string;
  /**
   * 查询字段名，即参与地址匹配的字段
   */
  searchFields?: null | string;
  title?: null | string;
  /**
   * 参与地址匹配数据的数据目录位置
   */
  url?: null | string;
  visits?: number | null;
  indexUrl: string;
  errorMsg: string | null;
  parameters: any;
}
