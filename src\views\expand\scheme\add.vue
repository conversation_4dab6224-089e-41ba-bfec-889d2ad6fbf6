<!--
 * @Description: 地图方案新增/编辑/预览
 * @Date: 2023-02-06 11:42:26
 * @Author: <PERSON><PERSON>er<PERSON>
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025-01-11 15:00:00
-->
<template>
  <div class="scheme-editor">
    <!-- 头部操作栏 -->
    <div class="header-toolbar">
      <div class="toolbar-left">
        <el-button @click="goBack" :icon="ArrowLeft">返回</el-button>
        <span class="page-title">{{ pageTitle }}</span>
      </div>
      <div class="toolbar-right">
        <template v-if="isPreviewMode">
          <el-button @click="editScheme" type="primary">编辑方案</el-button>
        </template>
        <template v-else>
          <!-- <el-button @click="resetForm">重置</el-button> -->
          <el-button type="primary" @click="saveScheme" :loading="saving">
            {{ isEditMode ? "更新方案" : "保存方案" }}
          </el-button>
        </template>
      </div>
    </div>

    <!-- 主体内容区 -->
    <div class="content-container">
      <!-- 地图容器撑满整个页面 -->
      <div class="map-container-full">
        <div id="map-container" ref="mapContainer"></div>

        <!-- 浮动配置面板 -->
        <div class="floating-config-panel" :class="{ 'panel-collapsed': panelCollapsed }">
          <!-- 面板头部 -->
          <div class="panel-header">
            <span class="panel-title">方案配置</span>
            <el-button
              @click="togglePanel"
              :icon="panelCollapsed ? ArrowLeft : ArrowRight"
              text
              size="small"
            >
              {{ panelCollapsed ? "展开" : "收起" }}
            </el-button>
          </div>

          <!-- 面板内容 -->
          <div class="panel-content" v-show="!panelCollapsed">
            <!-- 预览模式的方案信息 -->
            <div v-if="isPreviewMode" class="scheme-info">
              <div class="info-section">
                <h4>基本信息</h4>
                <div class="info-item">
                  <label>方案名称：</label>
                  <span>{{ formData.name }}</span>
                </div>
                <div class="info-item">
                  <label>方案描述：</label>
                  <span>{{ formData.remark || "无" }}</span>
                </div>
                <div class="info-item" v-if="schemeDetailInfo.createTime">
                  <label>创建时间：</label>
                  <span>{{ schemeDetailInfo.createTime }}</span>
                </div>
                <div class="info-item" v-if="schemeDetailInfo.updateTime">
                  <label>更新时间：</label>
                  <span>{{ schemeDetailInfo.updateTime }}</span>
                </div>
              </div>

              <div class="info-section">
                <h4>地图配置</h4>
                <div class="info-item">
                  <label>地图主题：</label>
                  <span>{{ getThemeLabel(mapConfig.baseLayer) }}</span>
                </div>
                <div class="info-item">
                  <label>显示标注：</label>
                  <span>{{ mapConfig.showLabels ? "是" : "否" }}</span>
                </div>
                <div class="info-item">
                  <label>坐标系统：</label>
                  <span>{{ getProjectionLabel(mapConfig.projection) }}</span>
                </div>
                <div class="info-item">
                  <label>边界裁剪：</label>
                  <span>{{ mapConfig.enableBoundaryClip ? "已启用" : "未启用" }}</span>
                </div>
                <div class="info-item" v-if="mapConfig.enableBoundaryClip && mapConfig.clipExtent">
                  <label>裁剪范围：</label>
                  <span>{{ formatExtentDisplay(mapConfig.clipExtent) }}</span>
                </div>
              </div>

              <div class="info-section">
                <h4>视图配置</h4>
                <div class="info-item">
                  <label>中心点：</label>
                  <span>{{ centerPointDisplay }}</span>
                </div>
                <div class="info-item">
                  <label>初始级别：</label>
                  <span>{{ mapConfig.initialZoom }}</span>
                </div>
                <div class="info-item">
                  <label>最小级别：</label>
                  <span>{{ mapConfig.minZoom }}</span>
                </div>
              </div>

              <div
                class="info-section"
                v-if="formData.userCodeList && formData.userCodeList.length > 0"
              >
                <h4>关联用户</h4>
                <div class="user-tags">
                  <el-tag v-for="user in formData.userCodeList" :key="user" size="small">
                    {{ getUserDisplayName(user) }}
                  </el-tag>
                </div>
              </div>

              <div class="info-section" v-if="formData.selectedLayers">
                <h4>图层配置</h4>
                <div class="info-item">
                  <label>选中图层：</label>
                  <span>{{ getLayerDisplayName(formData.selectedLayers) }}</span>
                </div>
              </div>
            </div>

            <!-- 编辑/新增模式的表单 -->
            <el-form
              v-else
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="80px"
              size="small"
            >
              <!-- 基本信息 -->
              <div class="form-section">
                <h4>基本信息</h4>
                <el-form-item label="方案名称" prop="name">
                  <el-input v-model="formData.name" placeholder="请输入方案名称" />
                </el-form-item>
                <el-form-item label="方案描述" prop="remark">
                  <el-input
                    v-model="formData.remark"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入方案描述"
                  />
                </el-form-item>
              </div>

              <!-- 租户配置 -->
              <div class="form-section">
                <h4>租户配置</h4>
                <el-form-item label="关联用户" prop="userCodeList">
                  <el-select
                    v-model="formData.userCodeList"
                    multiple
                    filterable
                    placeholder="请选择关联用户"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="user in userList"
                      :key="user.value"
                      :label="user.label"
                      :value="user.code"
                    />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 图层配置 -->
              <div class="form-section">
                <h4>图层配置</h4>
                <el-form-item label="选择图层" prop="selectedLayers">
                  <el-select
                    v-model="formData.selectedLayers"
                    filterable
                    placeholder="请选择地图图层"
                    style="width: 100%"
                    :disabled="isPreviewMode"
                    @change="handleLayerChange"
                  >
                    <el-option
                      v-for="service in serviceList"
                      :key="service.name"
                      :label="service.title || service.name"
                      :value="service.name"
                    />
                  </el-select>
                </el-form-item>
              </div>

              <!-- 地图配置 -->
              <div class="form-section">
                <h4>地图配置</h4>
                <el-form-item label="地图主题" prop="mapTheme">
                  <el-select v-model="mapConfig.baseLayer" @change="updateMapTheme">
                    <el-option label="矢量底图" value="vector" />
                    <el-option label="影像底图" value="image" />
                    <el-option label="地形底图" value="terrain" />
                  </el-select>
                </el-form-item>
                <el-form-item label="显示标注" prop="showLabels">
                  <el-switch v-model="mapConfig.showLabels" @change="updateMapLabels" />
                </el-form-item>
                <el-form-item label="坐标系统" prop="projection">
                  <el-select v-model="mapConfig.projection" @change="updateProjection">
                    <el-option label="WGS84 (EPSG:4326)" value="EPSG:4326" />
                    <el-option label="Web Mercator (EPSG:3857)" value="EPSG:3857" />
                  </el-select>
                </el-form-item>
                <el-form-item label="边界裁剪" prop="enableBoundaryClip">
                  <div class="boundary-clip-config">
                    <div class="clip-switch-row">
                      <el-switch
                        v-model="mapConfig.enableBoundaryClip"
                        @change="updateBoundaryClip"
                      />
                      <span class="form-item-tip">启用后将根据当前地图范围裁剪显示</span>
                    </div>
                    <div v-if="mapConfig.enableBoundaryClip" class="clip-extent-info">
                      <div class="extent-display" v-if="mapConfig.clipExtent">
                        <span class="extent-label">裁剪范围：</span>
                        <span class="extent-coords">
                          {{ formatExtentDisplay(mapConfig.clipExtent) }}
                        </span>
                      </div>
                      <div class="extent-controls">
                        <el-button
                          @click="refreshClipExtent"
                          size="small"
                          type="primary"
                          :icon="Refresh"
                          style="width: 100%"
                        >
                          重新获取当前范围
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </div>

              <!-- 视图配置 -->
              <div class="form-section">
                <h4>视图配置</h4>
                <el-form-item label="中心点">
                  <div class="center-point-config">
                    <el-input
                      v-model="centerPointDisplay"
                      placeholder="点击地图获取坐标"
                      @blur="parseCenterPoint"
                      style="margin-bottom: 8px"
                    />
                    <el-button
                      @click="enableMapClick"
                      :type="mapClickEnabled ? 'primary' : 'default'"
                      size="small"
                      style="width: 100%"
                    >
                      {{ mapClickEnabled ? "取消点击" : "地图点击" }}
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="初始级别" prop="initialZoom">
                  <el-slider
                    v-model="mapConfig.initialZoom"
                    :min="1"
                    :max="18"
                    :step="1"
                    show-input
                    @change="updateZoom"
                  />
                </el-form-item>
                <el-form-item label="最小级别" prop="minZoom">
                  <el-slider
                    v-model="mapConfig.minZoom"
                    :min="1"
                    :max="mapConfig.initialZoom"
                    :step="1"
                    show-input
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>

        <!-- 地图信息面板 -->
        <div class="map-info-panel" v-if="currentCoordinate">
          <span>当前坐标: {{ currentCoordinate }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox, FormInstance, FormRules } from "element-plus";
import { ArrowLeft, ArrowRight, Refresh } from "@element-plus/icons-vue";
import { Map, View } from "ol";
import { Tile as TileLayer } from "ol/layer";
import { WMTS, XYZ } from "ol/source";
import { get as getProjection, fromLonLat, Projection, toLonLat, transformExtent } from "ol/proj";
import { defaults as defaultControls, ScaleLine, MousePosition } from "ol/control";
import { createStringXY } from "ol/coordinate";
import { addScheme, updateScheme, getSchemeDetail } from "@/api/scheme";
import { getUserList } from "@/api/user";
import type { MapSchemeDto, MapSchemeVo } from "@/interface/business/mapScheme";
import type { UserSelectOption } from "@/interface/security/user";
import { ServiceUtil, BaseServiceInfo, BaseSetting } from "geoserver-manager";
import "ol/ol.css";
import localCache from "@/utils/auth";
import { getWmtsInfo } from "@/api/resource";
import proj4 from "proj4";
import WMTSGrid from "ol/tilegrid/WMTS";
import { createTemToken } from "@/api/security/token";
import { boundingExtent, getTopLeft, getWidth } from "ol/extent";
const router = useRouter();
const route = useRoute();
const formRef = ref<FormInstance>();
const baseUrl = BaseSetting.getBaseUrl();
/**
 * @description 页面模式判断
 */
const isEditMode = computed(() => route.query.mode === "edit" && route.query.id);
const isPreviewMode = computed(
  () => route.path.includes("Preview") || route.query.mode === "preview"
);
const pageTitle = computed(() => {
  if (isPreviewMode.value) return "方案预览";
  if (isEditMode.value) return "编辑方案";
  return "新增方案";
});

/**
 * @description 表单数据
 */
const formData = reactive<MapSchemeDto & { selectedLayers?: string }>({
  id: 0,
  name: "",
  remark: "",
  userCodeList: [],
  selectedLayers: "", // 新增：选中的图层
  configValue: ""
});

/**
 * @description 地图配置
 */
const mapConfig = reactive({
  baseLayer: "vector",
  showLabels: true,
  projection: "EPSG:3857",
  center: [103.55, 29.41],
  initialZoom: 13,
  minZoom: 8,
  enableBoundaryClip: false, // 新增：启用边界裁剪
  clipExtent: null as number[] | null // 新增：裁剪范围 [minX, minY, maxX, maxY] (WGS84坐标)
});

/**
 * @description 表单验证规则
 */
const formRules: FormRules = {
  name: [{ required: true, message: "请输入方案名称", trigger: "blur" }]
};

/**
 * @description 其他响应式数据
 */
const saving = ref(false);
const userList = ref<UserSelectOption[]>([]);
const serviceList = ref<BaseServiceInfo[]>([]); // 新增：地图服务列表
const map = ref<Map | null>(null);
const mapContainer = ref<HTMLElement>();
const mapClickEnabled = ref(false);
const currentCoordinate = ref("");
const panelCollapsed = ref(false);
const schemeDetailInfo = ref<MapSchemeVo>({} as MapSchemeVo);

/**
 * @description 中心点显示格式
 */
const centerPointDisplay = computed({
  get: () => {
    const [lon, lat] = mapConfig.center;
    return `${lon.toFixed(6)}, ${lat.toFixed(6)}`;
  },
  set: (value: string) => {
    // 通过parseCenterPoint处理
  }
});

/**
 * @description 瓦片服务配置
 */
const TILE_SERVICES = {
  vector:
    "http://*************:8077/bcserver/services/swq_tdt_digital/rest/xyz/{z}/{x}/{y}/tile.png",
  image: "http://*************:8077/bcserver/services/swq_tdt_image/rest/xyz/{z}/{x}/{y}/tile.png",
  terrain:
    "http://*************:8077/bcserver/services/swq_tdt_terrain/rest/xyz/{z}/{x}/{y}/tile.png",
  labels: "http://*************:8077/bcserver/services/swq_tdt_label/rest/xyz/{z}/{x}/{y}/tile.png"
};

/**
 * @description 初始化地图
 */
const initMap = async () => {
  await nextTick();

  if (!mapContainer.value) return;

  // 创建底图图层
  const baseLayer = new TileLayer({
    source: new XYZ({
      url: TILE_SERVICES.vector,
      maxZoom: 18
    })
  });

  // 创建标注图层
  const labelsLayer = new TileLayer({
    source: new XYZ({
      url: TILE_SERVICES.labels,
      maxZoom: 18
    })
  });

  // 创建地图
  map.value = new Map({
    target: mapContainer.value,
    layers: [baseLayer, labelsLayer],
    view: new View({
      center: fromLonLat(mapConfig.center),
      zoom: mapConfig.initialZoom,
      minZoom: mapConfig.minZoom,
      projection: mapConfig.projection
    }),
    controls: defaultControls().extend([
      new ScaleLine({ units: "metric" }),
      new MousePosition({
        coordinateFormat: createStringXY(6),
        projection: "EPSG:4326"
      })
    ])
  });

  // 添加点击事件
  map.value.on("click", (event) => {
    if (mapClickEnabled.value) {
      const coordinate = toLonLat(event.coordinate);
      mapConfig.center = [coordinate[0], coordinate[1]];
      updateMapCenter();
      mapClickEnabled.value = false;
    }

    // 更新当前坐标显示
    const coord = toLonLat(event.coordinate);
    currentCoordinate.value = `${coord[0].toFixed(6)}, ${coord[1].toFixed(6)}`;
  });

  // 保存图层引用以便后续操作
  map.value.set("baseLayer", baseLayer);
  map.value.set("labelsLayer", labelsLayer);
};

/**
 * @description 更新地图主题
 */
const updateMapTheme = () => {
  if (!map.value) return;

  const baseLayer = map.value.get("baseLayer") as TileLayer<XYZ>;
  const source = baseLayer.getSource() as XYZ;
  source.setUrl(TILE_SERVICES[mapConfig.baseLayer as keyof typeof TILE_SERVICES]);
};

/**
 * @description 更新地图标注
 */
const updateMapLabels = () => {
  if (!map.value) return;

  const labelsLayer = map.value.get("labelsLayer") as TileLayer<XYZ>;
  labelsLayer.setVisible(mapConfig.showLabels);
};

/**
 * @description 更新投影
 */
const updateProjection = () => {
  if (!map.value) return;

  const view = map.value.getView();
  const center = toLonLat(view.getCenter() || [0, 0]);

  // map.value.setView(
  //   new View({
  //     center: fromLonLat(center),
  //     zoom: view.getZoom(),
  //     minZoom: mapConfig.minZoom,
  //     projection: mapConfig.projection
  //   })
  // );
};

/**
 * @description 更新地图中心点
 */
const updateMapCenter = () => {
  if (!map.value) return;

  const view = map.value.getView();
  view.setCenter(fromLonLat(mapConfig.center));
};

/**
 * @description 更新缩放级别
 */
const updateZoom = () => {
  if (!map.value) return;

  const view = map.value.getView();
  view.setZoom(mapConfig.initialZoom);
  view.setMinZoom(mapConfig.minZoom);
};

/**
 * @description 获取当前地图可视范围
 * @returns {number[] | null} 返回WGS84坐标系的extent [minX, minY, maxX, maxY]
 */
const getCurrentMapExtent = (): number[] | null => {
  if (!map.value) return null;

  const view = map.value.getView();
  const extent = view.calculateExtent(map.value.getSize());
  // 转换为WGS84坐标系
  const wgs84Extent = transformExtent(extent, mapConfig.projection, "EPSG:4326");
  return wgs84Extent;
};

/**
 * @description 更新边界裁剪设置
 */
const updateBoundaryClip = () => {
  if (mapConfig.enableBoundaryClip) {
    // 开启边界裁剪时，自动获取当前地图范围
    mapConfig.clipExtent = getCurrentMapExtent();
    if (mapConfig.clipExtent) {
      ElMessage.success("已获取当前地图范围作为裁剪边界");
    }
  } else {
    // 关闭边界裁剪时，清空裁剪范围
    mapConfig.clipExtent = null;
  }

  // 如果有选中的图层，重新加载以应用边界裁剪设置
  if (formData.selectedLayers) {
    loadSelectedLayer(formData.selectedLayers, false);
  }
};

/**
 * @description 重新获取当前地图范围
 */
const refreshClipExtent = () => {
  if (!mapConfig.enableBoundaryClip) {
    ElMessage.warning("请先开启边界裁剪功能");
    return;
  }

  mapConfig.clipExtent = getCurrentMapExtent();
  if (mapConfig.clipExtent) {
    ElMessage.success("已更新裁剪范围");
    // 重新加载图层以应用新的裁剪范围
    if (formData.selectedLayers) {
      loadSelectedLayer(formData.selectedLayers, false);
    }
  } else {
    ElMessage.error("获取地图范围失败");
  }
};

/**
 * @description 启用地图点击
 */
const enableMapClick = () => {
  mapClickEnabled.value = !mapClickEnabled.value;
  if (mapClickEnabled.value) {
    ElMessage.info("请在地图上点击选择中心点");
  }
};

/**
 * @description 切换面板展开/收起状态
 */
const togglePanel = () => {
  panelCollapsed.value = !panelCollapsed.value;
};

/**
 * @description 获取主题标签
 */
const getThemeLabel = (theme: string) => {
  const labels: Record<string, string> = {
    vector: "矢量底图",
    image: "影像底图",
    terrain: "地形底图"
  };
  return labels[theme] || theme;
};

/**
 * @description 获取投影标签
 */
const getProjectionLabel = (projection: string) => {
  const labels: Record<string, string> = {
    "EPSG:4326": "WGS84 (EPSG:4326)",
    "EPSG:3857": "Web Mercator (EPSG:3857)"
  };
  return labels[projection] || projection;
};

/**
 * @description 获取用户显示名称
 */
const getUserDisplayName = (userCode: string) => {
  const user = userList.value.find((u) => u.value === userCode);
  return user?.label || userCode;
};

/**
 * @description 获取图层显示名称
 * @param {string} layerName 图层名称
 * @returns {string} 图层显示名称
 */
const getLayerDisplayName = (layerName: string): string => {
  if (!layerName) {
    return "未选择图层";
  }

  if (serviceList.value.length === 0) {
    console.log("服务列表为空，返回图层名称:", layerName);
    return layerName;
  }

  const service = serviceList.value.find((s) => s.name === layerName);
  if (!service) {
    console.log("未找到图层服务信息:", layerName);
    return layerName;
  }

  return service.title || service.name || layerName;
};

/**
 * @description 格式化extent显示
 * @param {number[]} extent 范围数组 [minX, minY, maxX, maxY]
 * @returns {string} 格式化的范围字符串
 */
const formatExtentDisplay = (extent: number[]): string => {
  if (!extent || extent.length !== 4) return "无效范围";

  const [minX, minY, maxX, maxY] = extent;
  return `${minX.toFixed(6)}, ${minY.toFixed(6)} 至 ${maxX.toFixed(6)}, ${maxY.toFixed(6)}`;
};

// 存储当前加载的图层引用
const currentLayer = ref();

/**
 * @description 清理当前图层
 */
const clearCurrentLayer = () => {
  if (currentLayer.value && map.value) {
    console.log(map.value.getLayers().getArray());
    map.value.removeLayer(currentLayer.value);
    console.log(map.value.getLayers().getArray());
    // map.value.render();
    // debugger;
    currentLayer.value = null;
  }
};

/**
 * @description 加载选中的图层
 * @param {string} layerName 图层名称
 */
const loadSelectedLayer = async (layerName: string, isLocation: boolean = true) => {
  console.log("开始加载图层:", layerName);

  if (!layerName) {
    console.log("图层名称为空，跳过加载");
    return;
  }

  if (!map.value) {
    console.log("地图对象不存在，跳过加载");
    return;
  }
  try {
    // 清理现有图层
    clearCurrentLayer();
    console.log("已清理现有图层");

    // 获取图层信息
    const wmtsInfo = await getWmtsInfo(layerName);
    proj4.defs(wmtsInfo.srs, wmtsInfo.srsWkt);
    if (wmtsInfo.srs === "EPSG:4326") {
      const projection = getProjection("EPSG:900913");
      const projectionExtent = projection?.getExtent();
      const userName = localCache.getCache("realName");
      const token = await createTemToken(userName, "");

      const matrixIdS = [
        "EPSG:900913:0",
        "EPSG:900913:1",
        "EPSG:900913:2",
        "EPSG:900913:3",
        "EPSG:900913:4",
        "EPSG:900913:5",
        "EPSG:900913:6",
        "EPSG:900913:7",
        "EPSG:900913:8",
        "EPSG:900913:9",
        "EPSG:900913:10",
        "EPSG:900913:11",
        "EPSG:900913:12",
        "EPSG:900913:13",
        "EPSG:900913:14",
        "EPSG:900913:15",
        "EPSG:900913:16",
        "EPSG:900913:17",
        "EPSG:900913:18",
        "EPSG:900913:19",
        "EPSG:900913:20",
        "EPSG:900913:21"
      ];

      const width = getWidth(projectionExtent!);
      const resolutions = [];
      for (let z = 0; z < 21; z++) {
        resolutions[z] = width / (256 * Math.pow(2, z));
      }

      // 创建新图层
      const layerOptions: any = {
        source: new WMTS({
          url: `${baseUrl}${wmtsInfo.url ?? "/gwc/service/wmts"}?token=${token}`,
          layer: wmtsInfo.name,
          matrixSet: "EPSG:900913",
          format: wmtsInfo.format,
          tileGrid: new WMTSGrid({
            origin: getTopLeft(projectionExtent!),
            resolutions,
            matrixIds: matrixIdS
          }),
          style: wmtsInfo.style
        })
      };

      // 如果启用边界裁剪，添加extent配置
      if (mapConfig.enableBoundaryClip && mapConfig.clipExtent) {
        // 将WGS84坐标的clipExtent转换为当前投影坐标系
        const projectionBound = transformExtent(mapConfig.clipExtent, "EPSG:4326", "EPSG:3857");
        layerOptions.extent = projectionBound;
        console.log("应用边界裁剪 (EPSG:4326分支):", {
          原始范围: mapConfig.clipExtent,
          转换后范围: projectionBound
        });
      }

      currentLayer.value = new TileLayer(layerOptions);

      // 添加到地图
      map.value.addLayer(currentLayer.value);

      // 根据边界裁剪设置和isLocation参数决定fit的范围
      if (isLocation) {
        if (mapConfig.enableBoundaryClip && mapConfig.clipExtent) {
          // 如果启用边界裁剪，fit到裁剪范围
          const clipBound = transformExtent(mapConfig.clipExtent, "EPSG:4326", "EPSG:3857");
          map.value.getView().fit(clipBound, {
            size: map.value.getSize()
          });
          console.log("已应用边界裁剪，fit到裁剪范围:", mapConfig.clipExtent);
        } else {
          // 否则fit到整个图层范围
          console.log(getExtentByBbox(wmtsInfo.bound)!);
          const bound = getExtentByBbox(wmtsInfo.bound)!;
          const projectionBound = transformExtent(bound, "EPSG:4326", "EPSG:3857");
          map.value.getView().fit(projectionBound, {
            size: map.value.getSize()
          });
          console.log("fit到图层完整范围");
        }
      }
    } else {
      const projection = new Projection({
        code: wmtsInfo.srs,
        units: wmtsInfo.units,
        axisOrientation: wmtsInfo.neu ? "neu" : undefined
      });
      const userName = localCache.getCache("realName");
      const token = await createTemToken(userName, "");

      const geoserverLayerOptions: any = {
        source: new WMTS({
          url: `${baseUrl}${wmtsInfo.url ?? "/gwc/service/wmts"}?token=${token}`,
          layer: wmtsInfo.name,
          matrixSet: wmtsInfo.tileMatrixSet,
          format: wmtsInfo.format,
          tileGrid: new WMTSGrid({
            origin: getTopLeft(getExtentByBbox(wmtsInfo.gridBound)!),
            resolutions: wmtsInfo.resolutions,
            matrixIds: wmtsInfo.matrixIds
          }),
          style: wmtsInfo.style
          // tileLoadFunction: function (image: any, src) {
          //   getImage(src).then((data) => {
          //     if (data) {
          //       const url = URL.createObjectURL(data);
          //       const img: any = image.getImage();
          //       img.addEventListener("load", function () {
          //         console.log("aaa");
          //         URL.revokeObjectURL(url);
          //       });
          //       img.src = url;
          //     }
          //   });
          // }
        })
      };

      // 如果启用边界裁剪，添加extent配置
      if (mapConfig.enableBoundaryClip && mapConfig.clipExtent) {
        // 对于非EPSG:4326的坐标系，clipExtent已经是WGS84坐标，需要转换到图层坐标系
        if (wmtsInfo.srs !== "EPSG:4326") {
          const layerBound = transformExtent(mapConfig.clipExtent, "EPSG:4326", wmtsInfo.srs);
          geoserverLayerOptions.extent = layerBound;
          console.log("应用边界裁剪 (非EPSG:4326分支):", {
            原始范围: mapConfig.clipExtent,
            图层坐标系: wmtsInfo.srs,
            转换后范围: layerBound
          });
        } else {
          // 如果图层本身就是WGS84，直接使用clipExtent
          geoserverLayerOptions.extent = mapConfig.clipExtent;
          console.log("应用边界裁剪 (WGS84图层):", {
            裁剪范围: mapConfig.clipExtent
          });
        }
      }

      const geoserverLayer = new TileLayer(geoserverLayerOptions);
      map.value.addLayer(geoserverLayer);

      // 根据边界裁剪设置和isLocation参数决定fit的范围
      if (isLocation) {
        if (mapConfig.enableBoundaryClip && mapConfig.clipExtent) {
          // 如果启用边界裁剪，fit到裁剪范围
          let fitExtent;
          if (wmtsInfo.srs !== "EPSG:4326") {
            // 转换到图层坐标系
            fitExtent = transformExtent(mapConfig.clipExtent, "EPSG:4326", wmtsInfo.srs);
          } else {
            // 直接使用WGS84坐标
            fitExtent = mapConfig.clipExtent;
          }
          map.value.getView().fit(fitExtent, {
            size: map.value.getSize()
          });
          console.log("已应用边界裁剪，fit到裁剪范围:", mapConfig.clipExtent);
        } else {
          // 否则fit到整个图层范围
          map.value.getView().fit(getExtentByBbox(wmtsInfo.bound)!, {
            size: map.value.getSize()
          });
          console.log("fit到图层完整范围");
        }
      }
    }

    console.log(`图层 ${layerName} 加载成功`);
  } catch (error) {
    console.error(`加载图层 ${layerName} 失败:`, error);
    ElMessage.error(`加载图层失败: ${error}`);
  }
};

/**
 * @description 解析中心点坐标
 */
const parseCenterPoint = (event: any) => {
  const value = event.target.value.trim();
  if (!value) return;

  const coords = value.split(",").map((s: string) => parseFloat(s.trim()));
  if (coords.length === 2 && !coords.some(isNaN)) {
    mapConfig.center = coords;
    updateMapCenter();
  } else {
    ElMessage.error("坐标格式错误，请输入：经度, 纬度");
  }
};

/**
 * @description 加载用户列表
 */
const loadUserList = async () => {
  try {
    const response = await getUserList();
    if (response.code === 200 && Array.isArray(response.data)) {
      userList.value = response.data.map((user) => ({
        label: user.name || user.nickname || user.username || "未知用户",
        value: user.code || user.username || String(user.id),
        code: user.code,
        id: user.id
      }));
    } else {
      console.error("用户列表接口返回异常:", response.msg);
      userList.value = [];
    }
  } catch (error) {
    console.error("加载用户列表失败:", error);
    userList.value = [];
  }
};

/**
 * @description 加载地图服务列表
 */
const loadServiceList = async () => {
  try {
    const allServices = await ServiceUtil.getAllServices();
    // 过滤出地图服务，展开所有子服务
    const mapServices: BaseServiceInfo[] = [];
    allServices.forEach((service: any) => {
      if (service.children && Array.isArray(service.children)) {
        service.children.forEach((child: BaseServiceInfo) => {
          // 只添加地图类型的服务
          if (child.type === "map" || child.serviceTypeString?.includes("地图服务")) {
            mapServices.push(child);
          }
        });
      }
    });
    serviceList.value = mapServices;
  } catch (error) {
    console.error("加载服务列表失败:", error);
    serviceList.value = [];
  }
};

/**
 * @description 加载方案详情
 */
const loadSchemeDetail = async () => {
  const routeValue: any = route.query;
  if (!routeValue && !routeValue.id) return;
  console.log(routeValue.id);
  try {
    const response = await getSchemeDetail(routeValue.id);
    if (response.code === 200 && response.data) {
      const scheme = response.data;

      // 填充表单数据
      formData.id = scheme.id || 0;
      formData.name = scheme.name || "";
      formData.remark = scheme.remark || "";

      console.log("填充表单数据完成:", {
        id: formData.id,
        name: formData.name,
        remark: formData.remark
      });

      // 填充详细信息（用于预览模式）
      schemeDetailInfo.value = scheme;

      // 解析配置值
      if (scheme.configValue) {
        try {
          const config = JSON.parse(scheme.configValue);
          Object.assign(mapConfig, config);

          // 支持新旧数据格式的用户列表解析
          if (config.userInfoList && Array.isArray(config.userInfoList)) {
            // 新格式：使用完整用户信息
            formData.userCodeList = config.userInfoList.map((user: any) => user.code);
          } else if (config.userCodeList && Array.isArray(config.userCodeList)) {
            // 旧格式：仅有用户编码
            formData.userCodeList = config.userCodeList;
          }

          // 解析图层信息
          if (config.selectedLayers) {
            formData.selectedLayers = config.selectedLayers;
            console.log("解析到图层信息:", config.selectedLayers);
          } else {
            console.log("配置中没有图层信息");
          }

          // 解析边界裁剪配置
          if (config.enableBoundaryClip !== undefined) {
            mapConfig.enableBoundaryClip = config.enableBoundaryClip;
            console.log("解析到边界裁剪配置:", config.enableBoundaryClip);
          }

          // 解析裁剪范围配置
          if (config.clipExtent) {
            mapConfig.clipExtent = config.clipExtent;
            console.log("解析到裁剪范围配置:", config.clipExtent);
          }
        } catch (error) {
          console.error("解析配置值失败:", error);
        }
      }

      // 更新地图
      await nextTick();
      updateMapTheme();
      updateMapLabels();
      updateMapCenter();
      updateZoom();

      // 加载选中的图层（确保serviceList已加载）
      if (formData.selectedLayers) {
        console.log("准备加载图层:", formData.selectedLayers);
        console.log("服务列表长度:", serviceList.value.length);

        // 如果服务列表还没加载完成，等待一下
        if (serviceList.value.length === 0) {
          console.log("等待服务列表加载完成...");
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        await loadSelectedLayer(formData.selectedLayers, true);
      }
    }
  } catch (error) {
    console.error("加载方案详情失败:", error);
    ElMessage.error("加载方案详情失败");
  }
};

/**
 * @description 保存方案
 */
const saveScheme = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    // 构建用户完整信息列表（包含label、value、code）
    const userInfoList = (formData.userCodeList || []).map((userCode: any) => {
      const user = userList.value.find((u: any) => u.code === userCode);
      return {
        label: user?.label || userCode,
        value: user?.value || userCode,
        code: userCode
      };
    });

    // 构建完整配置值（包含地图配置、用户完整信息和图层信息）
    const fullConfig = {
      ...mapConfig,
      userCodeList: formData.userCodeList, // 保持向后兼容
      userInfoList, // 新增完整用户信息
      selectedLayers: formData.selectedLayers || "", // 新增选中的图层
      enableBoundaryClip: mapConfig.enableBoundaryClip, // 新增边界裁剪配置
      clipExtent: mapConfig.clipExtent // 新增裁剪范围配置
    };
    const configValue = JSON.stringify(fullConfig);

    const schemeData: MapSchemeDto = {
      ...formData,
      configValue
    };

    let response;
    if (isEditMode.value) {
      response = await updateScheme(schemeData);
    } else {
      delete schemeData.id;
      response = await addScheme(schemeData);
    }

    if (response.code === 200) {
      ElMessage.success(isEditMode.value ? "更新成功" : "保存成功");
      goBack();
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存方案失败:", error);
    ElMessage.error("保存失败，请稍后重试");
  } finally {
    saving.value = false;
  }
};

const getExtentByBbox = (bound: number[]) => {
  try {
    const minx = bound[0];
    const miny = bound[1];
    const maxx = bound[2];
    const maxy = bound[3];
    const mapExtent = boundingExtent([
      [minx, miny],
      [maxx, maxy]
    ]);
    return mapExtent;
  } catch {
    return undefined;
  }
};

/**
 * @description 处理图层选择变化
 */
const handleLayerChange = async () => {
  if (formData.selectedLayers) {
    await loadSelectedLayer(formData.selectedLayers, true);
  }
};
/**
 * @description 重置表单
 */
const resetForm = () => {
  ElMessageBox.confirm("确定要重置表单吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    formRef.value?.resetFields();
    Object.assign(mapConfig, {
      baseLayer: "vector",
      showLabels: true,
      projection: "EPSG:3857",
      center: [103.55, 29.41],
      initialZoom: 13,
      minZoom: 8,
      enableBoundaryClip: false,
      clipExtent: null
    });
    updateMapTheme();
    updateMapLabels();
    updateMapCenter();
    updateZoom();
  });
};

/**
 * @description 编辑方案
 */
const editScheme = () => {
  router.push({
    path: "/expand/mapSchemeAdd",
    query: { id: route.query.id, mode: "edit" }
  });
};

/**
 * @description 返回列表页
 */
const goBack = () => {
  router.push("/expand/mapSchemeManage");
};

/**
 * @description 组件挂载
 */
onMounted(async () => {
  console.log("组件挂载开始");

  // 并行加载用户列表和服务列表
  await Promise.all([loadUserList(), loadServiceList()]);

  console.log("用户列表和服务列表加载完成");
  console.log("服务列表长度:", serviceList.value.length);

  await initMap();
  console.log("地图初始化完成");

  if (isEditMode.value || isPreviewMode.value) {
    console.log("开始加载方案详情");
    await loadSchemeDetail();
  }

  console.log("组件挂载完成");
});
</script>

<style scoped lang="scss">
.scheme-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .header-toolbar {
    height: 60px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 15px;

      .page-title {
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
    }

    .toolbar-right {
      display: flex;
      gap: 10px;
    }
  }

  .content-container {
    flex: 1;
    position: relative;
    overflow: hidden;

    .map-container-full {
      width: 100%;
      height: 100%;
      position: relative;

      #map-container {
        width: 100%;
        height: 100%;
      }

      .floating-config-panel {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 380px;
        max-height: calc(100vh - 140px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        z-index: 1000;
        transition: all 0.3s ease;

        &.panel-collapsed {
          width: 140px;
          .panel-content {
            display: none;
          }
        }

        .panel-header {
          height: 50px;
          padding: 0 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 1px solid rgba(228, 231, 237, 0.6);
          background: rgba(64, 158, 255, 0.05);
          border-radius: 12px 12px 0 0;

          .panel-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .panel-content {
          padding: 16px;
          overflow-y: auto;
          max-height: calc(100vh - 190px);

          .form-section {
            margin-bottom: 20px;

            h4 {
              margin: 0 0 12px 0;
              font-size: 14px;
              color: #303133;
              font-weight: 600;
              border-bottom: 1px solid #409eff;
              padding-bottom: 6px;
            }

            .el-form-item {
              margin-bottom: 16px;
            }
          }

          .center-point-config {
            .el-input {
              width: 100%;
            }
          }

          .form-item-tip {
            font-size: 11px;
            color: #909399;
            margin-left: 8px;
          }

          .boundary-clip-config {
            .clip-switch-row {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 8px;
            }

            .clip-extent-info {
              margin-top: 8px;
              padding: 8px;
              background: #f8f9fa;
              border-radius: 4px;
              border: 1px solid #e9ecef;

              .extent-display {
                margin-bottom: 8px;
                font-size: 11px;

                .extent-label {
                  color: #606266;
                  font-weight: 500;
                }

                .extent-coords {
                  color: #303133;
                  font-family: monospace;
                  word-break: break-all;
                }
              }

              .extent-controls {
                .el-button {
                  font-size: 11px;
                  height: 28px;
                }
              }
            }
          }

          // 预览模式样式
          .scheme-info {
            .info-section {
              margin-bottom: 20px;

              h4 {
                margin: 0 0 12px 0;
                font-size: 14px;
                color: #303133;
                font-weight: 600;
                border-bottom: 1px solid #409eff;
                padding-bottom: 6px;
              }

              .info-item {
                display: flex;
                margin-bottom: 8px;
                font-size: 12px;

                label {
                  min-width: 70px;
                  color: #606266;
                  font-weight: 500;
                }

                span {
                  color: #303133;
                  flex: 1;
                  word-break: break-all;
                }
              }

              .user-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
              }

              .map-controls-preview {
                display: flex;
                flex-direction: column;
                gap: 8px;
              }
            }
          }
        }
      }

      .map-info-panel {
        position: absolute;
        bottom: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 12px;
        font-family: monospace;
        z-index: 999;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// Element Plus 样式覆盖
:deep(.floating-config-panel) {
  .el-form-item__label {
    color: #606266;
    font-weight: 500;
    font-size: 12px;
  }

  .el-input__wrapper {
    border-radius: 6px;
    font-size: 12px;
  }

  .el-select .el-input__wrapper {
    border-radius: 6px;
  }

  .el-textarea__inner {
    border-radius: 6px;
    font-size: 12px;
  }

  .el-button {
    border-radius: 6px;
  }

  .el-slider__runway {
    background-color: #e4e7ed;
  }

  .el-slider__bar {
    background-color: #409eff;
  }

  .el-switch.is-checked .el-switch__core {
    background-color: #409eff;
  }

  .el-slider__button-wrapper {
    // transform: scale(0.8);
  }

  .el-slider__input {
    width: 50px;
  }

  .el-form--small .el-form-item {
    margin-bottom: 12px;
  }
}

// OpenLayers 控件样式
:deep(.ol-control) {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.ol-zoom) {
  top: 20px;
  left: 20px;
}

:deep(.ol-mouse-position) {
  display: none; // 隐藏默认的鼠标位置控件，使用自定义的map-info-panel
}

:deep(.ol-scale-line) {
  bottom: 20px;
  right: 430px; // 避免与浮动面板重叠
  background: rgba(255, 255, 255, 0.9);
  padding: 5px 10px;
  border-radius: 6px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.ol-attribution) {
  bottom: 20px;
  right: 430px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
</style>
