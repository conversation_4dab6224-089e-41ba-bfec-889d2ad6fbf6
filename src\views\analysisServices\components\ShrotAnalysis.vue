<!--
 * @Description: 
 * @Date: 2023-08-01 11:08:40
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-08-08 18:06:04
-->
<template>
  <div class="custom-content">
    <div class="info-bg">
      <div class="custom-table-header-row">
        <div class="custom-table-title">最短路径分析</div>
      </div>
      <div class="box">
        <el-form
          :model="shortInfo"
          ref="formRef"
          :rules="crossRules"
          class="demo-ruleForm custom-sub-form"
          :label-position="'left'"
          status-icon
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="起点" prop="startNodeCode">
                <el-input placeholder="请输入起点" v-model="shortInfo.startNodeCode" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="终点" prop="endNodeCode">
                <el-input placeholder="请输入终点" v-model="shortInfo.endNodeCode" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row>
          <el-col :span="24">
            <div class="custom-dialog-footer">
              <div class="dialog-submit" style="width: 140px" @click="showRequest">
                查看请求参数
              </div>
              <div class="dialog-submit" @click="startAnalysis">提交</div>
            </div>
          </el-col>
        </el-row>
        <div class="result-box" v-if="Object.keys(analysisResult).length > 0">
          <div class="result-title">分析结果</div>
          <div class="result-value">
            <el-input :readonly="true" type="textarea" v-model="analysisResult" />
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :close-on-click-modal="false"
      destroy-on-close
      title="请求参数"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="@/assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">请求参数详情</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <div style="white-space: pre-wrap">{{ showText }}</div>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-submit" @click="dialogVisible = false">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref } from "vue";
import { shrotAnalysis } from "@/api/analysis/network";
import { useRoute } from "vue-router";
const dialogVisible = ref<boolean>(false);
const shortInfo: any = ref({});
const formRef = ref();
const route = useRoute(); // 活跃状态的路由
const crossRules = reactive<FormRules>({
  startNodeCode: [{ required: true, message: "请输入起点", trigger: "blur" }],
  endNodeCode: [{ required: true, message: "请输入终点", trigger: "blur" }]
});
const analysisResult: any = ref("");
const startAnalysis = async () => {
  const data = await formRef.value.validate();
  if (data) {
    shrotAnalysis(route.query.name as string, shortInfo.value).then((res) => {
      ElMessage({
        message: "请求成功",
        type: "success"
      });
      analysisResult.value = JSON.stringify(res);
    });
  }
};
const showText = ref("");
const showRequest = () => {
  showText.value = `{\n startNodeCode:${shortInfo.value.startNodeCode},\n endNodeCode:${shortInfo.value.endNodeCode}\n}`;
  dialogVisible.value = true;
};
</script>
<style lang="scss" scoped>
.box {
  width: 60%;
  border: 1px solid #e6eaef;
  padding: 20px 20px 30px;
}
.info-bg {
  width: 98%;
  padding-bottom: 100px;
  background: #ffffff;
  min-height: 82vh;
  border: 1px solid #e6eaef;
  border-radius: 4px;
  margin-top: 20px;
  padding-left: 2%;
}
:deep(.el-textarea__inner) {
  min-height: 100px !important;
}
.result-value {
  margin-top: 20px;
  width: 98%;
  :deep(.el-textarea__inner) {
    min-height: 280px !important;
  }
}
</style>
