import {
  BaseServiceInfo,
  BaseSetting,
  GeoServer<PERSON>ayer,
  PreviewAddress,
  ServiceType
} from "geoserver-manager";
import { GeocodingInfo } from "./geocodingInfo";
import { deleteService, editService, getServiceByName } from "@/api/geocoding";

/*
 * @Description: 地理编码服务
 * @Autor: silei
 * @Date: 2023-11-28 15:32:26
 * @LastEditors: silei
 * @LastEditTime: 2023-11-28 15:32:31
 */
export class GeocodingService extends BaseServiceInfo {
  get layers(): Array<{ name: string; style: string }> {
    throw new Error("Method not implemented.");
  }

  set layers(value: Array<{ name: string; style: string }>) {
    throw new Error("Method not implemented.");
  }

  private _urls = [] as any[];
  private _changed = false;
  private _enabledServices = ["REST"];
  get visits(): number {
    return this._service.visits ?? 0;
  }

  get errorMsg(): string {
    return this._service.errorMsg ?? "";
  }

  get dateCreated(): Date {
    if (this._service.dateCreated) {
      return new Date(this._service.dateCreated);
    }
    return new Date();
  }

  get dateModified(): Date {
    if (this._service.dateModified) {
      return new Date(this._service.dateModified);
    }
    return this.dateCreated;
  }

  get changed(): boolean {
    return this._changed;
  }

  set changed(value: boolean) {
    this._changed = value;
  }

  get name(): string {
    return this._service?.name;
  }

  get title(): string {
    return this._service?.title as any;
  }

  set title(value: string) {
    this._service && (this._service.title = value);
    this._changed = true;
  }

  get description(): string {
    return this._service?.description as any;
  }

  set description(value: string) {
    this._service && (this._service.description = value);
    this._changed = true;
  }

  get type(): ServiceType {
    return "geocoding" as any;
  }

  get enabledServices(): string[] {
    return this._enabledServices;
  }

  set enabledServices(value: string[]) {
    this._enabledServices = value;
  }

  get supportServices(): string[] {
    return this.getServiceList();
  }

  get serviceTypeString(): string {
    return "地理编码服务";
  }

  get thumbnail(): string {
    return null!;
  }

  get urls(): Array<{ url: string; overviewUrl: string }> {
    return this._urls;
  }

  get enabled(): boolean {
    return this._service?.enabled !== false;
  }

  get cacheLayer(): GeoServerLayer {
    return null!;
  }

  set cacheLayer(value: GeoServerLayer) {}

  get datasource(): { [key: string]: any } {
    const params = this._service.parameters.entry;
    const data = {} as any;
    for (const param of params) {
      data[param["@key"]] = param.$;
    }
    return {
      ...data,
      indexUrl: this._service.indexUrl,
      datasetNames: this._service.datasetNames,
      searchFields: this._service.searchFields,
      filterFields: this._service.filterFields,
      geoDecodingRadius: this._service.geoDecodingRadius
    };
  }

  set datasource(value) {
    // 设置数据源参数
    const params = this._service.parameters.entry;
    this._service.parameters = params.map((param: any) => {
      return {
        $: value[param["@key"]],
        "@key": param["@key"]
      };
    });
    this._service.indexUrl = value.indexUrl;
    this._service.datasetNames = value.datasetNames;
    this._service.searchFields = value.searchFields;
    this._service.filterFields = value.filterFields;
    this._service.geoDecodingRadius = value.geoDecodingRadius;
    this._changed = true;
  }

  private _service: GeocodingInfo;
  constructor() {
    super();
    this._service = {} as any;
  }

  async load(serviceName: string): Promise<BaseServiceInfo> {
    const service = await getServiceByName(serviceName);
    this._service = service.geocoding;
    this._urls = this.getServiceUrl();
    return this;
  }

  from(service: GeocodingInfo): BaseServiceInfo {
    this._service = service;
    this._urls = this.getServiceUrl();
    return this;
  }

  async getPreviewUrl(): Promise<PreviewAddress[]> {
    return [];
  }

  getServiceList() {
    return ["REST"];
  }

  getServiceUrl() {
    const serviceName = this.name;
    const urls = [];
    const basePath = `${BaseSetting.getBaseUrl()}/services/${serviceName}/rest/geocode`;
    urls.push(
      {
        url: `${BaseSetting.getBaseUrl()}/services/${serviceName}/rest/address/geocoding`,
        overviewUrl: `${BaseSetting.getBaseUrl()}/../geocoding/rest/${serviceName}/geocoding`
      },
      {
        url: `${BaseSetting.getBaseUrl()}/services/${serviceName}/rest/address/geodecoding`,
        overviewUrl: `${BaseSetting.getBaseUrl()}/../geocoding/rest/${serviceName}/geodecoding`
      }
    );
    return urls;
  }

  getServiceType() {
    return this.type;
  }

  async setEnabled(value: boolean): Promise<void> {
    this._service.enabled = value;
    await editService(this._service);
  }

  async delete(): Promise<void> {
    const serviceName = this.name;
    // 删除图层组
    await deleteService(serviceName);
  }

  async save() {
    if (this._changed) {
      await editService(this._service);
    }
  }
}
