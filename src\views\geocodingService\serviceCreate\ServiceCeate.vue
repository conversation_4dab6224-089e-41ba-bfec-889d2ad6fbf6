<!--
 * @Description: 服务创建
 * @Autor: silei
 * @Date: 2023-02-02 11:06:08
 * @LastEditors: silei
 * @LastEditTime: 2023-12-01 15:16:32
-->
<template>
  <div>
    <div v-if="currStep.name === '1'">
      <ServiceInfo
        ref="serviceInfoRef"
        v-model="serviceInfo"
        :datasources="datasources"
        :default-type="DatasourceType.SHAPEFILES"
      />
    </div>
    <div v-else-if="currStep.name === '2'">
      <DataSource
        ref="datasourceRef"
        v-model="serviceInfo"
        :datasource-type="serviceInfo.datasource"
      />
    </div>
    <div v-else-if="currStep.name === '3'">
      <GeocodingDataStore
        ref="geocodingRef"
        v-model="serviceInfo"
        :datasource-type="serviceInfo.datasource"
        :connection-parameter="serviceInfo.connectionParameter"
      />
    </div>
    <div v-else-if="currStep.name === '4'">
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务名称">
            <span>{{ serviceInfo.name }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务标题">
            <span>{{ serviceInfo.title }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="服务描述">
            <span>{{ serviceInfo.description }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <el-row class="custom-dialog-footer create-footer">
      <el-button :loading="btnLoading" class="dialog-cancle" @click="cancel">取消</el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== '1'"
        class="dialog-submit"
        @click="getPreStep"
      >
        上一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name !== '4'"
        class="dialog-submit"
        @click="getNextStep"
      >
        下一步
      </el-button>
      <el-button
        :loading="btnLoading"
        v-show="currStep.name === '4'"
        class="dialog-submit"
        @click="createService"
      >
        完成
      </el-button>
    </el-row>
  </div>
</template>
<script lang="ts" setup>
import {
  DatasourceStep,
  DatasourceType,
  Charset,
  ServiceStep,
  ServiceUtil,
  DataSourceFactory,
  BaseService
} from "geoserver-manager";
import { Ref, ref } from "vue";

import { setScoped } from "@/utils/basic";
import ServiceInfo from "@/components/service/ServiceInfo.vue";
import DataSource from "@/components/service/DataSource.vue";
import { ElMessage } from "element-plus";
import { createService as createGeocodingService } from "@/api/geocoding";
import { GeocodingInfo } from "@/interface/business/geocodingInfo";
import GeocodingDataStore from "./GeocodingDataStore.vue";
import { OperatorStep } from "@/components/service/type";
const datasources = [
  {
    name: "文件",
    datasources: [DatasourceType.SHAPEFILES]
  },
  {
    name: "数据库",
    datasources: [DatasourceType.POSTGIS]
  }
];

// 当前步骤
const currStep: Ref<OperatorStep> = ref(new OperatorStep());
const initStep = () => {
  let step = new OperatorStep();
  step.name = "4";
  for (let i = 3; i > 0; i--) {
    step = new OperatorStep(step);
    step.name = i.toString();
  }
  currStep.value = step;
};
initStep();
// 数据源列表
const datastoreOptions = ref({});
const serviceInfo = ref<any>({
  parameters: {},
  services: ["REST"]
} as any);
const emits = defineEmits(["canceled", "completed"]);
const serviceInfoRef = ref();
const datasourceRef = ref();
const layerSelectRef = ref();
const geocodingRef = ref();
const layerData = ref([] as any[]);
const layers = ref<string[] | null>(null);
const connectionParameter = ref({});
/**
 * 获取上一步
 */
const getNextStep = async () => {
  if (currStep.value.name === "1") {
    const service = await serviceInfoRef.value.submitForm();
    if (!service) {
      return;
    }
    serviceInfo.value = service;
  } else if (currStep.value.name === "2") {
    const serviceInfo = await datasourceRef.value.submitForm();
    if (!serviceInfo) {
      return;
    }
  } else if (currStep.value.name === "3") {
    const serviceInfo = await geocodingRef.value.submitForm();
    if (!serviceInfo) {
      return;
    }
  }
  // 选择数据源
  const nextStep = currStep.value.getNextStep();
  if (nextStep) {
    currStep.value = nextStep;
  }
};
/**
 * 获取下一步
 */
const getPreStep = () => {
  const preStep = currStep.value.getPreStep();
  if (preStep) {
    currStep.value = preStep;
  }
};
const btnLoading = ref(false);
/**
 * 创建服务
 */
const createService = async () => {
  btnLoading.value = true;
  try {
    await createGeocodingService({
      ...serviceInfo.value,
      parameters: {
        entry: Object.keys(serviceInfo.value.parameters).map((key) => {
          return { "@key": key, $: serviceInfo.value.parameters[key] };
        })
      }
    } as any);
    emits("completed");
    ElMessage({
      message: "创建成功",
      type: "success"
    });
    setScoped(`服务新增-${serviceInfo.value.name}`);
  } finally {
    btnLoading.value = false;
  }
};
const cancel = () => {
  emits("canceled");
};
</script>
<style scoped lang="scss">
.create-footer {
  margin: 20px 0 0 0;
}
</style>
