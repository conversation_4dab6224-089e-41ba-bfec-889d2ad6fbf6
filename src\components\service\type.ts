export class OperatorStep {
  public name: string = "";
  public component: any;
  public nextStep: OperatorStep | null = null;
  public previousStep: OperatorStep | null = null;
  constructor(nextStep: OperatorStep | undefined = undefined) {
    if (nextStep) {
      this.nextStep = nextStep;
      nextStep.previousStep = this;
    }
  }

  public getNextStep(): OperatorStep | null {
    return this.nextStep;
  }

  public getPreStep(): OperatorStep | null {
    return this.previousStep;
  }
}
