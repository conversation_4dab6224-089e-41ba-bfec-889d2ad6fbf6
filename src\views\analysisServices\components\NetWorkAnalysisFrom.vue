<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-09 14:08:22
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-08-04 15:53:47
-->
<template>
  <el-form
    :rules="serviceRules"
    :model="serviceInfo"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    :label-position="props.labelPosition"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="name" label="服务名称">
          <el-select
            v-model="serviceInfo.name"
            class="custom-dialog-select"
            placeholder="选择数据源"
            @change="handleServerNameChange"
          >
            <el-option
              v-for="(item, index) in serverNameList"
              :key="index"
              :label="item.title"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="nodeName" label="管点图层">
          <el-select
            v-model="serviceInfo.nodeName"
            class="custom-dialog-select"
            placeholder="选择管点图层"
            @change="handlePipePointChanged"
          >
            <el-option
              v-for="(item, index) in layerTypeList"
              :key="index"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="nodeCode" label="管点编码">
          <el-select
            v-model="serviceInfo.nodeCode"
            class="custom-dialog-select"
            placeholder="选择管点编码"
          >
            <el-option
              v-for="(item, index) in pipePointCodeList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="edgeName" label="管线图层">
          <el-select
            v-model="serviceInfo.edgeName"
            class="custom-dialog-select"
            placeholder="选择管线图层"
            @change="handlePipeLineChanged"
          >
            <el-option
              v-for="(item, index) in layerTypeList"
              :key="index"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="edgeCode" label="管线编码">
          <el-select
            v-model="serviceInfo.edgeCode"
            class="custom-dialog-select"
            placeholder="选择管线编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="startNodeCode" label="起点节点编码">
          <el-select
            v-model="serviceInfo.startNodeCode"
            class="custom-dialog-select"
            placeholder="选择起点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="endNodeCode" label="终点节点编码">
          <el-select
            v-model="serviceInfo.endNodeCode"
            class="custom-dialog-select"
            placeholder="选择终点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="startElevation" label="起点高程">
          <el-select
            v-model="serviceInfo.startElevation"
            class="custom-dialog-select"
            placeholder="选择终点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="endElevation" label="终点高程">
          <el-select
            v-model="serviceInfo.endElevation"
            class="custom-dialog-select"
            placeholder="选择终点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="startDeepth" label="起点埋深">
          <el-select
            v-model="serviceInfo.startDeepth"
            class="custom-dialog-select"
            placeholder="选择终点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="endDeepth" label="终点埋深">
          <el-select
            v-model="serviceInfo.endDeepth"
            class="custom-dialog-select"
            placeholder="选择终点节点编码"
          >
            <el-option
              v-for="(item, index) in pipeLineList"
              :key="index"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="hasDirection" label="管线方向">
          <el-switch
            inline-prompt
            active-text="双向"
            inactive-text="单向"
            v-model="serviceInfo.hasDirection"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { getServices, getVectorInfo } from "@/api/analysis/network";
import { reactive, ref, onMounted, watch } from "vue";
import { FormRules } from "element-plus";
const props = defineProps({
  labelPosition: {
    type: String,
    default: () => "top"
  },
  info: {
    type: Object,
    default: () => {}
  }
});
watch(
  () => props.info,
  (newValue) => {
    serviceInfo.value = newValue;
    review();
  }
);
const serviceInfo: any = ref({});
const nameChecked = (rule: any, value: any, callback: any) => {
  if (/.*[\u4e00-\u9fa5]+.*$/.test(value)) {
    callback(new Error("不能出现中文字符，请重新输入"));
  } else if (!value) {
    callback(new Error("服务名称不能为空"));
  } else {
    callback();
  }
};
const serviceRules = reactive<FormRules>({
  name: [{ required: true, validator: nameChecked, trigger: "change" }],
  nodeName: [{ required: true, message: "请选择管点图层", trigger: "change" }],
  nodeCode: [{ required: true, message: "请选择管点编码", trigger: "change" }],
  edgeName: [{ required: true, message: "请选择管线图层", trigger: "change" }],
  edgeCode: [{ required: true, message: "请选择管线编码", trigger: "change" }],
  startNodeCode: [{ required: true, message: "请选择起点节点编码", trigger: "change" }],
  endNodeCode: [{ required: true, message: "请选择终点节点编码", trigger: "change" }]
});
const handlePipePointChanged = (value: string) => {
  if (layerTypeList.value.length > 0) {
    layerTypeList.value.forEach((layerItem: any) => {
      if (layerItem.name === value) {
        pipePointCodeList.value = layerItem.fields;
      }
    });
    serviceInfo.value.nodeCode = "";
  }
};
const handlePipeLineChanged = (value: string) => {
  if (layerTypeList.value.length > 0) {
    layerTypeList.value.forEach((layerItem: any) => {
      if (layerItem.name === value) {
        pipeLineList.value = layerItem.fields;
      }
    });
    serviceInfo.value.edgeCode = "";
    serviceInfo.value.startNodeCode = "";
    serviceInfo.value.endNodeCode = "";
  }
};
const layerTypeList: any = ref([]);
const handleServerNameChange = async (value: string) => {
  serverNameList.value.forEach((serverItem: any) => {
    if (serverItem.name === value) {
      serviceInfo.value.title = serverItem.title;
    }
  });
  const result = await getVectorInfo(value);
  layerTypeList.value = result;
  serviceInfo.value.edgeName = "";
  serviceInfo.value.edgeCode = "";
  serviceInfo.value.nodeName = "";
  serviceInfo.value.nodeCode = "";
  serviceInfo.value.startNodeCode = "";
  serviceInfo.value.endNodeCode = "";
};
const pipePointCodeList: any = ref([]);
const pipeLineList: any = ref([]);
const formRef = ref();
const serverNameList: any = ref([]);
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      if (vaild) {
        resolve(serviceInfo.value);
      } else {
        resolve(null);
      }
    });
  });
};
defineExpose({
  submitForm
});
const initData = async () => {
  const result = await getServices();
  serverNameList.value = result;
};
const review = async () => {
  if (Object.keys(props.info).length !== 0) {
    const result = await getVectorInfo(props.info.name);
    layerTypeList.value = result;
    layerTypeList.value.forEach((layerItem: any) => {
      if (layerItem.name === props.info.nodeName) {
        pipePointCodeList.value = layerItem.fields;
      }
      if (layerItem.name === props.info.edgeName) {
        pipeLineList.value = layerItem.fields;
      }
    });
  }
};
onMounted(async () => {
  await initData();
});
</script>
