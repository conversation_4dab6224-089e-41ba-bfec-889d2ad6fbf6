/*
 * @Description: 服务样式接口
 * @Autor: xyq
 * @Date: 2023-02-16 13:50:50
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-22 14:22:42
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
export const getStyles = async () => {
  const result: any = await hRequest.get<DataType>({
    url: "/styleManage/styles",
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 风格添加
export const styleStyles = async (name: string, data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: `/styleManage/styles/${name}`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 风格详情获取
export const styleDetials = async (name: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/styleManage/styles/${name}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 风格样式修改
export const styleEdit = async (name: string, data: {}) => {
  const result: any = await hRequest.put<DataType>({
    url: `/styleManage/styles/${name}`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 新增样式
export const importStyle = async (data: {}) => {
  const result: any = await hRequest.post<DataType>({
    url: "/styles",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 修改样式
export const importPutStyle = async (
  style: string,
  data: string,
  type = "application/vnd.ogc.sld+xml"
) => {
  const result: any = await hRequest.put<DataType>({
    url: `/styles/${style}`,
    headers: {
      "Content-Type": type
    },
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
export const downloadStyle = async (style: string, type = "application/vnd.ogc.sld+xml") => {
  const result: any = await hRequest.get<DataType>({
    url: `/styles/${style}`,
    headers: {
      Accept: type
    },
    maxRedirects: 0,
    timeout: 60000
  });
  return result;
};
// 修改样式
export const importPutZipStyle = async (style: string, data: any) => {
  const result: any = await hRequest.put<DataType>({
    url: `/styles/${style}`,
    headers: {
      "Content-Type": "application/zip"
    },
    data,
    maxRedirects: 0,
    timeout: 60000
  });
  return result;
};

// 样式详情
export const styleDetial = async (style: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/styles/${style}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
// 样式详情
export const styleDetialXml = async (style: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/styles/${style}`,
    maxRedirects: 0,
    headers: {
      Accept: "application/vnd.ogc.sld+xml"
    },
    timeout: 10000
  });
  return result;
};
export const delStyle = async (style: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/styles/${style}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
