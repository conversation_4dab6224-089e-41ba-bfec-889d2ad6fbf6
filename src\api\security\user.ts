/*
 * @Description: 用户
 * @Autor: xyq
 * @Date: 2023-01-31 13:50:50
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-13 11:49:07
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
/**
 * 获取角色列表
 * @returns all
 */
export const getUserList = async () => {
  const result = await hRequest.get<DataType>({
    url: "/authManage/users",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加用户
 * @returns all
 */
export const addUser = async (data: object) => {
  const result = await hRequest.post<DataType>({
    url: "/security/usergroup/users",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 修改用户状态
 * @returns all
 */
export const editUserStaue = async (name: string, enabled: number) => {
  const result = await hRequest.put<DataType>({
    url: `authManage/userEnabled/${name}?enabled=${enabled}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加用户
 * @returns all
 */
export const modifyUser = async (user: string, data: object) => {
  const result = await hRequest.post<DataType>({
    url: `/security/usergroup/user/${user}`,
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 删除用户
 * @returns all
 */
export const delUser = async (user: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/usergroup/user/${user}`,
    maxRedirects: 0,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    timeout: 10000
  });
  return result;
};
/**
 * 删除用户
 * @returns all
 */
export const detUserGroups = async (user: string) => {
  const result = await hRequest.get<DataType>({
    url: `/security/usergroup/user/${user}/groups`,
    maxRedirects: 0,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    timeout: 10000
  });
  return result;
};
