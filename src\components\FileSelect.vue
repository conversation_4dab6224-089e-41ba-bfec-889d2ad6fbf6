<!--
 * @Description: 文件选择
 * @Autor: silei
 * @Date: 2023-03-16 10:41:52
 * @LastEditors: silei
 * @LastEditTime: 2023-10-25 14:49:15
-->
<template>
  <div v-loading="loading">
    <el-row :gutter="32">
      <el-col :span="3">
        <div class="icon-box">
          <el-icon :size="18" class="back" @click="backPath()">
            <Back />
          </el-icon>
        </div>
      </el-col>
      <el-col :span="16">
        <DirectoryInput v-model="currentPath" @change="handlePathChanged" />
        <!-- <div class="select" v-if="menuShow" @click="menuShowChange">
          <el-input></el-input>
          <div class="upFileBox">
            <div ref="myRef" v-if="!overWidth" class="content">
              <div
                v-for="(item, index) in upFileList"
                class="menu-item"
                @click.stop="handlePathChanged(item)"
                :key="index"
              >
                {{ item }}>
              </div>
            </div>
            <div ref="myRef" v-if="overWidth" class="content">
              <div class="menu-item">
                {{ upFileList[upFileList.length - 2] }}
              </div>
              >
              <div class="menu-item">
                {{ upFileList[upFileList.length - 1] }}
              </div>
            </div>
          </div>
        </div>
        <el-input
          v-if="!menuShow"
          v-model="currentPath"
          @change="inputPathChanged"
          @blur="inputPathChanged"
        /> -->
      </el-col>
      <el-col :span="4">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
          :show-file-list="false"
          accept=".zip"
          :on-change="handleUpload"
          :auto-upload="false"
        >
          <template #trigger>
            <div class="opload-btn">
              <img src="../assets/img/upload.png" alt="" />
              <span>上传</span>
            </div>
          </template>
        </el-upload>
      </el-col>
    </el-row>

    <el-table
      :data="pathInfos"
      style="width: 100%; height: 400px"
      @row-click="handlePathClick"
      @row-dblclick="handlePathDbClick"
    >
      <el-table-column prop="name">
        <template #default="scope">
          <div style="display: flex; align-items: center; cursor: pointer">
            <img
              class="file-icon"
              v-if="scope.row.type === 'file'"
              src="../assets/img/file.png"
              alt=""
            />
            <img
              class="file-icon"
              v-if="scope.row.type === 'dir'"
              src="../assets/img/filedesk.png"
              alt=""
            />
            <span>{{ scope.row.name === "" ? scope.row.path : scope.row.name }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-row class="row-p-d" justify="space-between">
      <div class="selectFile">
        <div class="title">文件名称</div>
        <el-input v-model="selectPath" />
      </div>
    </el-row>
    <span class="custom-dialog-footer">
      <div class="dialog-cancle" @click="submitSelect('')">取消</div>
      <div class="dialog-submit" @click="submitSelect(selectPath)">确定</div>
    </span>
  </div>
</template>
<script lang="ts" setup>
import { getPath, uploadZip } from "@/api/resource";
import { onMounted, Ref, ref } from "vue";
import { Back } from "@element-plus/icons-vue";
import { ElMessage, UploadFile } from "element-plus";
import localCache from "@/utils/auth";
import DirectoryInput from "./DirectoryInput.vue";
const props = defineProps({
  path: String,
  chooseType: {
    type: String,
    default: "dir"
  }, // file 或 dir
  accept: {
    type: String,
    default: ""
  }
});
interface PathInfo {
  name: string;
  path: string;
  type: string;
}
const pathInfos: Ref<PathInfo[]> = ref([]);
// 文件选择key
const cacheKey = "FILEPATH";
const queryPath = async (path: string, accept: string) => {
  if (path) {
    currentPath.value = path;
  }
  const data = await getPath(path, accept);
  pathInfos.value = data;
};
const selectPath = ref("");
const currentPath = ref("");
const loading = ref(false);
const emits = defineEmits(["fileSelected"]);

const handlePathDbClick = (row: PathInfo) => {
  // console.log("列表的高度是：", myRef.value?.clientWidth);
  // let width = 0;
  // if (myRef.value?.clientWidth) {
  //   width = myRef.value?.clientWidth;
  //   if (width > 250) {
  //     overWidth.value = true;
  //   }
  // }
  const path = row;
  if (path.type === "dir") {
    if (path.type === props.chooseType) {
      selectPath.value = path.path;
    }
    // const splitPath = path.path.split("\\");
    // if (splitPath[1] === "") {
    //   splitPath.pop();
    // }
    // upFileList.value = splitPath;
    queryPath(path.path, props.accept);
  } else {
    // 确定选择
    selectPath.value = path.path;
    submitSelect(selectPath.value);
  }
};
const submitSelect = (result: string) => {
  localCache.setCache(cacheKey, currentPath.value);
  emits("fileSelected", result);
};
const handlePathClick = (row: PathInfo) => {
  const path = row;
  if (path.type === props.chooseType) {
    selectPath.value = path.path;
  }
};
const handlePathChanged = (value: string) => {
  queryPath(currentPath.value, props.accept);
};
const backPath = () => {
  if (!currentPath.value) {
    return;
  }
  const splitter = currentPath.value.includes("/") ? "/" : "\\";
  const position = currentPath.value.endsWith(splitter)
    ? currentPath.value.length - 1
    : currentPath.value.length - 2;
  let path = currentPath.value;
  while (path.endsWith(splitter)) {
    path = path.substring(0, path.length - 2);
  }
  const index = path.lastIndexOf(splitter, position);
  currentPath.value = path.substring(0, index + 1);
  queryPath(currentPath.value, props.accept);
};
const handleUpload = (uploadFile: UploadFile) => {
  if (!uploadFile.name.endsWith("zip")) {
    ElMessage({
      message: "只能上传zip文件",
      type: "success"
    });
    return;
  }
  // 上传文件
  loading.value = true;
  uploadZip(uploadFile.raw!, currentPath.value).finally(() => {
    queryPath(currentPath.value, props.accept);
    loading.value = false;
  });
};
onMounted(() => {
  // 获取缓存的路径
  let path = localCache.getCache(cacheKey) ?? "";
  path = props.path ?? path;
  currentPath.value = path;
  queryPath(currentPath.value, props.accept);
});
</script>
<style lang="scss" scoped>
.breadcrumb-text {
  color: blue;
}
.menu-item {
  margin-left: 5px;
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Regular;
  font-weight: 400;
  color: #232a3a;
}
.menu-item:hover {
  cursor: pointer;
  border-bottom: 1px solid;
}
.icon-box {
  width: 100%;
  border-right: 1px solid #e9edf1;
  height: 50%;
  margin: 8px 0 0 0;
  .back {
    text-align: start;
    cursor: pointer;
    margin: -2px 0 0 0;
  }
}
.select {
  position: relative;
}
.upFileBox {
  top: 5px;
  left: 14px;
  position: absolute;
  .content {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
  }
}
.opload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 32px;
  background: #4076f3;
  border-radius: 16px;
  img {
    width: 16px;
    height: 13px;
    margin: 0 5px 0 0;
  }
  span {
    height: 32px;
    line-height: 32px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: #ffffff;
  }
}
.row-p-d {
  margin: 8px 0;
}
.selectFile {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  .title {
    width: 80px;
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    color: #7d8da1;
    margin: 0 10px 0 0;
  }
  span {
    font-size: 16px;
    font-family: Source Han Sans CN, Source Han Sans CN-Regular;
    font-weight: 400;
    text-align: left;
    color: #232a3a;
  }
}
.file-icon {
  width: 20px;
  height: 20px;
  margin: 0 6px 0 0;
}
</style>
