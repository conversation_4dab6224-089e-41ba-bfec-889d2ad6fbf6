<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-03 16:00:16
 * @LastEditors: silei
 * @LastEditTime: 2023-12-01 15:19:06
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="24">
        <el-form-item prop="indexUrl" label="索引目录">
          <el-input v-model="value.indexUrl" class="file-input" placeholder="请选择文件目录">
          </el-input>
          <el-button @click="handleSelect('index')">远程浏览</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="datasetNames" label="数据集">
          <el-select
            v-model="value.datasetNames"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            class="custom-dialog-select"
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择服务接口"
            @change="handleDatasetChange"
          >
            <el-option
              v-for="item in datasets"
              :key="item.name"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="searchFields" label="查询字段">
          <el-select
            v-model="value.searchFields"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            class="custom-dialog-select"
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择服务接口"
          >
            <el-option
              v-for="item in fields"
              :key="item.fieldName"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="filterFields" label="过滤字段">
          <el-select
            v-model="value.filterFields"
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            class="custom-dialog-select"
            default-first-option
            :reserve-keyword="false"
            placeholder="请选择服务接口"
          >
            <el-option
              v-for="item in fields"
              :key="item.fieldName"
              :label="item.fieldName"
              :value="item.fieldName"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="geoDecodingRadius" label="查询半径">
          <el-input-number v-model="value.geoDecodingRadius" placeholder="查询半径" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <el-dialog v-model="dialogVisible" title="选择文件" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../../../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">选择文件</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <FileSelect choose-type="dir" v-if="dialogVisible" @file-selected="handleFileSelected" />
  </el-dialog>
</template>
<script lang="ts" setup>
import { computed, onMounted, PropType, reactive, ref } from "vue";
import { FormRules } from "element-plus";
import FileSelect from "@/components/FileSelect.vue";
import { BaseService, DatasourceType } from "geoserver-manager";
const rules = reactive<FormRules>({
  indexUrl: [{ required: true, message: "请输入索引", trigger: "blur" }],
  datasetNames: [{ required: true, message: "请选择数据集", trigger: "blur" }],
  searchFields: [{ required: true, message: "请选择查询字段", trigger: "blur" }],
  filterFields: [{ required: true, message: "请输入过滤字段", trigger: "blur" }]
});
const props = defineProps<{
  modelValue: any;
}>();
const datasets = ref<any[]>([]);
const fields = ref<any[]>([]);
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
onMounted(() => {
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.port = props.modelValue.port ?? "5432";
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.dbtype = "postgis";
});
const dialogVisible = ref(false);
const selectType = ref("config");
const chooseType = ref("file");
const accept = ref(".xml");
const handleSelect = (type: string) => {
  dialogVisible.value = true;
  selectType.value = type;
  chooseType.value = "dir";
  accept.value = "";
};
const initData = async () => {
  // 更新字段列表
  const dataStore = BaseService.getDataStore(value.value.type, "index", value.value.parameters);
  const data = await dataStore.getDataInfos();
  datasets.value = data;
  handleDatasetChange();
};
initData();
const handleDatasetChange = () => {
  fields.value = datasets.value
    .filter((item) => value.value.datasetNames.includes(item.name))
    .flatMap((item) => item.fields);
};
const handleFileSelected = async (fileName: string) => {
  dialogVisible.value = false;
  value.value.indexUrl = fileName;
};
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      value.value.datasetNames = value.value.datasetNames.join(",");
      value.value.searchFields = value.value.searchFields.join(",");
      value.value.filterFields = value.value.filterFields.join(",");
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
