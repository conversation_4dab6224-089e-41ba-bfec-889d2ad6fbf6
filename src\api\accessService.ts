import hRequest from "@/utils/http/request_server/hRequest";
import { DataType } from "@/utils/http/types";

/**
 * @description 矢量瓦片分页查询
 * @param {any} data 查询参数
 * @returns {Promise<any>} 分页查询结果
 */
export const accessPage = async (data: any) => {
  const result: any = await hRequest.get<DataType>({
    url: "/business/service/access/page",
    params: data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 新增矢量瓦片
 * @param {any} data 矢量瓦片数据
 * @returns {Promise<any>} 新增操作结果
 */
export const addAccess = async (data: any) => {
  const result: any = await hRequest.post<DataType>({
    url: "/business/service/access",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 修改矢量瓦片
 * @param {any} data 矢量瓦片数据
 * @returns {Promise<any>} 修改操作结果
 */
export const editAccess = async (data: any) => {
  const result: any = await hRequest.put<DataType>({
    url: "/business/service/access",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 获取矢量瓦片详情
 * @param {string} id 矢量瓦片ID
 * @returns {Promise<any>} 矢量瓦片详情
 */
export const detailAccess = async (id: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/business/service/access/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 删除矢量瓦片
 * @param {string} id 矢量瓦片ID
 * @returns {Promise<any>} 删除操作结果
 */
export const delAccess = async (id: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/business/service/access/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
