import hRequest from "@/utils/http/request_server/hRequest";
import { DataType } from "@/utils/http/types";
import type { CityInfo } from "@/interface/business/serviceAccess";

/**
 * @description 服务接入分页查询
 * @param {any} data 查询参数
 * @returns {Promise<any>} 分页查询结果
 */
export const accessPage = async (data: any) => {
  const result: any = await hRequest.get<DataType>({
    url: "/business/service/access/page",
    params: data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 新增服务接入
 * @param {any} data 服务接入数据
 * @returns {Promise<any>} 新增操作结果
 */
export const addAccess = async (data: any) => {
  const result: any = await hRequest.post<DataType>({
    url: "/business/service/access",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 修改服务接入
 * @param {any} data 服务接入数据
 * @returns {Promise<any>} 修改操作结果
 */
export const editAccess = async (data: any) => {
  const result: any = await hRequest.put<DataType>({
    url: "/business/service/access",
    data,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 获取服务接入详情
 * @param {string} id 服务接入ID
 * @returns {Promise<any>} 服务接入详情
 */
export const detailAccess = async (id: string) => {
  const result: any = await hRequest.get<DataType>({
    url: `/business/service/access/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 删除服务接入
 * @param {string} id 服务接入ID
 * @returns {Promise<any>} 删除操作结果
 */
export const delAccess = async (id: string) => {
  const result: any = await hRequest.delete<DataType>({
    url: `/business/service/access/${id}`,
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};

/**
 * @description 获取城市列表
 * @returns {Promise<CityInfo[]>} 城市列表
 */
export const getCityList = async (): Promise<CityInfo[]> => {
  // 暂时返回模拟数据，后续可以接入真实的城市API
  const mockCities: CityInfo[] = [
    { code: "110000", name: "北京市", province: "北京市", longitude: 116.4074, latitude: 39.9042 },
    { code: "310000", name: "上海市", province: "上海市", longitude: 121.4737, latitude: 31.2304 },
    { code: "440100", name: "广州市", province: "广东省", longitude: 113.2644, latitude: 23.1291 },
    { code: "440300", name: "深圳市", province: "广东省", longitude: 114.0579, latitude: 22.5431 },
    { code: "330100", name: "杭州市", province: "浙江省", longitude: 120.1551, latitude: 30.2741 },
    { code: "320100", name: "南京市", province: "江苏省", longitude: 118.7969, latitude: 32.0603 },
    { code: "510100", name: "成都市", province: "四川省", longitude: 104.0665, latitude: 30.5723 },
    { code: "420100", name: "武汉市", province: "湖北省", longitude: 114.3054, latitude: 30.5928 }
  ];

  return Promise.resolve(mockCities);
};

/**
 * @description 根据城市名称获取坐标信息
 * @param {string} cityName 城市名称
 * @returns {Promise<{longitude: number, latitude: number} | null>} 坐标信息
 */
export const getCityCoordinates = async (
  cityName: string
): Promise<{ longitude: number; latitude: number } | null> => {
  const cities = await getCityList();
  const city = cities.find((c) => c.name === cityName);

  if (city) {
    return {
      longitude: city.longitude,
      latitude: city.latitude
    };
  }

  return null;
};
