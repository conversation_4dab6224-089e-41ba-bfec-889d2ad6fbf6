/*
 * @Description: 令牌
 * @Autor: silei
 * @Date: 2023-03-03 09:32:22
 * @LastEditors: silei
 * @LastEditTime: 2023-04-18 18:04:37
 */
import { TokenInfo } from "@/interface/security/token";
import hRequest from "@/utils/http";
/**
 * 获取token
 * @returns all
 */
export const createToken = async (token: TokenInfo) => {
  const result = await hRequest.post<string>({
    url: "/token",
    data: {
      tokenInfo: token
    }
  });
  return result;
};
export const createTemToken = async (userName: string, referer: string) => {
  const result = await hRequest.post<string>({
    url: "/token/tem",
    params: {
      userName,
      referer
    }
  });
  return result;
};
