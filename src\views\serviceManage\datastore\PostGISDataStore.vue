<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-03-03 16:00:16
 * @LastEditors: silei
 * @LastEditTime: 2023-03-09 15:51:29
-->
<!--
 * @Description: 
 * @Autor: silei
 * @Date: 2023-02-15 15:34:50
 * @LastEditors: silei
 * @LastEditTime: 2023-02-15 15:39:09
-->
<template>
  <el-form
    :rules="rules"
    :model="value"
    ref="formRef"
    class="demo-ruleForm custom-sub-form"
    label-position="top"
    status-icon
  >
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="host" label="地址">
          <el-input v-model="value.host" placeholder="地址"> </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="port" label="端口号">
          <el-input v-model="value.port" placeholder="端口号"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="database" label="数据库">
          <el-input v-model="value.database" placeholder="数据库"> </el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="user" label="用户名">
          <el-input v-model="value.user" placeholder="用户名"> </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="32">
      <el-col :span="12">
        <el-form-item prop="passwd" label="密码">
          <el-input v-model="value.passwd" placeholder="密码" type="password" show-password>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script lang="ts" setup>
import { computed, onMounted, PropType, reactive, ref } from "vue";
import { FormRules } from "element-plus";
const rules = reactive<FormRules>({
  host: [{ required: true, message: "请输入地址", trigger: "blur" }],
  port: [{ required: true, message: "请输入端口号", trigger: "blur" }],
  database: [{ required: true, message: "请输入数据库", trigger: "blur" }],
  user: [{ required: true, message: "请输入用户", trigger: "blur" }],
  passwd: [{ required: true, message: "请输入密码", trigger: "blur" }]
});
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {
      return {
        host: "",
        port: "5432"
      };
    }
  }
});
const emits = defineEmits(["update:modelValue"]);
const value = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emits("update:modelValue", value);
  }
});
onMounted(() => {
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.port = props.modelValue.port ?? "5432";
  // eslint-disable-next-line vue/no-mutating-props
  props.modelValue.dbtype = "postgis";
});
const formRef = ref();
const submitForm = () => {
  return new Promise((resolve) => {
    formRef.value.validate((vaild: boolean) => {
      resolve(vaild);
    });
  });
};
defineExpose({
  submitForm
});
</script>
