/*
 * @Description: 用户组
 * @Autor: xyq
 * @Date: 2023-01-31 13:50:50
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-13 13:59:01
 */
import hRequest from "@/utils/http";
import { DataType } from "@/utils/http/types";
/**
 * 获取角色列表
 * @returns all
 */
export const getGroupsList = async () => {
  const result = await hRequest.get<DataType>({
    url: "/authManage/userGroups",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加用户组
 * @returns all
 */
export const addGroups = async (group: string) => {
  const result = await hRequest.post<DataType>({
    url: `/security/usergroup/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 添加用户组
 * @returns all
 */
export const delGroups = async (group: string) => {
  const result = await hRequest.delete<DataType>({
    url: `/security/usergroup/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 获取用户组下的用户
 * @returns all
 */
export const getGroupsUser = async (group: string) => {
  const result = await hRequest.get<DataType>({
    url: `/security/usergroup/group/${group}/users`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
/**
 * 获取用户组下的角色
 * @returns all
 */
export const getGroupsRole = async (group: string) => {
  const result = await hRequest.get<DataType>({
    url: `/security/roles/group/${group}`,
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    maxRedirects: 0,
    timeout: 10000
  });
  return result;
};
