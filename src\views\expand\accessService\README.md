# 服务接入预览功能说明

## 功能概述

服务接入预览功能允许用户在新窗口中预览配置的地图服务，支持天地图、百度地图、高德地图以及第三方地图瓦片服务。

## 主要特性

### 1. 多服务类型支持
- **天地图**: 支持CGCS2000坐标系，需要配置token
- **百度地图**: 支持BD09坐标系，需要配置ak参数
- **高德地图**: 支持GCJ02坐标系，需要配置key参数
- **第三方地图**: 支持自定义配置，如OpenStreetMap

### 2. 智能URL解析
- 自动替换瓦片URL中的占位符：
  - `{token}`, `{key}`, `{ak}`: 服务密钥
  - `{z}`, `{x}`, `{y}`: 瓦片坐标
  - `{0-7}`, `{1-4}`, `{0-3}`: 随机服务器编号

### 3. 地图功能
- 基于OpenLayers的地图渲染
- 支持不同坐标系自动转换
- 根据城市坐标自动定位中心点
- 地图控件：比例尺、鼠标位置显示

### 4. 错误处理
- 瓦片加载失败提示
- 网络连接错误处理
- 配置解析错误提示
- 自动降级到测试数据

### 5. 用户体验
- 响应式设计，支持移动端
- 加载状态显示
- 服务信息详情查看
- 多种测试模式

## 使用方法

### 1. 基本预览
1. 在服务接入管理页面，点击任意服务的"预览"按钮
2. 系统会在新窗口中打开预览页面
3. 地图会根据服务配置自动加载

### 2. 预览页面操作
- **返回**: 返回上一页
- **刷新地图**: 重新加载地图
- **服务信息**: 查看详细的服务配置信息

## 配置示例

### 天地图配置
```javascript
{
  serviceType: "tianditu",
  cityName: "北京市",
  adminArea: "朝阳区",
  tileUrl: "https://t{0-7}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk={token}",
  serviceKey: "your_tianditu_token"
}
```

### 高德地图配置
```javascript
{
  serviceType: "amap",
  cityName: "上海市", 
  adminArea: "浦东新区",
  tileUrl: "https://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scale=1&style=8&x={x}&y={y}&z={z}&key={token}",
  serviceKey: "your_amap_key"
}
```

### OpenStreetMap配置
```javascript
{
  serviceType: "custom",
  cityName: "广州市",
  adminArea: "天河区", 
  tileUrl: "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
  serviceKey: "" // OSM不需要密钥
}
```

## 技术实现

### 前端技术栈
- Vue 3 + TypeScript
- OpenLayers 地图库
- Element Plus UI组件
- SCSS样式

### 核心组件
- `ServiceAccessPreview.vue`: 预览组件主体
- `ServiceAccessInfo.vue`: 服务配置表单
- `index.vue`: 服务管理主页面

### API接口
- `detailAccess(id)`: 获取服务详情
- `getCityList()`: 获取城市列表
- `getCityCoordinates(cityName)`: 获取城市坐标

## 故障排除

### 常见问题
1. **地图无法加载**: 检查瓦片URL和服务密钥配置
2. **瓦片显示错误**: 确认坐标系配置正确
3. **网络连接失败**: 检查网络连接和服务器状态
4. **配置解析错误**: 验证JSON格式是否正确

### 调试方法
1. 打开浏览器开发者工具查看控制台错误
2. 检查网络请求是否成功
3. 使用测试模式验证功能
4. 查看服务信息弹窗确认配置

## 扩展开发

### 添加新的地图服务类型
1. 在 `ServiceType` 枚举中添加新类型
2. 更新 `getProjectionConfig` 函数
3. 添加相应的URL模板解析逻辑
4. 更新表单配置选项

### 自定义地图控件
1. 在 `initMap` 函数中添加新的控件
2. 使用OpenLayers的控件API
3. 添加相应的样式定义

## 版本历史

- v1.0.0: 基础预览功能实现
- v1.1.0: 添加多服务类型支持
- v1.2.0: 完善错误处理和用户体验
- v1.3.0: 添加响应式设计和移动端支持
