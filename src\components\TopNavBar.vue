<!--
 * @Description: 
 * @Date: 2022-09-21 14:52:55
 * @Author: GISerZ
 * @LastEditors: Tanqy
 * @LastEditTime: 2024-04-09 10:03:23
-->
<template>
  <div class="header">
    <div class="mid">
      <img src="../assets/img/earth.png" />
      <span>{{ title }}</span>
    </div>
    <div class="right">
      <div class="tiems">
        <div class="el-dropdown-link">
          <img src="../assets/img/logo.png" alt="" />
          <div class="userName">{{ realName }}</div>
        </div>
        <!-- <el-dropdown trigger="click" class="dropdown" popper-class="custom-user-dropdown">
          <div class="el-dropdown-link">
            <img src="../assets/img/logo.png" alt="" />
            <div class="userName">{{ realName }}</div>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="logout">
              <el-dropdown-item @click="handleCountSet"> {{ "账户设置" }}</el-dropdown-item>
              <el-dropdown-item @click="handleToken"> {{ "令牌" }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
        <div class="p-d"></div>
        <div class="loginOut" @click="handleExit"><img src="../assets/img/exit.png" alt="" /></div>
      </div>
    </div>
  </div>
  <el-dialog v-model="dialogVisible" title="获取令牌" width="34%">
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">获取令牌</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <TokenCreate v-if="dialogVisible" />
    <template #footer>
      <span class="custom-dialog-footer">
        <div class="dialog-cancle" @click="dialogVisible = false">取消</div>
        <div class="dialog-submit" @click="dialogVisible = false">确定</div>
      </span>
    </template>
  </el-dialog>
  <el-dialog
    v-model="countDialogVisible"
    :before-close="handleCloseAccount"
    title="账户修改"
    width="34%"
  >
    <template #header="{ titleId, titleClass }">
      <div class="custom-dialog-header">
        <img src="../assets/img/dialog-icon.png" alt="" />
        <h4 :id="titleId" :class="titleClass">账户修改</h4>
      </div>
    </template>
    <div class="custom-split-line"></div>
    <el-form
      ref="accountFormRef"
      :model="countForm"
      :rules="rules"
      class="demo-ruleForm custom-sub-form"
      label-position="top"
    >
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="账号名称" prop="name">
            <el-input :disabled="true" v-model="countForm.name" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号密码" prop="password">
            <el-input v-model="countForm.password" type="password" placeholder="请输入密码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="32">
        <el-col :span="12">
          <el-form-item label="确认密码" prop="checkPass">
            <el-input
              v-model="countForm.checkPass"
              type="password"
              placeholder="请再次输入密码"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="custom-dialog-footer">
        <div class="dialog-cancle" @click="countDialogVisible = false">取消</div>
        <div class="dialog-submit" @click="submitForm(accountFormRef)">确定</div>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { modifyUser } from "@/api/security/user";
import router from "@/router";
import TokenCreate from "@/views/security/token/TokenCreate.vue";
import { onMounted, ref, reactive, computed } from "vue";
import type { FormRules, FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
const store = useStore();
const realName = computed(() => store.state.login.userName);
const handleExit = () => {
  // localCatch.clearCache();
  return router.replace("/login");
};
const title = ref(process.env.VUE_APP_NAME);
const countForm = reactive({
  name: "",
  password: "",
  checkPass: ""
});
const accountFormRef = ref<FormInstance>();
const validatePass = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请输入密码"));
  } else {
    if (countForm.checkPass !== "") {
      if (!accountFormRef.value) return;
      accountFormRef.value.validateField("checkPass", () => null);
    }
    callback();
  }
};
const validatePass2 = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== countForm.password) {
    callback(new Error("两次输入密码不一致！"));
  } else {
    callback();
  }
};
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入用户名称", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { validator: validatePass, trigger: "blur" }
  ],
  checkPass: [
    { required: true, message: "请再次输入密码", trigger: "blur" },
    { validator: validatePass2, trigger: "blur" }
  ]
});
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await modifyUser(countForm.name, {
        user: {
          userName: countForm.name,
          password: countForm.password
        }
      });
      ElMessage({
        message: "修改成功",
        type: "success"
      });
      setTimeout(() => {
        router.push("/login");
      }, 1000);
    }
  });
};
const handleCloseAccount = () => {
  accountFormRef.value?.resetFields();
  countForm.name = "";
  countDialogVisible.value = false;
};
const dialogVisible = ref(false);
const countDialogVisible = ref(false);
// const handleToken = () => {
//   dialogVisible.value = true;
// };
// const handleCountSet = () => {
//   countDialogVisible.value = true;
//   countForm.name = realName;
// };
onMounted(async () => {});
</script>

<style lang="scss" scoped>
* {
  margin: 0;
  padding: 0;
}
.header {
  display: flex;
  width: 100%;
  height: 100%;
  background: #4076f3;
  align-items: center;
  .left {
    flex-grow: 10;
    font-size: 25px;
    color: #ffffff;
    font-weight: 500;
    display: flex;
    justify-content: flex-end;
  }
  .mid {
    flex-grow: 34;
    display: flex;
    margin-left: 30px;
    height: 100%;
    align-items: center;
    img {
      width: 42px;
      height: 42px;
      margin-right: 10px;
    }
    span {
      height: 36px;
      font-size: 24px;
      font-family: Source Han Sans CN, Source Han Sans CN-Bold;
      font-weight: 700;
      text-align: left;
      color: #ffffff;
    }
  }
  .right {
    flex-grow: 2;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-popconfirm__action {
    .el-button.is-text {
      &:hover {
        --el-fill-color-light: unset;
      }
    }
  }
}
.tiems {
  display: flex;
  font-size: 14px;
  color: #ffffff;
  align-items: center;
  justify-content: center;
  .loginOut {
    cursor: pointer;
    margin-left: 15px;
    margin-top: 3px;
  }
}
.line {
  // width: 1px;
  height: 20px;
  opacity: 0.35;
  border-right: 2px solid #9ec5e7;
  margin-left: 20px;
}
.user-avater {
  width: 40px;
  height: 40px;
  margin-left: 20px;
  margin-top: -8px;
}
.userName {
  margin-left: 10px;
  width: 50px;
  height: 24px;
  font-size: 16px;
  font-family: Source Han Sans CN, Source Han Sans CN-Medium;
  font-weight: 500;
  text-align: left;
  color: #ffffff;
  line-height: 22px;
}
.dropdown {
  cursor: pointer;
  width: 100px;
  margin: 0 20px 0 0;
}
.el-dropdown-link {
  display: flex;
  align-items: center;
}

.p-d {
  width: 1px;
  height: 16px;
  opacity: 0.4;
  background: #ecf2fc;
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
</style>
