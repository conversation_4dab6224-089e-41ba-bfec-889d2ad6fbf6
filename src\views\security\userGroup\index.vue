<!--
 * @Description: 
 * @Date: 2023-02-06 11:42:26
 * @Author: GISerZ
 * @LastEditors: GISerZ
 * @LastEditTime: 2023-03-13 15:02:50
-->
<template>
  <div class="custom-content">
    <div class="custom-table-header-row">
      <div class="custom-table-title role-row-width">用户组</div>
      <div class="custom-table-addBlue" @click="userAdd">
        <img src="../../../assets/img/add.png" alt="" />
        <span>新增用户组</span>
      </div>
    </div>
    <div class="custom-table-box">
      <el-form :model="queryform" label-position="top" class="custom-table-search-box">
        <div class="custom-search-item">
          <el-form-item label="用户组名称">
            <el-input class="custom-input" v-model="queryform.name" placeholder="请输入" />
          </el-form-item>
        </div>
        <div class="custom-search-item">
          <el-form-item class="online-form-btns">
            <div class="custom-query-button" @click="queryData">查询</div>
            <div class="custom-reset-button" @click="reset">重置</div>
          </el-form-item>
        </div>
      </el-form>
      <el-table
        :data="selectTable"
        class="custom-table"
        header-row-class-name="custom-header-row"
        header-cell-class-name="custom-header-cell"
        style="width: 96%"
      >
        <el-table-column align="center" width="50">
          <template v-slot="scop">
            {{ scop.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="用户组" />
        <el-table-column prop="users" label="关联用户" show-overflow-tooltip />
        <el-table-column prop="roles" label="关联角色" show-overflow-tooltip />
        <el-table-column label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button text @click="editHandler(scope.row)">编辑</el-button>
            <el-button text class="table-del-btn" @click="delteHandler(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="custom-pagination-box">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 30]"
          :small="small"
          :disabled="disabled"
          :background="background"
          layout="prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog
      :before-close="handleClose"
      :close-on-click-modal="false"
      v-model="dialogVisible"
      :title="dialogTitle"
      width="34%"
    >
      <template #header="{ titleId, titleClass }">
        <div class="custom-dialog-header">
          <img src="../../../assets/img/dialog-icon.png" alt="" />
          <h4 :id="titleId" :class="titleClass">{{ dialogTitle }}</h4>
        </div>
      </template>
      <div class="custom-split-line"></div>
      <el-form
        ref="groupFormRef"
        :model="ruleForm"
        :rules="rules"
        class="demo-ruleForm custom-sub-form"
        :size="formSize"
        label-position="top"
        status-icon
      >
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item label="用户组名称" prop="name">
              <el-input
                :disabled="dialogTitle !== '添加用户组'"
                placeholder="请输入用户组名称"
                v-model="ruleForm.name"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设置用户" prop="userGroup">
              <el-select
                v-model="userValue"
                multiple
                filterable
                collapse-tags
                allow-create
                default-first-option
                class="custom-dialog-select"
                :reserve-keyword="false"
                placeholder="请选择用户"
              >
                <el-option
                  v-for="item in userData"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="32">
          <el-col :span="12">
            <el-form-item label="设置用户角色" prop="userRole">
              <el-select
                v-model="roleValue"
                multiple
                filterable
                collapse-tags
                allow-create
                default-first-option
                class="custom-dialog-select"
                :reserve-keyword="false"
                placeholder="请选择用户角色"
              >
                <el-option
                  v-for="item in roleData"
                  :key="item.key"
                  :label="item.label"
                  :value="item.key"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="custom-dialog-footer">
          <div class="dialog-cancle" @click="resetForm(groupFormRef)">取消</div>
          <div class="dialog-submit" @click="submitForm(groupFormRef)">确定</div>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getRolesList,
  addRoleGroup,
  addUserGroup,
  delUserGroup,
  delRoleGroup
} from "@/api/security/role";
import { getUserList } from "@/api/security/user";
import {
  getGroupsList,
  addGroups,
  delGroups,
  getGroupsUser,
  getGroupsRole
} from "@/api/security/userGroup";
import { inputSearch, setScoped } from "@/utils/basic";
import { ref, reactive, onMounted } from "vue";
import type { FormInstance, FormRules } from "element-plus";
const formSize = ref("default");
const dialogTitle = ref("");
const groupFormRef = ref<FormInstance>();
const ruleForm = reactive({
  name: "",
  describe: "",
  roletype: ""
});
const QueryForm = class {
  // eslint-disable-next-line no-useless-constructor
  constructor(public name = "", public pageNum = 1, public pageSize = 10) {}
};
const searchData: any = ref([]);
const searchDataDeal = ref([]);
const queryform = ref(new QueryForm());
const searchStart = ref(false);
const queryData = () => {
  currentPage.value = 1;
  searchStart.value = true;
  searchData.value = inputSearch(tableData.value, queryform.value.name, "name");
  searchDataDeal.value = fenge(searchData.value, pageSize.value);
  selectTable.value = searchDataDeal.value[currentPage.value - 1];
  total.value = searchData.value.length;
};
const reset = () => {
  currentPage.value = 1;
  searchStart.value = false;
  queryform.value = new QueryForm();
  onloadPage();
};
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入用户名称", trigger: "blur" }]
});
const handleClose: any = (done: () => void) => {
  ruleForm.name = "";
  userValue.value = [];
  roleValue.value = [];
  groupFormRef.value?.resetFields();
  dialogVisible.value = false;
};
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (dialogTitle.value === "添加用户组") {
        await addGroups(ruleForm.name);
        for (let i = 0; i < userValue.value.length; i++) {
          await addUserGroup(userValue.value[i], ruleForm.name);
        }
        for (let i = 0; i < roleValue.value.length; i++) {
          await addRoleGroup(roleValue.value[i], ruleForm.name);
        }
        setScoped(`添加用户组-${ruleForm.name}`);
        ElMessage({
          message: "添加成功",
          type: "success"
        });
      } else {
        const { addUser, removeUser } = defferUser(staticUserValue.value, userValue.value);
        for (let i = 0; i < addUser.length; i++) {
          await addUserGroup(addUser[i], ruleForm.name);
        }
        for (let i = 0; i < removeUser.length; i++) {
          await delUserGroup(removeUser[i], ruleForm.name);
        }
        const roleDel = defferUser(staticRoleValue.value, roleValue.value);
        for (let i = 0; i < roleDel.addUser.length; i++) {
          await addRoleGroup(roleDel.addUser[i], ruleForm.name);
        }
        for (let i = 0; i < roleDel.removeUser.length; i++) {
          await delRoleGroup(roleDel.removeUser[i], ruleForm.name);
        }
        setScoped(`修改用户组-${ruleForm.name}`);
        ElMessage({
          message: "修改成功",
          type: "success"
        });
      }
      handleClose();
      await onloadPage();
      if (queryform.value.name !== "") {
        queryData();
      }
    } else {
      console.log("error submit!", fields);
    }
  });
};
// 修改是判断添加关联还是删除关联
const defferUser = (beforeArray: [], newArray: []) => {
  const deffer = beforeArray.concat(newArray).filter((v, i, arr: any) => {
    return arr.indexOf(v) === arr.lastIndexOf(v);
  });

  const removeList: any = [];
  for (let i = 0; i < beforeArray.length; i++) {
    for (let j = 0; j < deffer.length; j++) {
      if (beforeArray[i] === deffer[j]) {
        removeList.push(beforeArray[i]);
      }
    }
  }
  const addList = deffer.concat(removeList).filter((v, i, arr: any) => {
    return arr.indexOf(v) === arr.lastIndexOf(v);
  });
  return {
    addUser: addList,
    removeUser: removeList
  };
};
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  dialogVisible.value = false;
};
const tableData: any = ref([]);
const dealTableData: any = ref([]);
const selectTable: any = ref([]);
const getAllListData = async () => {
  tableData.value = [];
  dealTableData.value = [];
  selectTable.value = [];
  const data = await getGroupsList();
  tableData.value = data;
  dealTableData.value = fenge(tableData.value, pageSize.value);
  selectTable.value = dealTableData.value[currentPage.value - 1];

  total.value = tableData.value.length;
  const users: any = await getUserList();
  userData.value = generateUserData(users);
  const roles: any = await getRolesList();
  roleData.value = generateRoleData(roles);
};
const fenge = (arr: [], N: number) => {
  const result: any = [];
  for (let i = 0; i < arr.length; i += N) {
    result.push(arr.slice(i, i + N));
  }
  return result;
};
const handleSizeChange = (val: number) => {
  dealTableData.value = fenge(tableData.value, val);
  currentPage.value = 1;
  selectTable.value = dealTableData.value[0];
};
const handleCurrentChange: any = (val: number) => {
  if (!searchStart.value) {
    selectTable.value = dealTableData.value[val - 1];
  } else {
    selectTable.value = searchDataDeal.value[val - 1];
  }
};
const staticUserValue: any = ref([]);
const staticRoleValue: any = ref([]);
const matchGroupUser: any = async (groups: string) => {
  const result: any = await getGroupsUser(groups);
  const newArr: any = [];
  result.users.forEach((item: any) => {
    newArr.push(item.userName);
  });
  userValue.value = newArr;
  staticUserValue.value = userValue.value;
};
const matchGroupRole: any = async (groups: string) => {
  const result: any = await getGroupsRole(groups);
  roleValue.value = result.roles;
  staticRoleValue.value = roleValue.value;
};
//  编辑
const editHandler = async (row: any) => {
  dialogTitle.value = "修改用户组";
  ruleForm.name = row.name;
  await matchGroupUser(row.name);
  await matchGroupRole(row.name);
  dialogVisible.value = true;
};
//  删除
const delteHandler = async (row: any) => {
  ElMessageBox.confirm("确定要删除当前用户组吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "custom-messagebox"
  }).then(async () => {
    await delGroups(row.name);
    setScoped(`删除用户组-${row.name}`);
    ElMessage({
      message: "删除成功",
      type: "success"
    });
    await onloadPage();
    if (queryform.value.name !== "") {
      queryData();
    }
  });
};
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const small = ref(false);
const background = ref("#ffffff");
const disabled = ref(false);
const dialogVisible = ref(false);
const userAdd = () => {
  dialogTitle.value = "添加用户组";
  ruleForm.name = "";
  userValue.value = [];
  roleValue.value = [];
  dialogVisible.value = true;
};

interface Option {
  key: number | string;
  label: string;
  initial: string;
}
// 构造User的trans
const generateUserData = (states: []) => {
  const data: Option[] = [];
  states.forEach((item: any, index: number) => {
    data.push({
      label: item.name,
      key: item.name,
      initial: item.name
    });
  });
  return data;
};
// 构造Groups的trans
const generateRoleData = (states: []) => {
  const data: Option[] = [];
  states.forEach((item: any, index: number) => {
    data.push({
      label: item.name,
      key: item.name,
      initial: item.name
    });
  });
  return data;
};
const userData = ref<Option[]>([]);
const roleData = ref<Option[]>([]);
const userValue: any = ref([]);
const roleValue: any = ref([]);
const onloadPage = async () => {
  await getAllListData();
  // dealTableData.value = fenge(tableData.value, pageSize.value);
};

onMounted(async () => {
  onloadPage();
});
</script>
<style lang="scss" scoped>
.allData {
  width: 1300px;
  height: 100%;
}
.top-operate {
  display: flex;
  height: 50px;
  width: 100%;
  justify-content: flex-end;
}
.pagination-box {
  margin-top: 10px;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: flex-end;
}
::v-deep(.el-transfer__buttons) {
  padding: 0 10px;
  width: 80px;
  span {
    font-size: 12px;
    color: #000000;
    font-weight: 400;
  }
  .el-transfer__button:nth-child(2) {
    margin: 10px 0 0 0px;
    font-size: 14px;
  }
}
::v-deep(.el-dialog__body) {
  padding: 10px 30px;
}
::v-deep(.el-transfer-panel__body) {
  height: 130px;
}
::v-deep(.el-checkbox__label) {
  font-size: 12px;
}
::v-deep(.el-dialog__header) {
  display: flex;
}
::v-deep(.el-transfer-panel__list) {
  height: 130px;
}
.role-row-width {
  width: 90%;
}
</style>
